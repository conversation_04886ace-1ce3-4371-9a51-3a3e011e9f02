# ICT Trading Bot - Analizör Konfigürasyon Template
# Bu dosyayı .env olarak kopyalayın ve değerleri ihtiyacınıza göre ayarlayın

# =================
# ANA AYARLAR
# =================
API_KEY=your_bybit_api_key_here
API_SECRET=your_bybit_api_secret_here
SYMBOLS=BTCUSDT,ETHUSDT,SOLUSDT
TIMEFRAMES=240
MAX_CANDLES=230
ANALYSIS_INTERVAL=480

# =================
# TELEGRAM AYARLARI  
# =================
TELEGRAM_ENABLED=false
TELEGRAM_BOT_TOKEN=your_bot_token_here
TELEGRAM_CHAT_ID=your_chat_id_here

# =================
# MARKET STRUCTURE ANALYSIS
# =================
# MSS (Market Structure Shift) hassasiyeti - Küçük değer = daha hassas
MSS_SENSITIVITY=5

# MSB (Market Structure Break) hassasiyeti - Küçük değer = daha hassas  
MSB_SENSITIVITY=15

# =================
# DISPLACEMENT ANALYSIS
# =================
# Standart sapma hesaplaması için mum sayısı
DISPLACEMENT_STD_LENGTH=100

# Displacement threshold çarpanı - Küçük değer = daha hassas
DISPLACEMENT_STD_MULTIPLIER=4.0

# =================
# SUPERTREND ANALYSIS
# =================
# ATR periyodu
SUPERTREND_ATR_PERIOD=10

# ATR çarpanı - Küçük değer = daha hassas sinyal
SUPERTREND_ATR_MULTIPLIER=3.0

# RMA kullanımı (true/false)
SUPERTREND_USE_RMA=true

# =================
# NPOC (Volume Profile) ANALYSIS
# =================
# Volume profile çözünürlüğü
NPOC_RESOLUTION=30

# Geriye bakış periyodu
NPOC_LOOKBACK_PERIODS=5

# =================
# EXTERNAL LIQUIDITY ANALYSIS
# =================
# Swing window büyüklüğü
EXT_LIQ_SWING_WINDOW=20

# Proximity threshold (%) - Liquidity hunt toleransı
EXT_LIQ_PROXIMITY_PCT=0.2

# Minimum strength threshold
EXT_LIQ_STRENGTH_THRESHOLD=0.3

# =================
# OPENING GAP ANALYSIS
# =================
# Minimum gap size (%) - Küçük değer = daha küçük gap'leri yakalar
OPENING_GAP_MIN_SIZE_PCT=0.01

# =================
# LIQSFP ANALYSIS
# =================
# Hunt tolerance (%) - Küçük değer = daha kesin hunt gerekir
LIQSFP_TOLERANCE_PCT=0.0

# =================
# FIBONACCI ANALYSIS
# =================
# Fibonacci retracement seviyeleri (virgülle ayrılmış)
FIBONACCI_LEVELS=0.236,0.382,0.5,0.618,0.786

# Seviye tolerance (%)
FIBONACCI_TOLERANCE_PCT=0.5

# =================
# PREMIUM/DISCOUNT ANALYSIS
# =================
# Premium threshold (%) - Bu değerin üzerinde premium bölge
PD_PREMIUM_THRESHOLD=60.0

# Discount threshold (%) - Bu değerin altında discount bölge
PD_DISCOUNT_THRESHOLD=40.0

# Equilibrium tolerance (%) - Bu aralıkta equilibrium
PD_EQUILIBRIUM_TOLERANCE=5.0

# =================
# FVG (Fair Value Gap) ANALYSIS
# =================
# Minimum gap size (%)
FVG_MIN_GAP_PCT=0.1

# Maximum fill percentage (%) - Bu kadar dolduğunda FVG invalid
FVG_MAX_FILL_PCT=50.0

# =================
# ORDER BLOCK ANALYSIS
# =================
# Minimum volume ratio - Bu katın üzerinde volume gerekir
OB_MIN_VOLUME_RATIO=1.5

# Maximum lookback candles
OB_MAX_LOOKBACK=50

# =================
# BREAKER BLOCK ANALYSIS
# =================
# Minimum strength
BREAKER_MIN_STRENGTH=1.0

# Confirmation candles
BREAKER_CONFIRMATION_CANDLES=3

# =================
# RİSK YÖNETİMİ
# =================
RM_CAPITAL=10000
RM_DEFAULT_RISK_PCT=1.0
RM_LEVERAGE=10
RM_MAKER_FEE_PCT=0.02
RM_TAKER_FEE_PCT=0.05

# =================
# TİME AYARLARI
# =================
ENTRY_TIMEOUT_HOURS=20
NPOC_UPDATE_INTERVAL_SECONDS=14400
SFP_UPDATE_INTERVAL_SECONDS=3600
FVRP_UPDATE_INTERVAL=86400

# =================
# SESSION YÖNETİMİ
# =================
SESSIONS_ENABLED=false
SESSIONS_TRADE_DAYS=0,1,2,3,4
SESS_LONDON_START=08:00
SESS_LONDON_END=17:00
SESS_NY_START=13:00
SESS_NY_END=22:00

# =================
# PARAMETRE AYARLAMA REHBERİ
# =================
# 
# HASSAS AYARLAR (Daha fazla sinyal):
# - MSS_SENSITIVITY=3
# - DISPLACEMENT_STD_MULTIPLIER=2.5
# - SUPERTREND_ATR_MULTIPLIER=2.0
# - LIQSFP_TOLERANCE_PCT=0.1
#
# KONSERVATIF AYARLAR (Daha az, kaliteli sinyal):
# - MSS_SENSITIVITY=8
# - DISPLACEMENT_STD_MULTIPLIER=5.0
# - SUPERTREND_ATR_MULTIPLIER=4.0
# - LIQSFP_TOLERANCE_PCT=0.0
#
# VOLATILE MARKET AYARLARI:
# - DISPLACEMENT_STD_LENGTH=150
# - NPOC_LOOKBACK_PERIODS=7
# - PD_PREMIUM_THRESHOLD=70.0
#
# RANGE MARKET AYARLARI:
# - MSS_SENSITIVITY=7
# - FVG_MIN_GAP_PCT=0.15
# - OB_MIN_VOLUME_RATIO=2.0
