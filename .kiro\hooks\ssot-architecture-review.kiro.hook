{"enabled": true, "name": "SSoT Architecture Review", "description": "Monitors key files for violations of Single Source of Truth and analyzer-consumer separation principles, providing architectural guidance when changes are detected", "version": "1", "when": {"type": "userTriggered", "patterns": ["main.py", "scoring_system.py", "smart_entry_strategy.py", "fibonacci_analyzer.py", "order_block_analyzer.py", "ote_confluence_analyzer.py", "timeframe_levels_analyzer.py", "breaker_block_analyzer.py", "confluence_aggregator.py", "signal_orchestrator.py"]}, "then": {"type": "askAgent", "prompt": "Analyze the changed files for SSoT and analyzer-consumer separation violations. Check for:\n\n1. **scoring_system.py violations**: Look for _check_*_signal methods that perform analysis instead of consuming pre-analyzed data from all_symbol_data. These should be moved to appropriate analyzer modules.\n\n2. **smart_entry_strategy.py violations**: Check for OTE/POI confluence analysis (_find_ote_poi_confluence) that should be in ote_confluence_analyzer.py instead.\n\n3. **main.py violations**: Ensure it only orchestrates analyzers and doesn't perform direct analysis (like <PERSON><PERSON><PERSON><PERSON> calculations).\n\n4. **Cross-module analysis**: Verify that consumer modules (scoring_system, smart_entry_strategy, signal_orchestrator) only read from all_symbol_data and don't perform new analyses.\n\n5. **Analyzer purity**: Confirm analyzer modules only produce structured analysis results and don't make trading decisions.\n\nProvide specific Turkish recommendations for moving analysis logic to correct modules and updating the main.py orchestration flow. Focus on maintaining the analyzer-consumer separation where analyzers produce data and consumers use pre-analyzed results."}}