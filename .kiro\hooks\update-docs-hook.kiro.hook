{"enabled": true, "name": "Documentation Updater", "description": "Automatically updates PROJECT_ARCHITECTURE.md and README.md files when Python files or configuration files in the project are modified", "version": "1", "when": {"type": "fileEdited", "patterns": ["*.py", "requirements.txt", "docker-compose.yml", "Dockerfile", ".env.example"]}, "then": {"type": "askAgent", "prompt": "PROJECT_ARCHITECTURE.md ve README.md dosyalarını güncelleyin. Proje yapısındaki değişiklikleri analiz edin ve dokümantasyonu bu değişikliklere göre güncel tutun. Python dosyalarındaki yeni modüller, sınıflar ve fonksiyonları belgelendirin. Konfigürasyon dosyalarındaki değişiklikleri yansıtın."}}