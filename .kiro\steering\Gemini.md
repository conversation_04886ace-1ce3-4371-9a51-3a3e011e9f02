- Always respond in Turkish.
- Write concise, technical responses with accurate Python examples.
- Follow PEP 8 style guidelines for Python code.
- Implement proper error logging and user-friendly error messages.
- Don’t be STUPID: GRASP SOLID! Stay in line with the philosophy.
- Use the Dependency Injection pattern.
- Try to solve the problem from its source, do not create temporary solutions.
- Always clarify the data contract between modules.
- Make the code more readable and clarify expectations between modules
- All analyses must comply with the ICT (Inner Circle Trader) methodology.
- Do not test whether the system is working or not
- You should not test the changes. You should not run the program to test the changes.