# alert_manager.py

from loguru import logger
from typing import Dict, Any, Optional
import pandas as pd
from utils import format_price_standard

# TelegramNotifier'ı import etmeye çalış, yoksa None olarak ayarla
try:
    from telegram_notifier import TelegramNotifier
except ImportError:
    TelegramNotifier = None

# ChartGenerator'ı import etmeye çalış
try:
    from chart_generator import ChartGenerator
except ImportError:
    ChartGenerator = None


class AlertManager:
    """
    Farklı olay türleri için uyarıları formatlar ve ilgili kanallara gönderir.
    (örn. Telegram, TradingView Webhook, Discord vb.)
    """

    def __init__(self, config: Dict[str, Any]):
        """
        AlertManager'ı başlatır.

        Args:
            config (Dict[str, Any]): Uyarı ayarlarını içeren konfigürasyon.
        """
        self.config = config
        self.telegram_enabled = config.get('telegram_enabled', False) and TelegramNotifier is not None

        if self.telegram_enabled:
            self.telegram_notifier = TelegramNotifier(
                bot_token=config.get('telegram_bot_token', ''),
                chat_id=config.get('telegram_chat_id', '')
            )
            logger.info("AlertManager başlatıldı - Telegram bildirimleri AKTİF.")
        else:
            self.telegram_notifier = None
            logger.warning("AlertManager başlatıldı - Telegram bildirimleri DEVRE DIŞI.")
            if TelegramNotifier is None:
                logger.warning("-> 'telegram_notifier' modülü bulunamadı.")
            if not config.get('telegram_enabled', False):
                 logger.warning("-> Konfigürasyonda 'telegram_enabled' false olarak ayarlanmış.")
        
        # ChartGenerator'ı başlat
        if ChartGenerator:
            self.chart_generator = ChartGenerator(chart_dir=config.get("chart_dir", "charts"))
            logger.info("ChartGenerator başarıyla entegre edildi.")
        else:
            self.chart_generator = None
            logger.warning("ChartGenerator modülü bulunamadığı için grafik oluşturma devre dışı.")


    def send_alert(self, message: str, alert_type: str = "info", chart_path: Optional[str] = None):
        """
        Formatlanmış bir mesajı ve isteğe bağlı olarak bir grafiği yapılandırılmış kanallara gönderir.

        Args:
            message (str): Gönderilecek mesaj.
            alert_type (str): Uyarının türü.
            chart_path (Optional[str]): Gönderilecek grafiğin dosya yolu.
        """
        logger.debug(f"Uyarı gönderiliyor (tip: {alert_type}): {message}")

        if self.telegram_enabled and self.telegram_notifier:
            try:
                if chart_path:
                    self.telegram_notifier.send_photo(photo_path=chart_path, caption=message)
                    logger.info(f"Telegram'a '{alert_type}' tipinde grafikli uyarı gönderildi.")
                else:
                    self.telegram_notifier.send_message(message, parse_mode=None)
                    logger.info(f"Telegram'a '{alert_type}' tipinde metin uyarısı gönderildi.")
            except Exception as e:
                logger.error(f"Telegram'a uyarı gönderilirken hata oluştu: {e}")

    def format_trade_signal_alert(self, signal: Dict[str, Any]) -> str:
        """
        Gelişmiş ve detaylı bir ticaret sinyali için bildirim metni oluşturur.

        Args:
            signal (Dict[str, Any]): Sinyal bilgilerini içeren sözlük.

        Returns:
            str: Telegram için formatlanmış bildirim metni.
        """
        try:
            # --- Sinyal Verilerini Güvenli Bir Şekilde Al ve FORMATLA ---
            symbol = signal.get('symbol', 'N/A')
            direction = signal.get('direction', 'N/A').upper()
            entry_price = signal.get('primary_entry', 0)
            stop_loss = signal.get('stop_loss', 0)
            
            # Analiz ve Konfluens Bilgileri
            main_signal = signal.get('type', 'N/A')
            strategy = signal.get('strategy', main_signal)
            signal_priority = signal.get('priority', 'N/A')
            confluence_score = signal.get('confluence_score', 0.0)
            htf_trend = signal.get('htf_trend', 'N/A').upper()
            htf_strength = signal.get('htf_trend_strength', 0.0)
            
            # Zamanlama ve Seans Bilgileri
            active_killzone = signal.get('active_killzone', {})
            killzone = active_killzone.get('name', 'Aktif Değil') if isinstance(active_killzone, dict) else 'Aktif Değil'
            overlap = signal.get('active_overlap', 'Aktif Değil')
            impact_data = signal.get('impact', {})
            impact_level = impact_data.get('level', 'MEDIUM') if isinstance(impact_data, dict) else 'MEDIUM'
            impact_score = impact_data.get('score', 50) if isinstance(impact_data, dict) else 50

            # Piyasa Durumu (Sinyal objesinden alınır)
            price_info = signal.get('price_info', {})
            current_price = price_info.get('current_price', 0)
            daily_change_pct = price_info.get('daily_change_pct', 0.0)
            
            market_context = signal.get('market_context', {})
            trend_1h = market_context.get('1h_trend', 'N/A')
            trend_4h = market_context.get('4h_trend', 'N/A')
            htf_regime = market_context.get('htf_regime', 'N/A')
            htf_regime_state = market_context.get('htf_regime_state', 'N/A')
            
            liquidity_context = signal.get('liquidity_context', {})
            nearest_bsl = liquidity_context.get('nearest_bsl', {'price': 0, 'distance_pct': 0})
            nearest_ssl = liquidity_context.get('nearest_ssl', {'price': 0, 'distance_pct': 0})
            eqh_count = liquidity_context.get('eqh_count', 0)
            eql_count = liquidity_context.get('eql_count', 0)
            ifvg_summary = liquidity_context.get('ifvg_summary', 'N/A')

            # --- Mesajı Oluşturma ---
            direction_emoji = "🔴⬇️" if direction == 'BEARISH' else "🟢⬆️"
            header = f"📊 {symbol} | {direction_emoji} {direction}\n"
            separator = "\n━━━━━━━━━━━━━━━━\n"

            # Giriş, SL, TP (format_price_standard kullanarak)
            trade_info = f"├─ 💰 Giriş Fiyatı: {format_price_standard(entry_price)}\n"
            trade_info += f"├─ 🛡️ Stop Loss: {format_price_standard(stop_loss)}\n"
            
            # Individual TP seviyelerini kontrol et ve formatla
            tp1 = signal.get('tp1')
            tp1_5 = signal.get('tp1_5')  
            tp2 = signal.get('tp2')
            tp3 = signal.get('tp3')
            
            if tp1:
                trade_info += f"├─ 💵 TP1 (1R): {format_price_standard(tp1)}\n"
            if tp1_5:
                trade_info += f"├─ 💵 TP1.5 (1.5R): {format_price_standard(tp1_5)}\n"
            if tp2:
                trade_info += f"├─ 💵 TP2 (2R): {format_price_standard(tp2)}\n"
            if tp3:
                trade_info += f"├─ 💵 TP3 (3R): {format_price_standard(tp3)}\n"

            # Risk/Reward Oranı
            risk_reward = signal.get('risk_reward_ratio') or signal.get('risk_reward')
            if risk_reward and risk_reward > 0:
                trade_info += f"├─ 📈 Risk/Reward: 1:{risk_reward:.2f}\n"

            # HTF Uyumsuzluk Uyarısı
            confluence_warning = ""
            if htf_trend != 'N/A' and direction != 'N/A' and htf_trend != direction:
                confluence_warning = f"└─ ⚠️ Confluence Uyumsuz: HTF {htf_trend} ↔ Sinyal {direction}\n"
            
            # Analiz Detayları
            analysis_details = f"├─ 📊 Ana Sinyal: {main_signal} ({direction})\n"
            analysis_details += f"├─ 🔍 Sinyal Önceliği: {signal_priority}\n"
            analysis_details += f"├─ 🎯 Confluence Skoru: {confluence_score:.1f}/100\n"
            htf_trend_str = f"{htf_trend} ({htf_strength:.2f})" if htf_trend != 'N/A' else "Belirlenmedi"
            analysis_details += f"├─ 📈 HTF Yönü: {htf_trend_str}\n"

            analysis_details += f"├─ 🔥 Aktif Killzone: {killzone}\n"
            analysis_details += f"├─ ⏰ Aktif Overlap: {overlap}\n"

            impact_str = f"{impact_level.upper()} ({impact_score}/100)"
            analysis_details += f"├─ ⚡ Impact Seviyesi: {impact_str}\n"
            analysis_details += f"├─ 💼 Giriş Stratejisi: {strategy}\n"
            analysis_details += confluence_warning # Uyarıyı en sona ekle

            # Piyasa Durumu Özeti
            market_summary = f"💰 Fiyat: {format_price_standard(current_price)} ({daily_change_pct:+.2f}% günlük)\n"
            market_summary += f"├─ 📈 1H Trend: {trend_1h}\n"
            market_summary += f"├─ 📈 4H Trend: {trend_4h}\n"
            market_summary += f"├─ 🎯 HTF Rejim: {htf_regime} ({htf_regime_state})\n\n"
            
            bsl_str = f"{format_price_standard(nearest_bsl['price'])} ({nearest_bsl.get('distance_pct', 0):+.2f}%)" if nearest_bsl and nearest_bsl.get('price') else "N/A"
            ssl_str = f"{format_price_standard(nearest_ssl['price'])} ({nearest_ssl.get('distance_pct', 0):+.2f}%)" if nearest_ssl and nearest_ssl.get('price') else "N/A"
            
            market_summary += f"├─ 🔴 En Yakın BSL: {bsl_str}\n"
            market_summary += f"├─ 🟢 En Yakın SSL: {ssl_str}\n"
            market_summary += f"├─ 🔵 Likidite Yapısı: EQH: {eqh_count}, EQL: {eql_count}\n"
            market_summary += f"├─ 🔄 Inverse FVG: {ifvg_summary}\n"

            # Alt Bilgi
            footer_separator = "\n━━━━━━━━━━━━━━━━\n"
            footer = f"\nℹ️ Not: Bu strateji HTF trend ile ters yönde işlem alabilir\n"
            footer += "ℹ️ Lütfen kendi analizinizi yapın. Bu bir yatırım tavsiyesi değildir."

            # Tüm Parçaları Birleştir
            full_message = (
                header
                + separator
                + trade_info
                + separator
                + analysis_details
                + footer_separator
                + market_summary
                + footer_separator
                + footer
            )
            return full_message

        except Exception as e:
            logger.error(f"Sinyal mesajı formatlanırken hata oluştu: {e}", exc_info=True)
            # Hata durumunda basit bir mesaj döndür
            symbol = signal.get('symbol', 'N/A')
            direction = signal.get('direction', 'N/A')
            return f"🚨 HATA: {symbol} için {direction} sinyali formatlanamadı. Lütfen logları kontrol edin."

    def format_invalidation_alert(self, trade_idea: Dict[str, Any], reason: str) -> str:
        """
        Geçersiz kılınan bir ticaret fikri için uyarı metni oluşturur.

        Args:
            trade_idea (Dict[str, Any]): Geçersiz kılınan ticaret fikri.
            reason (str): Geçersiz kılma nedeni (örn. 'Karşıt SFP oluştu').

        Returns:
            str: Formatlanmış uyarı metni.
        """
        symbol = trade_idea.get('symbol', 'N/A')
        direction = trade_idea.get('direction', 'N/A').upper()
        entry = trade_idea.get('entry_price', 0)

        message = (
            f"🟡 **İşlem Fikri İptal Edildi: {symbol}** 🟡\n\n"
            f"**Yön:** `{direction}`\n"
            f"**Planlanan Giriş:** `{format_price_standard(entry)}`\n\n"
            f"**İptal Nedeni:** `{reason}`\n\n"
            f"Piyasa koşulları değiştiği için bekleyen emir iptal edildi."
        )
        return message