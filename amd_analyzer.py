# amd_analyzer.py

from datetime import datetime
from typing import Dict, List, Optional, Any
import pandas as pd
from loguru import logger

from pivot_analyzer import PivotAnalyzer
from market_structure_analyzer import MarketStructureAnalyzer


class AmdAnalyzer:
    """
    AMD (Accumulation, Manipulation, Distribution) modelini analiz eder.
    Wyckoff/ICT prensiplerine dayanarak konsolidasyon aralıklarını,
    manipülasyonları (Spring/UTAD) ve potansiyel genişleme fazlarını tespit eder.
    """

    def __init__(self, pivot_analyzer: PivotAnalyzer):
        self.pivot_analyzer = pivot_analyzer
        logger.info("AmdAnalyzer başlatıldı.")

    def analyze(self, candles: pd.DataFrame, structure_analysis: Dict[str, Any], lookback_period: int = 100) -> List[Dict[str, Any]]:
        """
        Verilen mum verilerinde AMD kurulumlarını arar.

        Args:
            candles: <PERSON><PERSON>z edilecek mum verileri.
            structure_analysis: Önceden hesaplanmış piyasa yapısı analizi.
            lookback_period: Konsolidasyon aralığını aramak için kullanılacak mum sayısı.

        Returns:
            Tespit edilen AMD sinyallerinin listesi.
        """
        signals = []
        if candles.empty or len(candles) < lookback_period:
            return []

        analysis_candles = candles.tail(lookback_period)

        # 1. Konsolidasyon Aralığı Tespiti
        pivots = self.pivot_analyzer.find_pivots(analysis_candles)
        if len(pivots) < 4: # En az iki high ve iki low olmalı
            return []

        highs = [p['price'] for p in pivots if p['type'] == 'high']
        lows = [p['price'] for p in pivots if p['type'] == 'low']

        if not highs or not lows or len(highs) < 2 or len(lows) < 2:
            return signals

        range_high = max(highs)
        range_low = min(lows)
        range_size = range_high - range_low

        # Eğer range çok genişse, bu bir konsolidasyon değildir.
        if range_size == 0 or range_size > (analysis_candles['close'].mean() * 0.10): # %10'dan büyükse atla
            return signals

        logger.debug(f"Potansiyel konsolidasyon aralığı tespit edildi: {range_low:.4f} - {range_high:.4f}")

        # 2. Manipülasyon (Spring / UTAD) Tespiti
        # Son 5 mumu kontrol et
        last_candle = analysis_candles.iloc[-1]
        recent_low = analysis_candles.tail(5)['low'].min()
        recent_high = analysis_candles.tail(5)['high'].max()

        # Spring (Bullish)
        # Son 5 mum içinde fiyat aralığın altına düşmüş ve son mum tekrar içine girmiş mi?
        if recent_low < range_low and last_candle['close'] > range_low:
            # Teyit: Fiyat aralığa geri döndükten sonra bir MSS var mı?
            # Önceden hesaplanmış structure_analysis'i kullan
            recent_breaks = [
                b for b in structure_analysis.get('breaks', []) 
                if b['direction'] == 'bullish' and pd.to_datetime(b['timestamp']) > (datetime.now() - pd.Timedelta(hours=4))
            ]
            if recent_breaks:
                signal = self._create_signal('bull', range_low, range_high, 'Spring')
                signals.append(signal)
                logger.success(f"BULLISH AMD (Spring) sinyali bulundu: {signal}")

        # UTAD (Bearish)
        # Son 5 mum içinde fiyat aralığın üstüne çıkmış ve son mum tekrar içine girmiş mi?
        if recent_high > range_high and last_candle['close'] < range_high:
            # Teyit: Fiyat aralığa geri döndükten sonra bir MSS var mı?
            recent_breaks = [
                b for b in structure_analysis.get('breaks', []) 
                if b['direction'] == 'bearish' and pd.to_datetime(b['timestamp']) > (datetime.now() - pd.Timedelta(hours=4))
            ]
            if recent_breaks:
                signal = self._create_signal('bear', range_low, range_high, 'UTAD')
                signals.append(signal)
                logger.success(f"BEARISH AMD (UTAD) sinyali bulundu: {signal}")

        return signals


    def _create_signal(self, direction: str, range_low: float, range_high: float, manip_type: str) -> Dict[str, Any]:
        """Sinyal sözlüğü oluşturur."""
        if direction == 'bull':
            entry_price = range_low # Aralığın altını yeniden test
            reason = f"Birikim aralığı sonrası Spring manipülasyonu ve aralığa geri dönüş."
            stop_loss = range_low * 0.995 # %0.5 tampon
        else:
            entry_price = range_high # Aralığın üstünü yeniden test
            reason = f"Dağıtım aralığı sonrası UTAD manipülasyonu ve aralığa geri dönüş."
            stop_loss = range_high * 1.005 # %0.5 tampon

        return {
            'type': f'AMD_{manip_type.upper()}',
            'direction': direction,
            'price': entry_price,
            'confidence': 0.85, # Yüksek güvenilirlikli model
            'reason': reason,
            'stop_loss': stop_loss, # Öneri SL
            'details': {
                'range_high': range_high,
                'range_low': range_low,
                'manipulation_type': manip_type
            }
        }