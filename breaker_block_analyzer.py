import pandas as pd
from loguru import logger
from typing import List, Dict, Any

class BreakerBlockAnalyzer:
    """
    ICT konseptlerine uygun olarak Breaker Block'ları tespit eder ve bu block'ların
    yeniden test edilmesini (retest) analiz eder.
    
    - Bullish Breaker: <PERSON><PERSON><PERSON><PERSON><PERSON> trendinde bir swing low'un altına inilip (likidite alınır),
      sonrasında bu hareketin başladığı swing high'ın kırılmasıyla oluşur. Başarısız
      olan bu düşüş hareketinden önceki son düşüş mumu Breaker Block'tur.
    - Bearish Breaker: Yükseliş trendinde bir swing high'ın üstüne çıkılıp,
      sonrasında bu hareketin başladığı swing low'un kırılmasıyla oluşur.
    """

    def __init__(self, retest_config: Dict[str, Any] = None):
        """
        Analyzer'ı başlatır.
        
        Args:
            retest_config (Dict[str, Any], optional): Retest analizi için konfigürasyon.
        """
        self.config = retest_config or {}
        self.retest_lookback = self.config.get('retest_lookback', 10) # Son 10 mumu kontrol et
        self.proximity_tolerance_pct = self.config.get('proximity_tolerance_pct', 0.05) # %0.05 tolerans

    def find_breaker_blocks(self, candles: pd.DataFrame, pivots: list) -> list:
        """
        Verilen pivot noktası dizilimlerine göre Breaker Block'ları bulur.
        Bir Breaker için L-H-LL (Bearish) veya H-L-HH (Bullish) dizilimi aranır.

        Args:
            candles (pd.DataFrame): Mum verileri.
            pivots (list): `pivot_analyzer` tarafından üretilen swing noktaları.

        Returns:
            list: Tespit edilen Breaker Block'ların listesi.
        """
        breaker_blocks = []
        if len(pivots) < 3:
            return breaker_blocks

        # Mumları zaman damgasına göre indeksle
        if not isinstance(candles.index, pd.DatetimeIndex):
            candles_indexed = candles.set_index('timestamp')
        else:
            candles_indexed = candles

        for i in range(len(pivots) - 2):
            p1 = pivots[i]
            p2 = pivots[i+1]
            p3 = pivots[i+2]

            # Bearish Breaker: High -> Low -> Lower High (H-L-LH)
            # ve p3'ün p1'in altına indiği durum.
            if p1.get('type') == 'high' and p2.get('type') == 'low' and p3.get('type') == 'high' and p3['price'] < p1['price']:
                # p1 ve p2 arasındaki son YÜKSELİŞ mumunu bul (Bearish Breaker)
                relevant_candles = candles_indexed.loc[p1['timestamp']:p2['timestamp']]
                up_candles = relevant_candles[relevant_candles['close'] > relevant_candles['open']]
                if not up_candles.empty:
                    breaker_candle = up_candles.iloc[-1]
                    breaker_blocks.append(self._create_breaker("bearish", breaker_candle))

            # Bullish Breaker: Low -> High -> Higher Low (L-H-HL)
            # ve p3'ün p1'in üstüne çıktığı durum
            if p1.get('type') == 'low' and p2.get('type') == 'high' and p3.get('type') == 'low' and p3['price'] > p1['price']:
                 # p1 ve p2 arasındaki son DÜŞÜŞ mumunu bul (Bullish Breaker)
                relevant_candles = candles_indexed.loc[p1['timestamp']:p2['timestamp']]
                down_candles = relevant_candles[relevant_candles['close'] < relevant_candles['open']]
                if not down_candles.empty:
                    breaker_candle = down_candles.iloc[-1]
                    breaker_blocks.append(self._create_breaker("bullish", breaker_candle))

        if breaker_blocks:
            logger.info(f"{len(breaker_blocks)} adet Breaker Block tespit edildi.")
        return breaker_blocks

    def analyze_retest(self, candles: pd.DataFrame, breaker_blocks: List[Dict[str, Any]]) -> Dict[str, Any]:
        """
        Tespit edilen Breaker Block'ların yeniden test edilip edilmediğini analiz eder.

        Args:
            candles (pd.DataFrame): Analiz için kullanılacak mum verileri.
            breaker_blocks (List[Dict[str, Any]]): `find_breaker_blocks` tarafından bulunan block'lar.

        Returns:
            Dict[str, Any]: Retest sinyallerini ve özet bilgileri içeren bir sözlük.
        """
        retest_signals = []
        if not breaker_blocks:
            return {"signals": [], "summary": "No breaker blocks to analyze."}

        recent_candles = candles.tail(self.retest_lookback)

        for block in breaker_blocks:
            block_top = block['top']
            block_bottom = block['bottom']
            block_direction = 'bullish' if 'bullish' in block['type'] else 'bearish'
            
            for _, candle in recent_candles.iterrows():
                # Mumun block'tan sonra oluştuğundan emin ol
                if candle['timestamp'] <= block['timestamp']:
                    continue

                # Fiyatın block bölgesine girip girmediğini kontrol et
                retested = False
                if block_direction == 'bullish' and candle['low'] <= block_top:
                    retested = True
                elif block_direction == 'bearish' and candle['high'] >= block_bottom:
                    retested = True
                
                if retested:
                    # Tepkiyi doğrula (örneğin, bullish retest sonrası yeşil mum)
                    reaction_confirmed = False
                    if block_direction == 'bullish' and candle['close'] > candle['open']:
                        reaction_confirmed = True
                    elif block_direction == 'bearish' and candle['close'] < candle['open']:
                        reaction_confirmed = True

                    if reaction_confirmed:
                        signal = {
                            "type": f"{block_direction.upper()}_BREAKER_RETEST",
                            "symbol": block.get("symbol", "N/A"),
                            "timestamp": candle['timestamp'],
                            "retest_price": candle['close'],
                            "breaker_block": block,
                            "confidence": self._calculate_retest_confidence(block, candle, candles)
                        }
                        retest_signals.append(signal)
                        # Bir block için birden fazla sinyal üretmemek için döngüden çık
                        break 
        
        if retest_signals:
            logger.success(f"Found {len(retest_signals)} breaker block retest signals.")

        return {
            "signals": retest_signals,
            "summary": f"Analyzed {len(breaker_blocks)} blocks, found {len(retest_signals)} retests."
        }

    def _calculate_retest_confidence(self, block: Dict[str, Any], candle: pd.Series, candles: pd.DataFrame) -> float:
        """Retest sinyalinin güvenilirliğini hesaplar."""
        confidence = 70.0  # Base confidence
        
        # Hacim onayı
        if candle['volume'] > candles['volume'].rolling(20).mean().iloc[-1] * 1.5:
            confidence += 15.0
            
        # Mumun fitilinin uzunluğu
        if block['direction'] == 'bullish':
            wick_size = candle['open'] - candle['low']
            if wick_size > (candle['high'] - candle['low']) * 0.5: # Uzun alt fitil
                confidence += 10.0
        else: # Bearish
            wick_size = candle['high'] - candle['open']
            if wick_size > (candle['high'] - candle['low']) * 0.5: # Uzun üst fitil
                confidence += 10.0

        return min(confidence, 100.0)

    def _create_breaker(self, direction: str, candle: pd.Series) -> dict:
        """Yardımcı fonksiyon: Breaker Block sözlüğü oluşturur."""
        return {
            'type': f'{direction}_breaker',
            'direction': direction,
            'timestamp': candle.name,
            'top': float(candle['high']),
            'bottom': float(candle['low']),
        }