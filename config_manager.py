import os
import json
from typing import Dict, Any, Optional
from loguru import logger

def load_config(config_file: Optional[str] = None) -> Dict[str, Any]:
    """
    Konfigürasyon dosyasından veya çevre değişkenlerinden ayarları yükler
    
    Args:
        config_file: İsteğe bağlı konfigürasyon dosyası yolu
        
    Returns:
        Dict: Konfigürasyon ayarları
    """
    try:
        config = {
            "api_key": os.getenv("API_KEY", ""),
            "api_secret": os.getenv("API_SECRET", ""),
            "telegram_enabled": os.getenv("TELEGRAM_ENABLED", "false").lower() == "true",
            "telegram_bot_token": os.getenv("TELEGRAM_BOT_TOKEN", ""),
            "telegram_chat_id": os.getenv("TELEGRAM_CHAT_ID", ""),
            "log_level": os.getenv("LOG_LEVEL", "INFO"),
            "symbols": os.getenv("SYMBOLS", "BTCUSDT,ETHUSDT,SOLUSDT").split(","),
            "timeframes": os.getenv("TIMEFRAMES", "240").split(","),        "max_candles": int(os.getenv("MAX_CANDLES", "230")),
            "analysis_interval": int(os.getenv("ANALYSIS_INTERVAL", "480")),
            "npoc_update_interval_seconds": int(os.getenv("NPOC_UPDATE_INTERVAL_SECONDS", "14400")),  # 4 saat = 14400 saniye
            "sfp_update_interval_seconds": int(os.getenv("SFP_UPDATE_INTERVAL_SECONDS", "3600")),   # 1 saat = 3600 saniye
            "fvrp_update_interval_seconds": int(os.getenv("FVRP_UPDATE_INTERVAL", "86400")),  # 24 saat = 86400 saniye
            "entry_timeout_hours": int(os.getenv("ENTRY_TIMEOUT_HOURS", "20")),  # Entry timeout süresi
            "min_confidence_threshold": float(os.getenv("MIN_CONFIDENCE_THRESHOLD", "0.6")),  # Minimum güven seviyesi

            # --- Unified Liquidity Analyzer Parametreleri ---
            "liquidity": {
                "hunt_tolerance_pct": float(os.getenv("LIQUIDITY_HUNT_TOLERANCE_PCT", "0.2")),
                "min_swing_strength": int(os.getenv("LIQUIDITY_MIN_SWING_STRENGTH", "3")),
                "expiry_hours": int(os.getenv("LIQUIDITY_EXPIRY_HOURS", "72")),
                "hunt_confirmation_pips": float(os.getenv("LIQUIDITY_HUNT_CONFIRMATION_PIPS", "5.0")),
                "proximity_pct": float(os.getenv("LIQUIDITY_PROXIMITY_PCT", "0.1")),
                "swing_window": int(os.getenv("LIQUIDITY_SWING_WINDOW", "20")),
                "min_swing_size": float(os.getenv("LIQUIDITY_MIN_SWING_SIZE", "50.0")),
                "max_age_hours": int(os.getenv("LIQUIDITY_MAX_AGE_HOURS", "168"))
            },

            # --- LIQSFP (Liquidity + SFP) Parametreleri ---
            "liqsfp": {
                "tolerance_pct": float(os.getenv("LIQSFP_TOLERANCE_PCT", "0.0"))
            },

            # --- OTE + Order Block Confluence Parametreleri (Strateji 2) ---
            "ote_ob_confluence": {
                "min_intersection_pct": float(os.getenv("OTE_MIN_INTERSECTION_PCT", "30")),
                "sweet_spot_weight": float(os.getenv("OTE_SWEET_SPOT_WEIGHT", "74.0")),
                "min_quality_score": float(os.getenv("OTE_MIN_QUALITY_SCORE", "50")),
                "max_distance_from_current_pct": float(os.getenv("OTE_MAX_DISTANCE_FROM_CURRENT_PCT", "2.0"))
            },

            # --- Yeni Modüler Ayarlar ---
            "risk_management": {
                "capital": float(os.getenv("RM_CAPITAL", "10000")),
                "default_risk_pct": float(os.getenv("RM_DEFAULT_RISK_PCT", "1.0")),
                "leverage": float(os.getenv("RM_LEVERAGE", "10")),
                "maker_fee_pct": float(os.getenv("RM_MAKER_FEE_PCT", "0.02")),
                "taker_fee_pct": float(os.getenv("RM_TAKER_FEE_PCT", "0.05"))
            },
            "sessions": {
                "enabled": os.getenv("SESSIONS_ENABLED", "false").lower() == "true",
                "trade_on_days": [int(d.strip()) for d in os.getenv("SESSIONS_TRADE_DAYS", "0,1,2,3,4").split(',')],
                "definitions": {
                    "london": {
                        "start": os.getenv("SESS_LONDON_START", "08:00"),
                        "end": os.getenv("SESS_LONDON_END", "17:00"),
                        "utc_offset": os.getenv("SESS_LONDON_TZ", "Europe/London")
                    },
                    "new_york": {
                        "start": os.getenv("SESS_NY_START", "13:00"),
                        "end": os.getenv("SESS_NY_END", "22:00"),
                        "utc_offset": os.getenv("SESS_NY_TZ", "America/New_York")
                    }
                }
            },
            # --- Merkezi Analizör Konfigürasyonları ---
            "analyzers": {
                "market_structure": {
                    "mss_sensitivity": int(os.getenv("MSS_SENSITIVITY", "5")),
                    "msb_sensitivity": int(os.getenv("MSB_SENSITIVITY", "5"))
                },
                "pivot": {
                    "length": int(os.getenv("PIVOT_LENGTH", "10"))
                },
                "displacement": {
                    "std_length": int(os.getenv("DISPLACEMENT_STD_LENGTH", "100")),
                    "std_multiplier": float(os.getenv("DISPLACEMENT_STD_MULTIPLIER", "4.0"))
                },
                "supertrend": {
                    "atr_period": int(os.getenv("SUPERTREND_ATR_PERIOD", "10")),
                    "atr_multiplier": float(os.getenv("SUPERTREND_ATR_MULTIPLIER", "3.0")),
                    "use_rma": os.getenv("SUPERTREND_USE_RMA", "true").lower() == "true"
                },
                "npoc": {
                    "resolution": int(os.getenv("NPOC_RESOLUTION", "30")),
                    "lookback_periods": int(os.getenv("NPOC_LOOKBACK_PERIODS", "5"))
                },
                "liqsfp": {
                    "tolerance_pct": float(os.getenv("LIQSFP_TOLERANCE_PCT", "0.0"))
                },
                "liquidity_unified": {
                    "hunt_tolerance_percent": float(os.getenv("LIQUIDITY_HUNT_TOLERANCE_PCT", "0.2")),
                    "min_swing_strength": int(os.getenv("LIQUIDITY_MIN_SWING_STRENGTH", "3")),
                    "liquidity_expiry_hours": int(os.getenv("LIQUIDITY_EXPIRY_HOURS", "72")),
                    "hunt_confirmation_pips": float(os.getenv("LIQUIDITY_HUNT_CONFIRMATION_PIPS", "5.0")),
                    "proximity_pct": float(os.getenv("LIQUIDITY_PROXIMITY_PCT", "0.1")),
                    "swing_window": int(os.getenv("LIQUIDITY_SWING_WINDOW", "20")),
                    "min_swing_size": float(os.getenv("LIQUIDITY_MIN_SWING_SIZE", "50.0")),
                    "max_age_hours": int(os.getenv("LIQUIDITY_MAX_AGE_HOURS", "168"))
                },
                "opening_gap": {
                    "min_gap_size_pct": float(os.getenv("OPENING_GAP_MIN_SIZE_PCT", "0.01"))
                },
                "fvg": {
                    "min_gap_pct": float(os.getenv("FVG_MIN_GAP_PCT", "0.1")),
                    "max_fill_pct": float(os.getenv("FVG_MAX_FILL_PCT", "50.0"))
                },
                "order_block": {
                    "min_volume_ratio": float(os.getenv("OB_MIN_VOLUME_RATIO", "1.5")),
                    "max_lookback": int(os.getenv("OB_MAX_LOOKBACK", "50"))
                },
                "breaker_block": {
                    "min_strength": float(os.getenv("BREAKER_MIN_STRENGTH", "1.0")),
                    "confirmation_candles": int(os.getenv("BREAKER_CONFIRMATION_CANDLES", "3"))
                },
                "fibonacci": {
                    "levels": [float(x.strip()) for x in os.getenv("FIBONACCI_LEVELS", "0.236,0.382,0.5,0.618,0.786").split(',')],
                    "tolerance_pct": float(os.getenv("FIBONACCI_TOLERANCE_PCT", "0.5"))
                },
                "premium_discount": {
                    "premium_threshold": float(os.getenv("PD_PREMIUM_THRESHOLD", "60.0")),
                    "discount_threshold": float(os.getenv("PD_DISCOUNT_THRESHOLD", "40.0")),
                    "equilibrium_tolerance": float(os.getenv("PD_EQUILIBRIUM_TOLERANCE", "5.0"))
                },
                # YENİ: HTF POI + LTF MSS Stratejisi Parametreleri
                "htf_poi_ltf_mss": {
                    "htf_timeframes": ["240", "720", "D"],  # 4h, 12h, Daily
                    "ltf_timeframes": ["60", "15"],  # 1h (primary), 15m (secondary)
                    "max_poi_mitigation_pct": float(os.getenv("HTF_POI_MAX_MITIGATION_PCT", "80")),
                    "max_mss_age_candles": int(os.getenv("LTF_MSS_MAX_AGE_CANDLES", "10")),
                    "min_mss_strength": int(os.getenv("LTF_MSS_MIN_STRENGTH", "3")),
                    "min_confluence_score": float(os.getenv("HTF_POI_MIN_CONFLUENCE_SCORE", "65")),
                    "max_poi_distance_pct": float(os.getenv("HTF_POI_MAX_DISTANCE_PCT", "2.0")),
                    "default_htf_timeframe": os.getenv("HTF_POI_DEFAULT_HTF_TF", "240"),  # 4h
                    "default_ltf_timeframe": os.getenv("HTF_POI_DEFAULT_LTF_TF", "15"),  # 15m
                    "htf_limit": int(os.getenv("HTF_POI_HTF_LIMIT", "240")),
                    "ltf_limit": int(os.getenv("HTF_POI_LTF_LIMIT", "200"))
                },
                # YENİ: Likidite Avı + Zayıf/Güçlü Swing Konfluansı Stratejisi
                "liquidity_hunt_weak_strong": {
                    "min_confluence_score": float(os.getenv("LIQ_HUNT_MIN_CONFLUENCE", "5.0")),
                    "hunt_tolerance_pct": float(os.getenv("LIQ_HUNT_TOLERANCE_PCT", "0.3")),
                    "min_swing_strength": int(os.getenv("LIQ_HUNT_MIN_SWING_STRENGTH", "3")),
                    "weak_swing_max_strength": int(os.getenv("LIQ_HUNT_WEAK_MAX_STRENGTH", "4")),
                    "strong_swing_min_strength": int(os.getenv("LIQ_HUNT_STRONG_MIN_STRENGTH", "6")),
                    "liquidity_max_age_hours": int(os.getenv("LIQ_HUNT_MAX_AGE_HOURS", "72")),  # 3 gün
                    "proximity_tolerance_pct": float(os.getenv("LIQ_HUNT_PROXIMITY_TOL_PCT", "0.2")),
                    "hunt_confirmation_pips": float(os.getenv("LIQ_HUNT_CONFIRMATION_PIPS", "3.0"))
                },
                # YENİ: Killzone + Session Manipulation Stratejisi
                "killzone_session_manipulation": {
                    "min_confluence_score": float(os.getenv("KILLZONE_MIN_CONFLUENCE", "65.0")),
                    "manipulation_tolerance_pct": float(os.getenv("KILLZONE_MANIPULATION_TOLERANCE_PCT", "0.15")),
                    "min_manipulation_pips": float(os.getenv("KILLZONE_MIN_MANIPULATION_PIPS", "3.0")),
                    "max_sweep_age_minutes": int(os.getenv("KILLZONE_MAX_SWEEP_AGE_MINUTES", "30")),
                    "manipulation_window_london": int(os.getenv("KILLZONE_MANIPULATION_WINDOW_LONDON", "120")),  # 2 saat
                    "manipulation_window_newyork": int(os.getenv("KILLZONE_MANIPULATION_WINDOW_NEWYORK", "120")),  # 2 saat
                    "min_confirmation_strength": float(os.getenv("KILLZONE_MIN_CONFIRMATION_STRENGTH", "40.0")),
                    "entry_tolerance_pct": float(os.getenv("KILLZONE_ENTRY_TOLERANCE_PCT", "0.15"))
                },
                # YENİ: Turtle Soup + IFVG Stratejisi
                "turtle_soup_ifvg": {
                    "min_breakout_distance_pct": float(os.getenv("TURTLE_SOUP_MIN_BREAKOUT_PCT", "0.2")),  # %0.2 minimum kırılım
                    "max_breakout_distance_pct": float(os.getenv("TURTLE_SOUP_MAX_BREAKOUT_PCT", "1.0")),  # %1.0 maksimum kırılım
                    "soup_confirmation_candles": int(os.getenv("TURTLE_SOUP_CONFIRMATION_CANDLES", "3")),  # 3 mum teyit
                    "min_level_age_hours": int(os.getenv("TURTLE_SOUP_MIN_LEVEL_AGE_HOURS", "4")),  # Minimum 4 saat eski seviye
                    "min_ifvg_strength": float(os.getenv("TURTLE_SOUP_MIN_IFVG_STRENGTH", "6.0")),  # Minimum IFVG gücü
                    "ifvg_proximity_tolerance_pct": float(os.getenv("TURTLE_SOUP_IFVG_PROXIMITY_PCT", "0.5")),  # %0.5 yakınlık
                    "min_confluence_score": float(os.getenv("TURTLE_SOUP_MIN_CONFLUENCE_SCORE", "70.0")),  # Minimum confluence skoru
                    "max_time_between_events_hours": int(os.getenv("TURTLE_SOUP_MAX_TIME_BETWEEN_HOURS", "12"))  # Max 12 saat arayla
                }
            }
        }
        
        logger.debug("Temel config oluşturuldu")
        
    except Exception as e:
        logger.error(f"Config oluşturulurken hata: {str(e)}")
        # Kritik hata durumunda en az konfigürasyon
        config = {
            "symbols": ["BTCUSDT"],
            "timeframes": ["240"],
            "max_candles": 240,
            "analysis_interval": 480
        }
        logger.warning("Minimal config ile devam ediliyor")
    
    # Dosyadan konfigürasyon yükle (varsa)
    if config_file:
        try:
            with open(config_file, 'r') as f:
                file_config = json.load(f)
                config.update(file_config)
            logger.info(f"Konfigürasyon dosyası ({config_file}) yüklendi.")
        except FileNotFoundError:
            logger.warning(f"Konfigürasyon dosyası bulunamadı: {config_file}")
        except Exception as e:
            logger.error(f"Konfigürasyon dosyası yüklenemedi: {str(e)}")
            
    # Ortam değişkenlerinin config dosyasını ezmesini sağla (isteğe bağlı)
    for key in config:
        env_val = os.getenv(key.upper())
        if env_val is not None:
            # Tip dönüşümü yap (örneğin bool, int)
            if isinstance(config[key], bool):
                config[key] = env_val.lower() == 'true'
            elif isinstance(config[key], int):
                try:
                    config[key] = int(env_val)
                except ValueError:
                     logger.warning(f"Ortam değişkeni {key.upper()} için geçersiz tamsayı değeri: {env_val}")
            elif isinstance(config[key], list) and isinstance(config[key][0], str):
                 config[key] = env_val.split(',')
            else:
                config[key] = env_val

    logger.info("Konfigürasyon yüklendi.")
    # Hassas bilgileri (API anahtarları, token'lar) loglamayalım
    safe_config = {k: v for k, v in config.items() 
                   if k not in ['api_key', 'api_secret', 'telegram_bot_token']}
    logger.debug(f"Yüklenen Konfigürasyon: {safe_config}")
    
    # Fonksiyonun kesinlikle dict döndürdüğünü garanti et
    if config is None:
        logger.error("KRITIK: Config None olarak döndü, varsayılan config kullanılacak!")
        return {
            "symbols": ["BTCUSDT"],
            "timeframes": ["240"],
            "max_candles": 240,
            "analysis_interval": 480
        }
    
    return config
