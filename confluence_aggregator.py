# confluence_aggregator.py

from typing import List, Dict, Any, Tuple
from loguru import logger
import itertools
from datetime import datetime

class ConfluenceAggregator:
    """
    Farklı analiz modüllerinden gelen POI (Point of Interest) bölgelerini birleştirir,
    k<PERSON><PERSON><PERSON><PERSON><PERSON> (confluence) bulur, puanlar ve en yüksek potansiyele sahip
    "Süper POI" bölgelerini oluşturur.
    """

    def __init__(self, config: Dict[str, Any]):
        """
        ConfluenceAggregator'ı başlatır.

        Args:
            config: Ağırlıklar ve diğer ayarları içeren konfigürasyon.
        """
        self.config = config.get('confluence_aggregator', {})
        self.weights = self.config.get('weights', {
            'order_block': 1.0,
            'fvg': 0.8,
            'liquidity_zone': 0.9,
            'ote_level': 1.2,
            'npoc': 0.7,
            'breaker_block': 0.8,
            'rejection_block': 1.1
        })
        self.proximity_tolerance_pct = self.config.get('proximity_tolerance_pct', 0.1)
        logger.info(f"ConfluenceAggregator başlatıldı. Ağırlıklar: {self.weights}")

    def _extract_zones(self, analysis_data: Dict[str, Any]) -> Dict[str, List[Dict]]:
        """
        Farklı analiz sonuçlarından standartlaştırılmış POI bölgelerini çıkarır.
        Her bölge 'type', 'direction', 'top', 'bottom' ve 'source_data' içermelidir.
        """
        zones = {
            'order_block': [], 'fvg': [], 'liquidity_zone': [], 'ote_level': [],
            'npoc': [], 'breaker_block': [], 'rejection_block': []
        }
        
        logger.debug(f"Analysis data keys: {list(analysis_data.keys())}")

        # Order Blocks
        ob_data = analysis_data.get('main_tf_order_blocks', {})
        logger.debug(f"Order Block data type: {type(ob_data)}, content: {ob_data}")
        
        if isinstance(ob_data, dict):
            # Bullish Order Blocks
            bullish_obs = ob_data.get('bullish', [])
            logger.debug(f"Bullish OBs count: {len(bullish_obs)}")
            for i, ob in enumerate(bullish_obs):
                logger.debug(f"Bullish OB {i} keys: {list(ob.keys()) if isinstance(ob, dict) else 'Not a dict'}")
                try:
                    # Bullish Order Block için: top = high, bottom = low
                    ob_top = ob.get('high', ob.get('top', 0))
                    ob_bottom = ob.get('low', ob.get('bottom', 0))
                    if ob_top == 0 or ob_bottom == 0:
                        logger.warning(f"Bullish OB {i} eksik fiyat verisi: top={ob_top}, bottom={ob_bottom}")
                        continue
                    zones['order_block'].append({'type': 'order_block', 'direction': 'bullish', 'top': ob_top, 'bottom': ob_bottom, 'source_data': ob})
                except Exception as e:
                    logger.error(f"Bullish OB {i} işlenirken hata: {e}")
                    
            # Bearish Order Blocks  
            bearish_obs = ob_data.get('bearish', [])
            logger.debug(f"Bearish OBs count: {len(bearish_obs)}")
            for i, ob in enumerate(bearish_obs):
                logger.debug(f"Bearish OB {i} keys: {list(ob.keys()) if isinstance(ob, dict) else 'Not a dict'}")
                try:
                    # Bearish Order Block için: top = high, bottom = low
                    ob_top = ob.get('high', ob.get('top', 0))
                    ob_bottom = ob.get('low', ob.get('bottom', 0))
                    if ob_top == 0 or ob_bottom == 0:
                        logger.warning(f"Bearish OB {i} eksik fiyat verisi: top={ob_top}, bottom={ob_bottom}")
                        continue
                    zones['order_block'].append({'type': 'order_block', 'direction': 'bearish', 'top': ob_top, 'bottom': ob_bottom, 'source_data': ob})
                except Exception as e:
                    logger.error(f"Bearish OB {i} işlenirken hata: {e}")

        # FVG
        fvg_list = analysis_data.get('fvg_analysis', [])
        if isinstance(fvg_list, list):
            for fvg in fvg_list:
                zones['fvg'].append({'type': 'fvg', 'direction': fvg.get('type'), 'top': fvg['top'], 'bottom': fvg['bottom'], 'source_data': fvg})

        # Liquidity Zones
        liq_data = analysis_data.get('liquidity_analysis', {})
        logger.debug(f"Liquidity data type: {type(liq_data)}")
        
        if isinstance(liq_data, dict):
            # External liquidity'den BSL/SSL zones
            ext_liq = liq_data.get('external_liquidity', {})
            if isinstance(ext_liq, dict):
                bsl_zones = ext_liq.get('bsl_zones', [])
                ssl_zones = ext_liq.get('ssl_zones', [])
                logger.debug(f"BSL zones: {len(bsl_zones)}, SSL zones: {len(ssl_zones)}")
                
                for bsl in bsl_zones:
                    try:
                        price = bsl.get('price', 0)
                        if price > 0:
                            # BSL için küçük bir aralık oluştur
                            zones['liquidity_zone'].append({
                                'type': 'liquidity_zone', 
                                'direction': 'bearish', 
                                'top': price * 1.001, 
                                'bottom': price * 0.999, 
                                'source_data': bsl
                            })
                    except Exception as e:
                        logger.error(f"BSL zone işlenirken hata: {e}")
                        
                for ssl in ssl_zones:
                    try:
                        price = ssl.get('price', 0)
                        if price > 0:
                            # SSL için küçük bir aralık oluştur
                            zones['liquidity_zone'].append({
                                'type': 'liquidity_zone', 
                                'direction': 'bullish', 
                                'top': price * 1.001, 
                                'bottom': price * 0.999, 
                                'source_data': ssl
                            })
                    except Exception as e:
                        logger.error(f"SSL zone işlenirken hata: {e}")
            
            # Direkt BSL/SSL zones (backward compatibility)
            direct_bsl = liq_data.get('bsl_zones', [])
            direct_ssl = liq_data.get('ssl_zones', [])
            logger.debug(f"Direct BSL zones: {len(direct_bsl)}, Direct SSL zones: {len(direct_ssl)}")
            
            for bsl in direct_bsl:
                try:
                    price = bsl.get('price', 0)
                    if price > 0:
                        zones['liquidity_zone'].append({
                            'type': 'liquidity_zone', 
                            'direction': 'bearish', 
                            'top': price * 1.001, 
                            'bottom': price * 0.999, 
                            'source_data': bsl
                        })
                except Exception as e:
                    logger.error(f"Direct BSL zone işlenirken hata: {e}")
                    
            for ssl in direct_ssl:
                try:
                    price = ssl.get('price', 0)
                    if price > 0:
                        zones['liquidity_zone'].append({
                            'type': 'liquidity_zone', 
                            'direction': 'bullish', 
                            'top': price * 1.001, 
                            'bottom': price * 0.999, 
                            'source_data': ssl
                        })
                except Exception as e:
                    logger.error(f"Direct SSL zone işlenirken hata: {e}")

        # OTE Levels
        fib_data = analysis_data.get('fibonacci_analysis', {})
        if isinstance(fib_data, dict) and 'ote_levels' in fib_data:
            ote_levels = fib_data['ote_levels']
            if isinstance(ote_levels, dict) and not ote_levels.get('error'):
                # OTE seviyelerinde doğru anahtar isimlerini kullan
                top_price = ote_levels.get('entry_zone_top', 0)
                bottom_price = ote_levels.get('entry_zone_bottom', 0)
                
                if top_price > 0 and bottom_price > 0:
                    zones['ote_level'].append({
                        'type': 'ote_level', 
                        'direction': ote_levels['direction'], 
                        'top': top_price, 
                        'bottom': bottom_price, 
                        'source_data': ote_levels
                    })
                else:
                    logger.warning(f"OTE seviyelerinde geçersiz fiyat değerleri: top={top_price}, bottom={bottom_price}")

        # Breaker Blocks
        breaker_data = analysis_data.get('breaker_block_analysis', [])
        if isinstance(breaker_data, list):
            for breaker in breaker_data:
                direction = 'bullish' if 'bullish' in breaker.get('type', '') else 'bearish'
                zones['breaker_block'].append({
                    'type': 'breaker_block',
                    'direction': direction,
                    'top': breaker['top'],
                    'bottom': breaker['bottom'],
                    'source_data': breaker
                })
        
        logger.debug(f"Breaker Block zones extracted: {len(zones['breaker_block'])}")

        # Diğer analizörler buraya eklenebilir (nPOC, Rejection vb.)

        for zone_type, zone_list in zones.items():
            logger.debug(f"Extracted {len(zone_list)} zones for type '{zone_type}'")
        return zones


    def _zones_overlap(self, zone1: Dict, zone2: Dict) -> bool:
        """İki bölgenin fiyat aralıklarının kesişip kesişmediğini kontrol eder."""
        # Yön uyumunu kontrol et
        if zone1.get('direction') != zone2.get('direction'):
            return False
        
        # Fiyat aralığı kesişimi
        overlap = max(zone1['bottom'], zone2['bottom']) <= min(zone1['top'], zone2['top'])
        return overlap

    def analyze_ob12_bos1(self, symbol: str, all_symbol_data: Dict[str, Any]) -> List[Dict[str, Any]]:
        """
        HTF Order Block ve BOS (Break of Structure) kesişimini analiz eder.
        Bu mantık scoring_system'den bu modüle taşınmıştır.
        """
        potential_signals = []
        structure_analysis = all_symbol_data.get('main_tf_structure', {})
        order_block_analysis = all_symbol_data.get('main_tf_order_blocks', {})
        
        structure_breaks = structure_analysis.get('breaks', [])
        if not structure_breaks:
            return []

        recent_bos = [b for b in structure_breaks if b.get('break_type') == 'BOS'][-2:]
        
        for break_event in recent_bos:
            bull_ob_list = order_block_analysis.get('bullish')
            bear_ob_list = order_block_analysis.get('bearish')

            # Bullish OB + Bullish BOS
            if break_event.get('direction') == 'bull' and bull_ob_list:
                bull_ob = bull_ob_list[0]
                if bull_ob.get('timeframe') in ['12h', '720', 'D']:
                    if abs(bull_ob.get('high', 0) - break_event.get('price', 0)) < (break_event.get('price', 1) * 0.01):
                        ob_bos_signal = {
                            'type': 'OB12_BOS1',
                            'symbol': symbol,
                            'direction': 'bull',
                            'price': break_event.get('price'),
                            'confidence': 0.8,
                            'reason': 'HTF Order Block + BOS Confluence',
                            'ob_data': bull_ob,
                            'break_data': break_event,
                            'priority_level': 8,
                            'confluence_score': 75
                        }
                        potential_signals.append(ob_bos_signal)
                        logger.info(f"[{symbol}] OB12_BOS1 Sinyali eklendi: HTF Order Block + BOS (BULL)")

            # Bearish OB + Bearish BOS
            elif break_event.get('direction') == 'bear' and bear_ob_list:
                bear_ob = bear_ob_list[0]
                if bear_ob.get('timeframe') in ['12h', '720', 'D']:
                    if abs(bear_ob.get('low', 0) - break_event.get('price', 0)) < (break_event.get('price', 1) * 0.01):
                        ob_bos_signal = {
                            'type': 'OB12_BOS1',
                            'symbol': symbol,
                            'direction': 'bear',
                            'price': break_event.get('price'),
                            'confidence': 0.8,
                            'reason': 'HTF Order Block + BOS Confluence',
                            'ob_data': bear_ob,
                            'break_data': break_event,
                            'priority_level': 8,
                            'confluence_score': 75
                        }
                        potential_signals.append(ob_bos_signal)
                        logger.info(f"[{symbol}] OB12_BOS1 Sinyali eklendi: HTF Order Block + BOS (BEAR)")
                        
        return potential_signals

    def aggregate(self, analysis_data: Dict[str, Any], current_price: float) -> List[Dict]:
        """
        Tüm analizlerden gelen bölgeleri birleştirir ve "Süper POI"ları oluşturur.

        Args:
            analysis_data: Tüm analizörlerden gelen sonuçları içeren dict.
            current_price: Mevcut piyasa fiyatı.

        Returns:
            Puanlanmış ve birleştirilmiş "Süper POI" bölgelerinin listesi.
        """
        logger.info("Confluence Aggregator: Analiz sonuçları birleştiriliyor...")
        all_zones_by_type = self._extract_zones(analysis_data)
        
        base_pois = all_zones_by_type.get('order_block', []) + all_zones_by_type.get('rejection_block', [])
        other_zones = list(itertools.chain.from_iterable(
            zone_list for type, zone_list in all_zones_by_type.items() if type not in ['order_block', 'rejection_block']
        ))

        if not base_pois:
            logger.warning("Confluence analizi için temel POI (Order Block/Rejection Block) bulunamadı.")
            return []

        super_pois = []
        for base_poi in base_pois:
            confluence_score = self.weights.get(base_poi['type'], 1.0)
            confluent_factors = [base_poi['type']]
            
            overlapping_zones = [base_poi]

            for other_zone in other_zones:
                if self._zones_overlap(base_poi, other_zone):
                    logger.debug(f"Kesişim bulundu: {base_poi['type']} ({base_poi['direction']}) ve {other_zone['type']} ({other_zone['direction']})")
                    confluence_score += self.weights.get(other_zone['type'], 0.0)
                    confluent_factors.append(other_zone['type'])
                    overlapping_zones.append(other_zone)
            
            if len(confluent_factors) > 1:
                super_poi_top = max(z['top'] for z in overlapping_zones)
                super_poi_bottom = min(z['bottom'] for z in overlapping_zones)
                
                direction = base_poi['direction']

                distance_to_poi = abs(current_price - ((super_poi_top + super_poi_bottom) / 2))
                price_range = super_poi_top - super_poi_bottom
                
                proximity_score = 0
                if price_range > 0:
                    proximity_score = max(0, 1 - (distance_to_poi / (price_range * 10)))
                
                final_score = confluence_score * (1 + proximity_score * 0.5)

                super_pois.append({
                    'symbol': analysis_data.get('symbol', 'N/A'),
                    'direction': direction,
                    'super_poi_top': super_poi_top,
                    'super_poi_bottom': super_poi_bottom,
                    'confluence_score': final_score,
                    'confluent_factors': list(set(confluent_factors)),
                    'source_zones': overlapping_zones,
                    'timestamp': datetime.now().isoformat()
                })

        super_pois.sort(key=lambda x: x['confluence_score'], reverse=True)
        
        logger.success(f"Toplam {len(super_pois)} adet 'Süper POI' oluşturuldu.")
        if super_pois:
            logger.info(f"En yüksek skorlu Süper POI: {super_pois[0]['confluence_score']:.2f} ({', '.join(super_pois[0]['confluent_factors'])})")
            
        return super_pois