import pandas as pd
from loguru import logger
from typing import Dict, List, Optional

class CVDAnalyzer:
    """
    Ham işlem verilerinden Volume Delta ve Cumulative Volume Delta (CVD) hesaplar.
    """
    
    def __init__(self):
        """CVDAnalyzer sınıfını başlatır."""
        self.lookback = 20  # Analiz için geriye bakış periyodu
    
    def analyze_oi_cvd_divergence(self, open_interest_data: List[Dict], cvd_data: Dict[str, pd.DataFrame], symbol: str) -> Optional[Dict]:
        """
        Open Interest ve CVD verilerini kullanarak squeeze potansiyelini analiz eder.
        
        Args:
            open_interest_data (List[Dict]): Bybit'den gelen Open Interest verisi
            cvd_data (Dict[str, pd.DataFrame]): CVD verisi (zaman dilimine göre)
            symbol (str): Kripto para sembolü
            
        Returns:
            Optional[Dict]: Squeeze analiz sonuçları veya None
        """
        # YENİ EKLENECEK SATIR - Her çalıştığında log üret
        logger.debug(f"[{symbol}] CVD/OI analizi çalıştırılıyor...")
        
        if not open_interest_data or not cvd_data:
            logger.debug(f"[{symbol}] CVD/OI analizi için yeterli veri yok")
            return None
            
        if len(open_interest_data) < self.lookback:
            logger.debug(f"[{symbol}] OI verisi yetersiz (mevcut: {len(open_interest_data)}, gereken: {self.lookback})")
            return None
        
        try:
            # Open Interest trendini analiz et
            recent_oi = float(open_interest_data[-1].get('openInterest', 0))
            previous_oi = float(open_interest_data[-2].get('openInterest', 0))
            
            if previous_oi == 0:
                logger.debug(f"[{symbol}] OI önceki değer sıfır, analiz yapılamıyor")
                return None
                
            oi_change = ((recent_oi - previous_oi) / previous_oi * 100)
            
            # CVD trendini analiz et (4h'i tercih et, yoksa 1h kullan)
            cvd_df = None
            for tf in ['4h', '1h']:
                temp_df = cvd_data.get(tf)
                if temp_df is not None and isinstance(temp_df, pd.DataFrame) and not temp_df.empty:
                    cvd_df = temp_df
                    break

            if cvd_df is None or cvd_df.empty or len(cvd_df) < 2:
                logger.debug(f"[{symbol}] CVD verisi yetersiz")
                return None
                
            recent_cvd = cvd_df.iloc[-1]['cvd']
            previous_cvd = cvd_df.iloc[-2]['cvd']
            cvd_change = recent_cvd - previous_cvd
            
            # Squeeze pattern tespiti
            squeeze_threshold = 5.0  # OI değişim eşiği (%)
            
            # Bullish Squeeze: OI artıyor + CVD düşüyor
            if oi_change > squeeze_threshold and cvd_change < 0:
                logger.info(f"[{symbol}] BULLISH SQUEEZE tespit edildi - OI: +{oi_change:.1f}%, CVD: {cvd_change:.2f}")
                return {
                    'type': 'BULLISH_SQUEEZE',
                    'details': f"OI +{oi_change:.1f}%, CVD -{abs(cvd_change):.2f}",
                    'oi_change': oi_change,
                    'cvd_change': cvd_change,
                    'confidence': abs(oi_change) + abs(cvd_change)
                }
            
            # Bearish Squeeze: OI artıyor + CVD yükseliyor
            elif oi_change > squeeze_threshold and cvd_change > 0:
                logger.info(f"[{symbol}] BEARISH SQUEEZE tespit edildi - OI: +{oi_change:.1f}%, CVD: +{cvd_change:.2f}")
                return {
                    'type': 'BEARISH_SQUEEZE',
                    'details': f"OI +{oi_change:.1f}%, CVD +{cvd_change:.2f}",
                    'oi_change': oi_change,
                    'cvd_change': cvd_change,
                    'confidence': abs(oi_change) + abs(cvd_change)
                }
            
            # Herhangi bir squeeze tespit edilmezse
            logger.debug(f"[{symbol}] Squeeze tespit edilmedi - OI: {oi_change:.1f}%, CVD: {cvd_change:.2f}")
            return None
            
        except Exception as e:
            logger.error(f"[{symbol}] CVD/OI divergence analizi sırasında hata: {e}", exc_info=True)
            return None

    def calculate_cvd_from_trades(
        self,
        trades: List[Dict],
        timeframes: List[str] = ['1h', '4h']
    ) -> Optional[Dict[str, pd.DataFrame]]:
        """
        Verilen ham işlem listesinden belirtilen zaman dilimleri için CVD hesaplar.

        Args:
            trades (List[Dict]): Bybit'in /v5/market/public-trade-history endpoint'inden gelen ham işlem listesi.
            timeframes (List[str]): Hesaplanacak zaman dilimleri (örn. ['1h', '4h']).

        Returns:
            Optional[Dict[str, pd.DataFrame]]: Her zaman dilimi için CVD verilerini içeren bir sözlük.
            Örnek: {'1h': DataFrame, '4h': DataFrame}
            DataFrame Sütunları: ['timestamp', 'volume_delta', 'cvd']
        """
        if not trades:
            logger.warning("CVD hesaplaması için işlem verisi bulunamadı.")
            return None

        try:
            # Gelen veriyi DataFrame'e dönüştür
            df = pd.DataFrame(trades)
            
            # Gerekli sütunları doğru veri tiplerine çevir
            df['price'] = pd.to_numeric(df['price'])
            df['size'] = pd.to_numeric(df['size'])
            
            # Time sütununu güvenli şekilde datetime'a çevir
            # Önce numeric'e çevir, sonra milisaniye olarak datetime'a dönüştür
            df['time'] = pd.to_numeric(df['time'])  # String'i sayıya çevir
            df['time'] = pd.to_datetime(df['time'], unit='ms')  # Milisaniyeyi datetime'a çevir
            df.set_index('time', inplace=True)

            # Volume Delta'yı hesapla: Alışlar pozitif, satışlar negatif
            # 'side' -> Buy/Sell
            df['volume_delta'] = df.apply(lambda row: row['size'] if row['side'] == 'Buy' else -row['size'], axis=1)

            results = {}
            for tf in timeframes:
                # Zaman dilimine göre yeniden örnekle (resample) ve Volume Delta'yı topla
                delta_per_bar = df['volume_delta'].resample(tf).sum().to_frame(name='volume_delta')
                
                # NaN değerleri 0 ile doldur
                delta_per_bar.fillna(0, inplace=True)
                
                # Cumulative Volume Delta (CVD) hesapla
                delta_per_bar['cvd'] = delta_per_bar['volume_delta'].cumsum()
                
                # Timestamp'i sütun olarak ekle
                delta_per_bar.reset_index(inplace=True)
                delta_per_bar.rename(columns={'time': 'timestamp'}, inplace=True)
                
                results[tf] = delta_per_bar
                logger.info(f"[{tf}] için CVD başarıyla hesaplandı. Son CVD değeri: {delta_per_bar['cvd'].iloc[-1]:.2f}")

            return results

        except Exception as e:
            logger.error(f"CVD hesaplaması sırasında bir hata oluştu: {e}", exc_info=True)
            return None
