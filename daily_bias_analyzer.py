import pandas as pd
from typing import Dict, Any, Optional, Tuple, List
from loguru import logger
from datetime import datetime, time
from utils import format_price_standard

class DailyBiasAnalyzer:
    """
    Önceki günün yüksek ve düşük seviyelerine göre günl<PERSON><PERSON> eğilim (bias) belirleyen sınıf.
    TTrades Daily Bias [TFO] göstergesinin mantığını kullanır.
    
    GELİŞTİRİLMİŞ VERSİYON: Power of Three (Po3) analizi eklendi.
    Po3, günlük mumların üç aşamasını analiz eder:
    1. <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> (Opening) - Günün başlangıç fiyatı
    2. <PERSON><PERSON><PERSON>lasyon (Manipulation) - Günün en yüksek/düşük seviyeleri
    3. Genişleme (Expansion) - Günün ana trendi ve kapanış
    """
    def __init__(self):
        self.bias_data = {}  # Sembol ve timeframe'e göre bias verilerini saklar
        self.po3_data = {}   # Power of Three analiz verilerini saklar
        logger.info("DailyBiasAnalyzer (Po3 destekli) başlatıldı")
    
    def _init_symbol_data(self, symbol: str, timeframe: str) -> None:
        """Yeni bir sembol için veri yapısını başlatır"""
        key = f"{symbol}_{timeframe}"
        if key not in self.bias_data:
            self.bias_data[key] = {
                "prev_high": None,  # Önceki günün yükseği (PDH)
                "prev_low": None,   # Önceki günün düşüğü (PDL)
                "current_high": None,  # Mevcut günün yükseği
                "current_low": None,   # Mevcut günün düşüğü
                "prev_open": None,     # Önceki günün açılışı
                "prev_up": False,      # Önceki günün trendi yukarı yönlü mü
                "bias": 0              # Bias: 1 (Bullish), -1 (Bearish), 0 (Nötr)
            }
    
    def determine_bias(self, symbol: str, timeframe: str, candles: pd.DataFrame) -> int:
        """
        Verilen mum verilerine göre günlük eğilimi (bias) belirler
        
        Args:
            symbol: İşlem sembolü
            timeframe: Zaman dilimi
            candles: OHLCV verileri içeren DataFrame
            
        Returns:
            int: 1 (Bullish Bias), -1 (Bearish Bias), 0 (No Bias)
        """
        if candles.empty or len(candles) < 2:
            logger.warning(f"{symbol} {timeframe} için yeterli veri yok, bias hesaplanamadı")
            return 0
            
        self._init_symbol_data(symbol, timeframe)
        key = f"{symbol}_{timeframe}"
        data = self.bias_data[key]
        
        # Son iki mumu al
        prev_candle = candles.iloc[-2]
        current_candle = candles.iloc[-1]
        
        # Veri hazırlığı ve hesaplama
        firstRun = False  # İlk çalıştırma mı kontrolü
        
        # Önceki veri yok ise ilk kez hesaplıyoruz demektir
        if data["prev_high"] is None:
            firstRun = True
            # İlk hesaplama için veri yapısını doldur
            data["prev_high"] = prev_candle["high"]
            data["prev_low"] = prev_candle["low"]
            data["current_high"] = current_candle["high"]
            data["current_low"] = current_candle["low"]
            data["prev_open"] = prev_candle["open"]
            data["prev_up"] = prev_candle["close"] > prev_candle["open"]
            logger.info(f"{symbol} {timeframe} için ilk bias hesaplaması yapılıyor")
        # Normal çalışma durumu - günlük değişim varsa verileri güncelle
        elif timeframe.upper() == 'D' or timeframe == '1440':
            # Önceki günün değerlerini sakla
            data["prev_high"] = data["current_high"]
            data["prev_low"] = data["current_low"]
            data["prev_open"] = current_candle["open"]
            data["prev_up"] = current_candle["close"] > current_candle["open"]
            
            # Yeni günün değerlerini güncelle
            data["current_high"] = current_candle["high"]
            data["current_low"] = current_candle["low"]
        else:
            # Günlük olmayan timeframe'lerde doğrudan mevcut mumu kullan
            data["current_high"] = current_candle["high"]
            data["current_low"] = current_candle["low"]
        
        # Yukarıya taşındı
        
        # Değerleri yerel değişkenlerde sakla daha kolay erişim için
        ph = data["prev_high"]
        pl = data["prev_low"]
        ch = data["current_high"]
        cl = data["current_low"]
        close = current_candle["close"]
        p_up = data["prev_up"]
        
        # TTrades Daily Bias Mantığını Uygula
        bias = 0
        bias_reason = ""
        
        # Kural 1: Kapanış önceki yüksekten büyükse -> Bullish Bias
        if close > ph:
            bias = 1
            bias_reason = "Kapanış PDH üzerinde"
        
        # Kural 2: Kapanış önceki düşükten küçükse -> Bearish Bias
        elif close < pl:
            bias = -1
            bias_reason = "Kapanış PDL altında"
        
        # Kural 3: Kapanış içeride, günün yükseği PDH üzerinde, düşüğü PDL üzerindeyse
        elif close < ph and close > pl and ch > ph and cl > pl:
            bias = -1
            bias_reason = "PDH üzerine çıkamadı"
        
        # Kural 4: Kapanış içeride, günün yükseği PDH altında, düşüğü PDL altındaysa
        elif close > pl and close < ph and ch < ph and cl < pl:
            bias = 1
            bias_reason = "PDL altına inemedi"
        
        # Kural 5: Günün yükseği PDH altında/eşit, düşüğü PDL üstünde/eşitse -> önceki trend devam eder
        elif ch <= ph and cl >= pl:
            bias = 1 if p_up else -1
            bias_reason = "Inside bar - Önceki trend devam ediyor"
        
        # Kural 6: Diğer durumlar -> No Bias
        else:
            bias = 0
            bias_reason = "Outside bar - eğilim belirsiz"
        
        # Sonucu sakla
        data["bias"] = bias
        
        # Log mesajı oluştur
        bias_text = "Bullish Bias" if bias == 1 else "Bearish Bias" if bias == -1 else "Nötr"
        
        # İlk çalıştırmada daha detaylı log yazalım
        if firstRun:
            logger.info(f"Daily Bias: {symbol} {timeframe}: {bias_text} - {bias_reason} [ilk hesaplama]") 
        else:
            logger.info(f"Daily Bias: {symbol} {timeframe}: {bias_text} - {bias_reason}")
            
        logger.debug(f"PDH: {format_price_standard(ph)}, PDL: {format_price_standard(pl)}, " 
                     f"CH: {format_price_standard(ch)}, CL: {format_price_standard(cl)}, "
                     f"Close: {format_price_standard(close)}")
        
        return bias
    
    def get_bias(self, symbol: str, timeframe: str) -> int:
        """Belirli bir sembol ve timeframe için mevcut eğilimi döndürür"""
        key = f"{symbol}_{timeframe}"
        if key in self.bias_data:
            return self.bias_data[key]["bias"]
        return 0

    # =================================================================================
    # POWER OF THREE (Po3) ANALYSIS - ICT Gelişmiş Günlük Bias Sistemi
    # =================================================================================

    def analyze_power_of_three(self, symbol: str, candles: pd.DataFrame) -> Dict[str, Any]:
        """
        Power of Three (Po3) analizi yapar.
        
        Po3, ICT konseptine göre günlük mumların 3 aşamasını analiz eder:
        1. Açılış (Opening): Günün açılış fiyatı ve ilk hareket
        2. Manipülasyon (Manipulation): Günün ekstrem noktaları (HOD/LOD)
        3. Genişleme (Expansion): Ana trend ve kapanış yönü
        
        Args:
            symbol: İşlem sembolü
            candles: Günlük mum verileri
            
        Returns:
            Po3 analiz sonuçları
        """
        if candles is None or len(candles) < 2:
            logger.warning(f"{symbol} için Po3 analizi için yetersiz veri")
            return self._empty_po3_result()
        
        logger.info(f"🔄 {symbol} için Power of Three analizi başlıyor")
        
        # Günlük mumu al (en son mum)
        daily_candle = candles.iloc[-1]
        
        # Po3 aşamalarını analiz et
        po3_phases = self._analyze_po3_phases(daily_candle)
        
        # Po3 bias'ını hesapla
        po3_bias = self._calculate_po3_bias(po3_phases, daily_candle)
        
        # Gün içi Po3 sinyallerini tespit et
        intraday_signals = self._detect_po3_intraday_signals(po3_phases)
        
        # Po3 kalitesini değerlendir
        po3_quality = self._evaluate_po3_quality(po3_phases, daily_candle)
        
        result = {
            'symbol': symbol,
            'po3_phases': po3_phases,
            'po3_bias': po3_bias,
            'intraday_signals': intraday_signals,
            'quality': po3_quality,
            'daily_candle': {
                'open': float(daily_candle['open']),
                'high': float(daily_candle['high']),
                'low': float(daily_candle['low']),
                'close': float(daily_candle['close']),
                'timestamp': daily_candle.get('timestamp', datetime.now())
            },
            'analysis_timestamp': datetime.now().isoformat()
        }
        
        # Po3 verilerini sakla
        self.po3_data[symbol] = result
        
        logger.success(f"✅ {symbol} Po3 analizi tamamlandı: "
                      f"Bias={po3_bias}, Quality={po3_quality}")
        
        return result

    def _analyze_po3_phases(self, daily_candle: pd.Series) -> Dict[str, Any]:
        """
        Günlük mumun Po3 aşamalarını analiz eder.
        """
        open_price = float(daily_candle['open'])
        high_price = float(daily_candle['high'])
        low_price = float(daily_candle['low'])
        close_price = float(daily_candle['close'])
        
        # 1. AÇILIŞ (Opening) Aşaması
        opening_phase = {
            'price': open_price,
            'level': 'OPENING',
            'significance': 'HIGH'  # Açılış her zaman önemli
        }
        
        # 2. MANİPÜLASYON (Manipulation) Aşaması
        # Ekstrem noktaları belirle
        manipulation_high = {
            'price': high_price,
            'level': 'HOD',  # High of Day
            'manipulation_type': 'UPSIDE_MANIPULATION',
            'distance_from_open': ((high_price - open_price) / open_price) * 100
        }
        
        manipulation_low = {
            'price': low_price,
            'level': 'LOD',  # Low of Day
            'manipulation_type': 'DOWNSIDE_MANIPULATION',
            'distance_from_open': ((open_price - low_price) / open_price) * 100
        }
        
        # Hangi manipülasyon daha güçlü?
        dominant_manipulation = (
            manipulation_high if manipulation_high['distance_from_open'] >= manipulation_low['distance_from_open']
            else manipulation_low
        )
        
        # 3. GENİŞLEME (Expansion) Aşaması
        # Kapanışın açılışa göre durumu
        expansion_direction = 'BULLISH' if close_price > open_price else 'BEARISH'
        expansion_strength = abs((close_price - open_price) / open_price) * 100
        
        # Kapanışın günün range'ındaki pozisyonu
        daily_range = high_price - low_price
        if daily_range > 0:
            close_position_pct = ((close_price - low_price) / daily_range) * 100
        else:
            close_position_pct = 50  # Range yok ise ortada kabul et
        
        expansion_phase = {
            'direction': expansion_direction,
            'strength': expansion_strength,
            'close_position_pct': close_position_pct,
            'close_near_high': close_position_pct >= 75,  # Üst %25'te
            'close_near_low': close_position_pct <= 25,   # Alt %25'te
            'close_price': close_price
        }
        
        return {
            'opening': opening_phase,
            'manipulation': {
                'high': manipulation_high,
                'low': manipulation_low,
                'dominant': dominant_manipulation
            },
            'expansion': expansion_phase
        }

    def _calculate_po3_bias(self, po3_phases: Dict[str, Any], 
                           daily_candle: pd.Series) -> Dict[str, Any]:
        """
        Po3 analizi temelinde bias hesaplar.
        """
        opening = po3_phases['opening']
        manipulation = po3_phases['manipulation']
        expansion = po3_phases['expansion']
        
        bias_score = 0
        bias_reasons = []
        
        # 1. Ekspansyon yönü (en önemli faktör)
        if expansion['direction'] == 'BULLISH':
            bias_score += 3
            bias_reasons.append("Bullish Expansion")
        else:
            bias_score -= 3
            bias_reasons.append("Bearish Expansion")
        
        # 2. Ekspansyon gücü
        if expansion['strength'] >= 2.0:  # %2'den fazla hareket
            if expansion['direction'] == 'BULLISH':
                bias_score += 2
                bias_reasons.append("Güçlü Bullish Hareket")
            else:
                bias_score -= 2
                bias_reasons.append("Güçlü Bearish Hareket")
        
        # 3. Kapanış pozisyonu
        if expansion['close_near_high']:
            bias_score += 1
            bias_reasons.append("Kapanış yüksek seviyede")
        elif expansion['close_near_low']:
            bias_score -= 1
            bias_reasons.append("Kapanış düşük seviyede")
        
        # 4. Dominant manipülasyon analizi
        dominant_manip = manipulation['dominant']
        if dominant_manip['manipulation_type'] == 'UPSIDE_MANIPULATION':
            if expansion['direction'] == 'BEARISH':
                # Yukarı manipülasyon sonrası aşağı ekspansyon = Bearish bias
                bias_score -= 1
                bias_reasons.append("Upside manip + Bearish expansion")
        else:  # DOWNSIDE_MANIPULATION
            if expansion['direction'] == 'BULLISH':
                # Aşağı manipülasyon sonrası yukarı ekspansyon = Bullish bias
                bias_score += 1
                bias_reasons.append("Downside manip + Bullish expansion")
        
        # Bias yönünü belirle
        if bias_score >= 2:
            bias_direction = 'BULLISH'
        elif bias_score <= -2:
            bias_direction = 'BEARISH'
        else:
            bias_direction = 'NEUTRAL'
        
        # Confidence seviyesi
        confidence = min(100, abs(bias_score) * 15)  # 0-100 arası
        
        return {
            'direction': bias_direction,
            'score': bias_score,
            'confidence': confidence,
            'reasons': bias_reasons
        }

    def _detect_po3_intraday_signals(self, po3_phases: Dict[str, Any]) -> List[Dict[str, Any]]:
        """
        Po3 analizi temelinde gün içi sinyaller üretir.
        """
        signals = []
        manipulation = po3_phases['manipulation']
        expansion = po3_phases['expansion']
        
        # Sinyal 1: Manipülasyon seviyelerinde reversal
        if manipulation['high']['distance_from_open'] >= 1.0:  # %1'den fazla manipulation
            signal = {
                'type': 'PO3_MANIPULATION_REVERSAL',
                'level': manipulation['high']['price'],
                'direction': 'BEARISH',
                'reasoning': 'HOD manipulation seviyesinde short fırsatı',
                'quality': 'HIGH' if manipulation['high']['distance_from_open'] >= 2.0 else 'MEDIUM'
            }
            signals.append(signal)
        
        if manipulation['low']['distance_from_open'] >= 1.0:  # %1'den fazla manipulation
            signal = {
                'type': 'PO3_MANIPULATION_REVERSAL',
                'level': manipulation['low']['price'],
                'direction': 'BULLISH',
                'reasoning': 'LOD manipulation seviyesinde long fırsatı',
                'quality': 'HIGH' if manipulation['low']['distance_from_open'] >= 2.0 else 'MEDIUM'
            }
            signals.append(signal)
        
        # Sinyal 2: Ekspansyon yönünde continuation
        if expansion['strength'] >= 1.5:
            signal = {
                'type': 'PO3_EXPANSION_CONTINUATION',
                'direction': expansion['direction'],
                'reasoning': f"Güçlü {expansion['direction'].lower()} ekspansyon devamı beklenir",
                'quality': 'HIGH' if expansion['strength'] >= 3.0 else 'MEDIUM'
            }
            signals.append(signal)
        
        return signals

    def _evaluate_po3_quality(self, po3_phases: Dict[str, Any], 
                             daily_candle: pd.Series) -> str:
        """
        Po3 analizinin kalitesini değerlendirir.
        """
        expansion = po3_phases['expansion']
        manipulation = po3_phases['manipulation']
        
        quality_score = 0
        
        # 1. Ekspansyon gücü
        if expansion['strength'] >= 3.0:
            quality_score += 3
        elif expansion['strength'] >= 1.5:
            quality_score += 2
        elif expansion['strength'] >= 0.5:
            quality_score += 1
        
        # 2. Kapanış pozisyonu
        if expansion['close_near_high'] or expansion['close_near_low']:
            quality_score += 2
        
        # 3. Manipülasyon gücü
        max_manip_distance = max(
            manipulation['high']['distance_from_open'],
            manipulation['low']['distance_from_open']
        )
        if max_manip_distance >= 2.0:
            quality_score += 2
        elif max_manip_distance >= 1.0:
            quality_score += 1
        
        # 4. Volume (eğer mevcut ise)
        volume = daily_candle.get('volume', 0)
        if volume > 0 and volume > 1000000:  # Yüksek volume
            quality_score += 1
        
        # Kalite seviyesi
        if quality_score >= 7:
            return 'HIGH'
        elif quality_score >= 4:
            return 'MEDIUM'
        else:
            return 'LOW'

    def get_po3_analysis(self, symbol: str) -> Optional[Dict[str, Any]]:
        """
        Belirli bir sembol için Po3 analiz sonuçlarını döndürür.
        """
        return self.po3_data.get(symbol)

    def get_combined_bias(self, symbol: str, timeframe: str, 
                         candles: pd.DataFrame) -> Dict[str, Any]:
        """
        Geleneksel bias ile Po3 bias'ını birleştirerek kapsamlı bias analizi yapar.
        """
        # Geleneksel bias
        traditional_bias = self.determine_bias(symbol, timeframe, candles)
        
        # Po3 bias
        po3_analysis = self.analyze_power_of_three(symbol, candles)
        po3_bias_data = po3_analysis.get('po3_bias', {})
        
        # Bias'ları birleştir
        combined_score = 0
        
        # Geleneksel bias ağırlığı: %60
        combined_score += traditional_bias * 0.6
        
        # Po3 bias ağırlığı: %40
        po3_direction = po3_bias_data.get('direction', 'NEUTRAL')
        if po3_direction == 'BULLISH':
            combined_score += 1 * 0.4
        elif po3_direction == 'BEARISH':
            combined_score -= 1 * 0.4
        
        # Final bias
        if combined_score >= 0.5:
            final_bias = 'BULLISH'
        elif combined_score <= -0.5:
            final_bias = 'BEARISH'
        else:
            final_bias = 'NEUTRAL'
        
        # Confidence hesapla
        confidence = min(100, abs(combined_score) * 100)
        
        return {
            'symbol': symbol,
            'timeframe': timeframe,
            'final_bias': final_bias,
            'confidence': confidence,
            'traditional_bias': traditional_bias,
            'po3_bias': po3_bias_data,
            'combined_score': combined_score,
            'analysis_timestamp': datetime.now().isoformat()
        }

    def _empty_po3_result(self) -> Dict[str, Any]:
        """
        Boş Po3 analiz sonucu döner.
        """
        return {
            'symbol': '',
            'po3_phases': {},
            'po3_bias': {'direction': 'NEUTRAL', 'score': 0, 'confidence': 0, 'reasons': []},
            'intraday_signals': [],
            'quality': 'LOW',
            'daily_candle': {},
            'analysis_timestamp': datetime.now().isoformat()
        } 