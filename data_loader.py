# data_loader.py

import pandas as pd
from loguru import logger
import time
from typing import Optional, Dict, Any

# Varsayılan olarak bybit istemcisini import ediyoruz,
# gelecekte diğer borsalar için de soyutlama yapılabilir.
from bybit_client import BybitClient
from exceptions import APIConnectionError, InvalidDataError

class DataLoader:
    """
    Farklı veri kaynaklarından (borsalar, dosyalar vb.) mum verilerini
    çekmek ve standart bir formatta sunmak için sorumlu olan sınıf.
    """

    def __init__(self, bybit_client: BybitClient):
        """
        DataLoader'ı bir Bybit istemcisi ile başlatır.

        Args:
            bybit_client (BybitClient): Bybit borsası ile iletişim kuracak istemci.
        """
        if not bybit_client:
            raise ValueError("BybitClient örneği sağlanmalıdır.")
        self.bybit_client = bybit_client
        logger.info("DataLoader başlatıldı.")

    def get_candles(self, symbol: str, timeframe: str, limit: int = 200, max_retries: int = 3, 
                   backoff_factor: float = 0.5, session_context: str = None, 
                   extra_historical_data: bool = False) -> Optional[pd.DataFrame]:
        """
        Belirtilen sembol ve zaman dilimi için mum verilerini Bybit'ten alır.
        Hata durumunda yeniden deneme mekanizması içerir.
        
        YENİ: Killzone analizi için seans-bazlı dinamik limit ayarlaması ve
        yeni seans başlangıçları için ekstra geçmiş veri çekme özelliği.

        Args:
            symbol (str): Alınacak sembol (örn. 'BTCUSDT').
            timeframe (str): Zaman dilimi (örn. '240' for 4h).
            limit (int): Alınacak mum sayısı.
            max_retries (int): Hata durumunda maksimum yeniden deneme sayısı.
            backoff_factor (float): Yeniden denemeler arasındaki bekleme süresi çarpanı.
            session_context (str): Analiz edilecek seans ('london', 'newyork', 'asia', None).
            extra_historical_data (bool): Yeni seans başlangıcında daha fazla geçmiş veri çekmek için.

        Returns:
            pd.DataFrame: Mum verilerini içeren bir DataFrame veya hata durumunda None.
        """
        # Seans bazlı ve ekstra geçmiş veri ihtiyacına göre dinamik limit ayarlaması
        adjusted_limit = self._calculate_adjusted_limit(
            timeframe, limit, session_context, extra_historical_data
        )
        
        for attempt in range(max_retries):
            try:
                log_message = (
                    f"Mum verileri alınıyor: Sembol={symbol}, Zaman Dilimi={timeframe}, "
                    f"Limit={adjusted_limit} (Seans: {session_context}, Ekstra Veri: {extra_historical_data}) "
                    f"(Deneme {attempt + 1}/{max_retries})"
                )
                logger.debug(log_message)

                df = self.bybit_client.fetch_klines(symbol, timeframe, adjusted_limit)

                if df is not None and not df.empty:
                    actual_count = len(df)
                    df.attrs['symbol'] = symbol
                    df.attrs['timeframe'] = timeframe

                    # Minimum 3 mum kontrolü, analizlerin çoğu için temel bir gerekliliktir.
                    if actual_count < 3:
                        raise InvalidDataError(f"Analiz için kritik veri eksikliği: {symbol} {timeframe} - Alınan: {actual_count}, Gerekli: 3.")
                    
                    if actual_count < adjusted_limit:
                        logger.warning(f"VERİ EKSİKLİĞİ TESPİT EDİLDİ: {symbol} {timeframe} - İstenen: {adjusted_limit}, Alınan: {actual_count}")
                    
                    logger.info(f"Başarıyla {actual_count} adet mum verisi alındı: Sembol={symbol}, Zaman Dilimi={timeframe}")
                    return df
                else:
                    # API'den boş ama hatasız bir yanıt gelmesi durumu
                    raise InvalidDataError(f"Veri bulunamadı (API boş yanıt döndü): Sembol={symbol}, Zaman Dilimi={timeframe}")

            except APIConnectionError as e:
                logger.warning(f"Veri bağlantı hatası (Deneme {attempt + 1}/{max_retries}): {e}")
                if attempt >= max_retries - 1:
                    logger.critical(f"Maksimum deneme sayısına ulaşıldı. Veri alınamıyor: {symbol}")
                    raise  # Orijinal APIConnectionError'ı yeniden fırlat
                
                sleep_time = backoff_factor * (2 ** attempt)
                logger.info(f"{sleep_time} saniye sonra yeniden denenecek...")
                time.sleep(sleep_time)

            except Exception as e:
                # Diğer beklenmedik hatalar için
                logger.error(f"Mum verileri alınırken beklenmedik bir hata oluştu: {e}", exc_info=True)
                raise InvalidDataError(f"Veri işlenirken beklenmedik hata: {e}")

        # Döngüden bir şekilde çıkılırsa (normalde olmamalı), bu bir hata durumudur.
        raise RuntimeError(f"Veri alma döngüsü beklenmedik bir şekilde sonlandı: {symbol}")

    def get_latest_candle(self, symbol: str, timeframe: str = '1') -> Optional[Dict[str, Any]]:
        """
        Belirtilen sembol için sadece en son mumu çeker.
        """
        df = self.get_candles(symbol, timeframe, limit=1)
        if df is not None and not df.empty:
            return df.iloc[-1].to_dict()
        return None

    def _calculate_adjusted_limit(self, timeframe: str, base_limit: int, session_context: str, extra_historical: bool) -> int:
        """
        Seans ve geçmiş veri ihtiyacına göre dinamik limit hesaplar.
        """
        try:
            tf_minutes = self._timeframe_to_minutes(timeframe)
            adjusted_limit = base_limit

            # 1. Seans bazlı ek veri
            if session_context:
                session_requirements = {
                    'london': 12, 'newyork': 8, 'asia': 6, 'default': 4
                }
                extra_hours = session_requirements.get(session_context, session_requirements['default'])
                extra_candles_needed = (extra_hours * 60) // tf_minutes
                adjusted_limit += extra_candles_needed
                logger.debug(f"Seans-bazlı limit ayarı: {session_context} -> +{extra_candles_needed} mum")

            # 2. Yeni seans başlangıcı için ekstra geçmiş veri
            if extra_historical:
                # Önceki seansı tamamen kapsayacak kadar (örn. 12 saat) ek veri
                extra_historical_candles = (12 * 60) // tf_minutes
                adjusted_limit += extra_historical_candles
                logger.debug(f"Ekstra geçmiş veri ayarı: +{extra_historical_candles} mum")

            # Maksimum API limitini aşma
            max_limit = 1000
            final_limit = min(adjusted_limit, max_limit)
            
            if final_limit != base_limit:
                logger.info(f"Dinamik limit ayarlandı: Temel={base_limit}, Sonuç={final_limit}")
            
            return final_limit
            
        except Exception as e:
            logger.warning(f"Dinamik limit hesaplama hatası: {e}. Temel limit kullanılıyor: {base_limit}")
            return base_limit

    def _timeframe_to_minutes(self, timeframe: str) -> int:
        """Timeframe string'ini dakika cinsine çevirir."""
        timeframe_minutes = {
            '1': 1, '3': 3, '5': 5, '15': 15, '30': 30,
            '60': 60, '120': 120, '240': 240, '360': 360,
            '720': 720, 'D': 1440, '1440': 1440
        }
        return timeframe_minutes.get(str(timeframe), 15) # Tipi str yap

    def get_multiple_timeframes(self, symbol: str, timeframes: Dict[str, str], limit: int = 200) -> Dict[str, Optional[pd.DataFrame]]:
        """
        Birden fazla zaman dilimi için mum verilerini alır.
        """
        logger.info(f"[{symbol}] Multi-timeframe veri alınıyor: {timeframes}")
        results = {}
        for tf_name, tf_value in timeframes.items():
            try:
                df = self.get_candles(symbol, tf_value, limit)
                results[tf_name] = df
            except Exception as e:
                logger.error(f"[{symbol}] {tf_name} ({tf_value}) veri alma hatası: {e}")
                results[tf_name] = None
        return results

    def get_htf_ltf_data(self, symbol: str, htf_timeframe: str = '240', ltf_timeframe: str = '15', 
                        htf_limit: int = 200, ltf_limit: int = 200) -> Dict[str, Optional[pd.DataFrame]]:
        """
        HTF ve LTF verilerini alır.
        """
        htf_data = self.get_candles(symbol, htf_timeframe, htf_limit)
        ltf_data = self.get_candles(symbol, ltf_timeframe, ltf_limit)
        return {
            'htf': htf_data, 'ltf': ltf_data,
            'htf_timeframe': htf_timeframe, 'ltf_timeframe': ltf_timeframe
        }
