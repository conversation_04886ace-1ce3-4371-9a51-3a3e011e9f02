# displacement_analyzer.py

import pandas as pd
import numpy as np
from loguru import logger
from typing import List, Dict, Any, Optional
from datetime import datetime

class DisplacementAnalyzer:
    """
    ICT Displacement Analysis - Güçlü energik fiyat hareketlerini tespit eder.
    
    ICT Mantığı:
    - Displacement: Piyasanın hızlı ve güçlü hareket ettiği mumları tespit eder
    - Bu hareketler genellikle Smart Money'nin büyük pozisyon alımlarını gösterir
    - Displacement mumları sonrası genellikle FVG'ler oluşur
    - Trend değişikliklerinin erken sinyalleridir
    """
    
    def __init__(self, std_length: int = 100, std_multiplier: float = 4.0):
        """
        Displacement Analyzer'ı başlatır.
        
        Args:
            std_length: Standart sapma hesaplaması için mum sayısı
            std_multiplier: Standart sapmanın kaç katını displacement olarak kabul edeceğiz
        """
        self.std_length = std_length
        self.std_multiplier = std_multiplier
        logger.info(f"ICT Displacement Analyzer başlatıldı - STD Length: {std_length}, Multiplier: {std_multiplier}")
    
    def detect_displacement(self, candles: pd.DataFrame, require_fvg: bool = True, 
                          displacement_type: str = "Open to Close") -> List[Dict[str, Any]]:
        """
        ICT Displacement mumlarını tespit eder.
        
        Args:
            candles: Mum verileri (high, low, open, close, timestamp)
            require_fvg: Displacement için FVG gereksinimini zorla
            displacement_type: "Open to Close" veya "High to Low"
            
        Returns:
            Tespit edilen displacement'ların listesi
        """
        if candles is None or len(candles) < self.std_length + 3:
            logger.warning(f"Displacement analizi için yetersiz veri: {len(candles) if candles is not None else 0}")
            return []
        
        displacements = []
        
        try:
            # Mum range'lerini hesapla
            if displacement_type == "Open to Close":
                candle_ranges = np.abs(candles['close'] - candles['open'])
            else:  # High to Low
                candle_ranges = candles['high'] - candles['low']
            
            # Rolling standart sapma hesapla
            std_values = candle_ranges.rolling(window=self.std_length, min_periods=self.std_length).std()
            threshold_values = std_values * self.std_multiplier
            
            # Her mumu displacement için kontrol et
            for i in range(self.std_length, len(candles)):
                current_range = candle_ranges.iloc[i]
                threshold = threshold_values.iloc[i]
                
                if pd.isna(threshold) or threshold == 0:
                    continue
                
                # Displacement kontrolü
                is_displacement = current_range > threshold
                
                if is_displacement:
                    current_candle = candles.iloc[i]
                    
                    # FVG gereksinimi kontrolü
                    fvg_present = True
                    if require_fvg and i >= 2:
                        fvg_present = self._check_fvg_formation(candles, i)
                    
                    if not require_fvg or fvg_present:
                        # Displacement yönünü belirle
                        direction = self._determine_displacement_direction(current_candle)
                        
                        # Displacement gücünü hesapla
                        strength = (current_range / threshold) if threshold > 0 else 0
                        
                        displacement = {
                            'timestamp': current_candle['timestamp'],
                            'candle_index': i,
                            'type': 'displacement',
                            'direction': direction,
                            'strength': round(strength, 2),
                            'range_value': round(current_range, 6),
                            'threshold': round(threshold, 6),
                            'displacement_type': displacement_type,
                            'has_fvg': fvg_present,
                            'price_level': current_candle['close'],
                            'body_size': abs(current_candle['close'] - current_candle['open']),
                            'wick_size': (current_candle['high'] - current_candle['low']) - abs(current_candle['close'] - current_candle['open'])
                        }
                        
                        displacements.append(displacement)
                        
                        logger.debug(f"ICT Displacement tespit edildi @ {current_candle['timestamp']}: "
                                   f"{direction.upper()}, Güç: {strength:.2f}, FVG: {fvg_present}")
            
            # Kalite filtresini uygula
            quality_displacements = self._apply_quality_filter(displacements)
            
            logger.info(f"Toplam {len(quality_displacements)} kaliteli displacement tespit edildi")
            return quality_displacements
            
        except Exception as e:
            logger.error(f"Displacement analizi hatası: {str(e)}")
            return []
    
    def _check_fvg_formation(self, candles: pd.DataFrame, current_index: int) -> bool:
        """
        Displacement ile birlikte FVG oluşumunu kontrol eder.
        
        Args:
            candles: Mum verileri
            current_index: Şu anki mum indeksi
            
        Returns:
            FVG oluşumu var mı?
        """
        if current_index < 2:
            return False
        
        try:
            # 3 mum FVG kontrolü: prev_prev, prev, current
            prev_prev = candles.iloc[current_index - 2]
            prev = candles.iloc[current_index - 1]
            current = candles.iloc[current_index]
            
            # Bullish FVG: prev_prev['high'] < current['low']
            bullish_fvg = prev_prev['high'] < current['low']
            
            # Bearish FVG: prev_prev['low'] > current['high']
            bearish_fvg = prev_prev['low'] > current['high']
            
            return bullish_fvg or bearish_fvg
            
        except Exception as e:
            logger.error(f"FVG formation kontrolü hatası: {str(e)}")
            return False
    
    def _determine_displacement_direction(self, candle: pd.Series) -> str:
        """
        Displacement yönünü belirler.
        
        Args:
            candle: Mum verisi
            
        Returns:
            'bullish' veya 'bearish'
        """
        # Body yönü
        if candle['close'] > candle['open']:
            body_direction = 'bullish'
        elif candle['close'] < candle['open']:
            body_direction = 'bearish'
        else:
            # Doji durumu - wick'lere bak
            upper_wick = candle['high'] - max(candle['open'], candle['close'])
            lower_wick = min(candle['open'], candle['close']) - candle['low']
            body_direction = 'bullish' if upper_wick > lower_wick else 'bearish'
        
        return body_direction
    
    def _apply_quality_filter(self, displacements: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """
        Displacement'ları kalite kriterlerine göre filtreler.
        
        ICT Kalite Kriterleri:
        - Güç seviyesi (strength) yüksek olmalı
        - FVG ile beraber olanlar öncelikli
        - Body/wick oranı uygun olmalı
        
        Args:
            displacements: Tüm displacement'lar
            
        Returns:
            Filtrelenmiş kaliteli displacement'lar
        """
        if not displacements:
            return []
        
        quality_displacements = []
        
        for disp in displacements:
            # Kalite puanı hesapla
            quality_score = 0
            
            # Güç seviyesi puanı
            strength = disp.get('strength', 0)
            if strength >= 6.0:
                quality_score += 40
            elif strength >= 4.0:
                quality_score += 25
            elif strength >= 2.0:
                quality_score += 10
            
            # FVG varlığı puanı
            if disp.get('has_fvg', False):
                quality_score += 30
            
            # Body/Wick oranı puanı
            body_size = disp.get('body_size', 0)
            wick_size = disp.get('wick_size', 0)
            total_range = body_size + wick_size
            
            if total_range > 0:
                body_ratio = body_size / total_range
                if body_ratio >= 0.6:  # %60+ body
                    quality_score += 20
                elif body_ratio >= 0.4:  # %40+ body
                    quality_score += 10
            
            # Zaman yakınlığı puanı (son 20 mum içinde)
            # Bu kısım main.py'de candle_index bilgisiyle yapılabilir
            quality_score += 10  # Base puan
            
            disp['quality_score'] = quality_score
            
            # Minimum kalite puanı 50 ve üstü
            if quality_score >= 50:
                quality_displacements.append(disp)
        
        # Kalite puanına göre sırala
        quality_displacements.sort(key=lambda x: x['quality_score'], reverse=True)
        
        return quality_displacements
    
    def get_recent_displacements(self, displacements: List[Dict[str, Any]], 
                               lookback_candles: int = 20) -> List[Dict[str, Any]]:
        """
        Son N mum içindeki displacement'ları döndürür.
        
        Args:
            displacements: Tüm displacement'lar
            lookback_candles: Geriye bakılacak mum sayısı
            
        Returns:
            Son displacement'lar
        """
        if not displacements:
            return []
        
        # En son mum indeksini bul
        latest_index = max(disp.get('candle_index', 0) for disp in displacements)
        min_index = latest_index - lookback_candles
        
        recent_displacements = [
            disp for disp in displacements 
            if disp.get('candle_index', 0) >= min_index
        ]
        
        return recent_displacements
    
    def analyze_displacement_context(self, displacement: Dict[str, Any], 
                                   candles: pd.DataFrame, 
                                   swing_points: List[Dict[str, Any]] = None) -> Dict[str, Any]:
        """
        Displacement'ın ICT kontekstini analiz eder.
        
        Args:
            displacement: Analiz edilecek displacement
            candles: Mum verileri
            swing_points: Swing noktaları (opsiyonel)
            
        Returns:
            Displacement kontekst analizi
        """
        context = {
            'displacement_id': displacement.get('timestamp'),
            'trend_context': 'unknown',
            'liquidity_context': 'unknown',
            'confluence_score': 0
        }
        
        try:
            candle_index = displacement.get('candle_index', 0)
            
            # Trend kontekstini belirle
            if candle_index >= 20:
                recent_highs = candles.iloc[candle_index-20:candle_index]['high'].max()
                recent_lows = candles.iloc[candle_index-20:candle_index]['low'].min()
                current_price = displacement.get('price_level', 0)
                
                if current_price > (recent_highs + recent_lows) / 2:
                    context['trend_context'] = 'upper_range'
                else:
                    context['trend_context'] = 'lower_range'
            
            # Swing konteksti (eğer swing_points varsa)
            if swing_points:
                near_swing = self._find_nearest_swing(displacement, swing_points)
                if near_swing:
                    context['nearest_swing'] = near_swing
                    context['confluence_score'] += 15
            
            # FVG confluence
            if displacement.get('has_fvg', False):
                context['confluence_score'] += 25
            
            # Güç confluence
            if displacement.get('strength', 0) >= 5.0:
                context['confluence_score'] += 20
                
            context['confluence_score'] = min(context['confluence_score'], 100)
            
        except Exception as e:
            logger.error(f"Displacement kontekst analizi hatası: {str(e)}")
        
        return context
    
    def _find_nearest_swing(self, displacement: Dict[str, Any], 
                          swing_points: List[Dict[str, Any]]) -> Optional[Dict[str, Any]]:
        """
        Displacement'a en yakın swing noktasını bulur.
        
        Args:
            displacement: Displacement verisi
            swing_points: Swing noktaları
            
        Returns:
            En yakın swing noktası
        """
        if not swing_points:
            return None
        
        displacement_price = displacement.get('price_level', 0)
        min_distance = float('inf')
        nearest_swing = None
        
        for swing in swing_points:
            swing_price = swing.get('price', 0)
            distance = abs(displacement_price - swing_price)
            
            if distance < min_distance:
                min_distance = distance
                nearest_swing = swing
        
        return nearest_swing
