"""
Divergence analizi yapan modül.
Hidden divergence'ları tespit eder.
"""

import numpy as np
import pandas as pd
from loguru import logger
from utils import format_price_standard

class DivergenceAnalyzer:
    """Hidden divergence'ları tespit eden sınıf."""

    def __init__(self):
        """Divergence Analyzer sınıfını başlatır."""
        self.divergences = []

    def calculate_indicators(self, df):
        """
        Teknik göstergeleri hesaplar.

        Args:
            df (pd.DataFrame): Mum verileri DataFrame'i

        Returns:
            pd.DataFrame: Göstergeler eklenmiş DataFrame
        """
        # Veriyi kopyala
        df = df.copy()

        # EMA değerlerini hesapla
        df['ema26'] = self._calculate_ema(df['close'], 26)
        df['ema50'] = self._calculate_ema(df['close'], 50)
        df['ema100'] = self._calculate_ema(df['close'], 100)

        # RSI
        df['rsi'] = self._calculate_rsi(df['close'], window=14)

        # MACD
        df['macd'], df['macd_signal'], df['macd_hist'] = self._calculate_macd(df['close'], fast=12, slow=26, signal=9)

        # vwMACD (Volume Weighted MACD)
        df['vwmacd'] = self._calculate_vwmacd(df['close'], df['volume'], fast=12, slow=26)

        # Stochastic
        df['stoch'] = self._calculate_stochastic(df['high'], df['low'], df['close'], window=14, smooth=3)

        # CCI
        df['cci'] = self._calculate_cci(df['high'], df['low'], df['close'], window=14)

        # OBV (On Balance Volume)
        df['obv'] = self._calculate_obv(df['close'], df['volume'])

        # Momentum
        df['momentum'] = self._calculate_momentum(df['close'], window=10)

        # Chaikin Money Flow
        df['cmf'] = self._calculate_chaikin_money_flow(df['high'], df['low'], df['close'], df['volume'], window=21)

        # Money Flow Index
        df['mfi'] = self._calculate_money_flow_index(df['high'], df['low'], df['close'], df['volume'], window=14)

        return df

    def _calculate_ema(self, series, window):
        """EMA (Exponential Moving Average) hesaplar"""
        return series.ewm(span=window, adjust=False).mean()

    def _calculate_rsi(self, series, window=14):
        """RSI göstergesini hesaplar."""
        delta = series.diff()
        gain = delta.where(delta > 0, 0)
        loss = -delta.where(delta < 0, 0)

        # Boş seriler oluştur
        avg_gain = pd.Series(0.0, index=gain.index)
        avg_loss = pd.Series(0.0, index=loss.index)

        # İlk window tanesi için hesaplama
        first_avg_gain = gain.iloc[:window].mean()
        first_avg_loss = loss.iloc[:window].mean()

        # İlk değerleri ata
        avg_gain.iloc[window-1] = first_avg_gain
        avg_loss.iloc[window-1] = first_avg_loss

        # Kalan değerleri hesapla
        for i in range(window, len(gain)):
            avg_gain.iloc[i] = (avg_gain.iloc[i-1] * (window-1) + gain.iloc[i]) / window
            avg_loss.iloc[i] = (avg_loss.iloc[i-1] * (window-1) + loss.iloc[i]) / window

        rs = avg_gain / avg_loss
        rsi = 100 - (100 / (1 + rs))
        return rsi

    def _calculate_macd(self, series, fast=12, slow=26, signal=9):
        """MACD göstergesini hesaplar."""
        exp1 = series.ewm(span=fast, adjust=False).mean()
        exp2 = series.ewm(span=slow, adjust=False).mean()
        macd = exp1 - exp2
        signal_line = macd.ewm(span=signal, adjust=False).mean()
        histogram = macd - signal_line
        return macd, signal_line, histogram

    def _calculate_vwmacd(self, price, volume, fast=12, slow=26):
        """Volume Weighted MACD göstergesini hesaplar."""
        # Hacim ağırlıklı fiyat
        vw_price = price * volume

        # Hacim ağırlıklı EMA hesapla
        vw_ema_fast = vw_price.ewm(span=fast, adjust=False).mean() / volume.ewm(span=fast, adjust=False).mean()
        vw_ema_slow = vw_price.ewm(span=slow, adjust=False).mean() / volume.ewm(span=slow, adjust=False).mean()

        # VWMACD
        vwmacd = vw_ema_fast - vw_ema_slow
        return vwmacd

    def _calculate_stochastic(self, high, low, close, window=14, smooth=3):
        """Stochastic göstergesini hesaplar."""
        lowest_low = low.rolling(window=window).min()
        highest_high = high.rolling(window=window).max()

        # %K değeri (hızlı stochastic)
        k = 100 * ((close - lowest_low) / (highest_high - lowest_low))

        # %D değeri (yavaş stochastic) - şu anda kullanılmıyor ama ileride gerekebilir
        # d = k.rolling(window=smooth).mean()

        return k

    def _calculate_cci(self, high, low, close, window=14):
        """CCI göstergesini hesaplar."""
        tp = (high + low + close) / 3
        tp_sma = tp.rolling(window=window).mean()
        tp_mean_dev = tp.rolling(window=window).apply(lambda x: np.mean(np.abs(x - np.mean(x))))

        cci = (tp - tp_sma) / (0.015 * tp_mean_dev)
        return cci

    def _calculate_obv(self, close, volume):
        """On Balance Volume göstergesini hesaplar."""
        obv = pd.Series(index=close.index, dtype='float64')
        obv.iloc[0] = volume.iloc[0]

        for i in range(1, len(close)):
            if close.iloc[i] > close.iloc[i-1]:
                obv.iloc[i] = obv.iloc[i-1] + volume.iloc[i]
            elif close.iloc[i] < close.iloc[i-1]:
                obv.iloc[i] = obv.iloc[i-1] - volume.iloc[i]
            else:
                obv.iloc[i] = obv.iloc[i-1]

        return obv

    def _calculate_momentum(self, series, window=10):
        """Momentum göstergesini hesaplar."""
        momentum = series.diff(window)
        return momentum

    def _calculate_chaikin_money_flow(self, high, low, close, volume, window=21):
        """Chaikin Money Flow göstergesini hesaplar."""
        money_flow_multiplier = ((close - low) - (high - close)) / (high - low)
        money_flow_volume = money_flow_multiplier * volume

        cmf = money_flow_volume.rolling(window=window).sum() / volume.rolling(window=window).sum()
        return cmf

    def _calculate_money_flow_index(self, high, low, close, volume, window=14):
        """Money Flow Index göstergesini hesaplar."""
        # Tipik fiyat
        tp = (high + low + close) / 3

        # Paranın akış yönü
        money_flow = tp * volume

        # Pozitif ve negatif para akışı
        pmf = pd.Series(0, index=tp.index)
        nmf = pd.Series(0, index=tp.index)

        for i in range(1, len(tp)):
            if tp.iloc[i] > tp.iloc[i-1]:
                pmf.iloc[i] = money_flow.iloc[i]
            elif tp.iloc[i] < tp.iloc[i-1]:
                nmf.iloc[i] = money_flow.iloc[i]

        # Pozitif ve negatif para akışı toplamı
        pmf_sum = pmf.rolling(window=window).sum()
        nmf_sum = nmf.rolling(window=window).sum()

        # Money Ratio ve Money Flow Index
        money_ratio = pmf_sum / nmf_sum
        mfi = 100 - (100 / (1 + money_ratio))

        return mfi

    def detect_hidden_divergences(self, df, swing_points, max_points=15, max_bars=100, timeframe=None):
        """
        Hidden divergence'ları tespit eder.

        Args:
            df: DataFrame
            swing_points: Pivot noktaları
            max_points: Maksimum kontrole edilecek nokta
            max_bars: Maksimum mum sayısı
            timeframe: Zaman dilimi ("60", "240", "D" vb.)
        """
        # 1h zaman dilimini kontrol et ve atlama yap
        if timeframe == "60" or timeframe == "1h":
            logger.info("1h timeframe için divergence analizi atlanıyor.")
            return []

        self.divergences = []

        # Debug logları ekle
        logger.debug(f"Divergence analizi başlatılıyor. DataFrame boyutu: {df.shape}")
        logger.debug(f"Swing noktası sayısı: {len(swing_points)}")

        # Sadece tipi olan swing noktalarını al
        swings_with_type = [sp for sp in swing_points if sp.get('type')]
        logger.debug(f"Tipi olan swing noktası sayısı: {len(swings_with_type)}")

        # Son 15 swing noktasını al
        last_swings = swings_with_type[-15:] if len(swings_with_type) >= 15 else swings_with_type
        logger.debug(f"Son swing noktası sayısı: {len(last_swings)}")

        # Pivot high noktaları (HH ve LH)
        pivot_highs = [s for s in last_swings if s['type'] in ['HH', 'LH']]
        logger.debug(f"Pivot high sayısı: {len(pivot_highs)}")

        # Pivot low noktaları (LL ve HL)
        pivot_lows = [s for s in last_swings if s['type'] in ['LL', 'HL']]
        logger.debug(f"Pivot low sayısı: {len(pivot_lows)}")

        # Son pivot noktaları
        if len(pivot_highs) >= 2:
            logger.debug("Hidden Negative Divergence kontrolü yapılıyor...")
            self._check_hidden_negative_divergence(df, pivot_highs, max_bars, max_points)

        if len(pivot_lows) >= 2:
            logger.debug("Hidden Positive Divergence kontrolü yapılıyor...")
            self._check_hidden_positive_divergence(df, pivot_lows, max_bars, max_points)
        else:
            logger.debug("Hidden Positive Divergence kontrolü yapılmadı - Yetersiz pivot low noktası")

        # Divergence'ları TradingView benzeri şekilde filtreleme
        self._deduplicate_divergences_by_distance()

        logger.debug(f"Tespit edilen divergence sayısı: {len(self.divergences)}")

        return self.divergences

    def _check_hidden_positive_divergence(self, df, pivot_lows, max_bars, max_points=10):
        """
        Hidden Positive Divergence'ları kontrol eder.
        """
        # Debug log ekle
        logger.debug(f"Hidden Positive Divergence kontrolüne başlıyor. Pivot low sayısı: {len(pivot_lows)}")

        # Pivot lows listesi boşsa işlem yapma
        if len(pivot_lows) < 2:
            logger.debug("Yetersiz pivot low. En az 2 tane gerekli.")
            return

        # Son pivot'u referans al
        last_pl = pivot_lows[-1]

        # Maksimum kontrol edilecek pivot sayısı (son pivotun kendisi hariç)
        max_pivot_check = min(max_points, len(pivot_lows) - 1)

        # Debug logu
        logger.debug(f"Son pivot: {last_pl['price']}, Kontrol edilecek pivot sayısı: {max_pivot_check}")

        # Son pivotun fiyatı ve tarihini al
        last_price = last_pl['price']
        last_time = last_pl['timestamp']

        # Her bir pivot için bir kez kontrol et
        checked_pivots = set()  # Kontrol edilen pivotları takip et

        for i in range(1, max_pivot_check + 1):
            # Kontrol edilecek önceki pivot
            prev_pl = pivot_lows[-(i+1)]
            prev_price = prev_pl['price']
            prev_time = prev_pl['timestamp']

            # Pivot kombinasyonu kontrolü
            pivot_pair_key = f"{prev_time}_{last_time}"
            if pivot_pair_key in checked_pivots:
                logger.debug(f"Bu pivot çifti daha önce kontrol edildi, atlanıyor: {pivot_pair_key}")
                continue
            checked_pivots.add(pivot_pair_key)

            logger.debug(f"Kontrol: Pivot {i}, Fiyat: {prev_price}, Fark: {last_price - prev_price}")

            # Higher Low durumu kontrol et (fiyat yükselmiş mi?)
            if last_price > prev_price:  # Higher Low durumu
                # DataFrame'deki indeksleri bul
                if isinstance(df.index, pd.DatetimeIndex):
                    try:
                        # Tarih temelli indeksleri bul
                        last_idx = df.index.get_loc(pd.to_datetime(last_time))
                        prev_idx = df.index.get_loc(pd.to_datetime(prev_time))
                    except:
                        # Tam eşleşme bulunamazsa, yaklaşık indeksleri bul
                        df_dates = df.index.astype(str).tolist()
                        last_date = last_time.strftime('%Y-%m-%d %H:%M:%S')
                        prev_date = prev_time.strftime('%Y-%m-%d %H:%M:%S')

                        if last_date in df_dates:
                            last_idx = df_dates.index(last_date)
                        else:
                            for j, date in enumerate(df_dates):
                                if date > last_date:
                                    last_idx = max(0, j-1)
                                    break
                            else:
                                last_idx = len(df_dates) - 1

                        if prev_date in df_dates:
                            prev_idx = df_dates.index(prev_date)
                        else:
                            for j, date in enumerate(df_dates):
                                if date > prev_date:
                                    prev_idx = max(0, j-1)
                                    break
                            else:
                                prev_idx = len(df_dates) - 1
                else:
                    # Sayısal indeksler için yaklaşık değerler
                    last_idx = last_pl.get('index', df.shape[0] - 1)
                    prev_idx = prev_pl.get('index', max(0, last_idx - 10))

                # İndeks bulma sonucunu logla
                logger.debug(f"Bulunan indeksler: Son: {last_idx}, Önceki: {prev_idx}")

                # İndeksler arası mesafe kontrolü
                if abs(last_idx - prev_idx) > max_bars:
                    logger.debug(f"İndeksler arası mesafe ({abs(last_idx - prev_idx)}) çok fazla, atlanıyor")
                    continue

                # Göstergeleri kontrol et
                indicators = {
                    'RSI': 'rsi',
                    'MACD': 'macd',
                    'MACD Histogram': 'macd_hist',
                    'vwMACD': 'vwmacd',
                    'Stochastic': 'stoch',
                    'CCI': 'cci',
                    'OBV': 'obv',
                    'Momentum': 'momentum',
                    'CMF': 'cmf',
                    'MFI': 'mfi'
                }

                hidden_pos_divs = []

                for name, col in indicators.items():
                    if col in df.columns:
                        try:
                            # Gösterge değerlerini indekslerle al
                            last_indicator = df.iloc[last_idx][col]
                            prev_indicator = df.iloc[prev_idx][col]

                            # Debug logu
                            logger.debug(f"Gösterge: {name}, Son değer: {last_indicator}, Önceki değer: {prev_indicator}, Fark: {last_indicator - prev_indicator}")

                            # Gösterge düşmüş mü? (Lower Low)
                            if not np.isnan(last_indicator) and not np.isnan(prev_indicator) and last_indicator < prev_indicator:
                                # Divergence doğrulama
                                is_valid = self._validate_divergence(
                                    df=df,
                                    last_idx=last_idx,
                                    prev_idx=prev_idx,
                                    last_indicator=last_indicator,
                                    prev_indicator=prev_indicator,
                                    last_price=last_price,
                                    prev_price=prev_price,
                                    div_type="hidden_positive",
                                    indicator_col=name
                                )

                                if is_valid:
                                    hidden_pos_divs.append(name)
                                    logger.debug(f"Hidden Positive Divergence tespit edildi ve doğrulandı: {name}")
                                else:
                                    logger.debug(f"Hidden Positive Divergence tespit edildi fakat doğrulanamadı: {name}")
                        except Exception as e:
                            logger.error(f"{name} göstergesi kontrolünde hata: {str(e)}")
                            continue

                if hidden_pos_divs:
                    self.divergences.append({
                        'type': 'Hidden Positive Divergence',
                        'indicators': hidden_pos_divs,
                        'pivot_time': last_pl['timestamp']
                    })
                    logger.debug(f"Hidden Positive Divergence eklendi: {hidden_pos_divs}")

    def _check_hidden_negative_divergence(self, df, pivot_highs, max_bars, max_points=10):
        """
        Hidden Negative Divergence'ları kontrol eder.
        """
        # Debug log ekle
        logger.debug(f"Hidden Negative Divergence kontrolüne başlıyor. Pivot high sayısı: {len(pivot_highs)}")

        # Pivot highs listesi boşsa işlem yapma
        if len(pivot_highs) < 2:
            logger.debug("Yetersiz pivot high. En az 2 tane gerekli.")
            return

        # Son pivot'u referans al
        last_ph = pivot_highs[-1]

        # Maksimum kontrol edilecek pivot sayısı (son pivotun kendisi hariç)
        max_pivot_check = min(max_points, len(pivot_highs) - 1)

        # Debug logu
        logger.debug(f"Son pivot: {last_ph['price']}, Kontrol edilecek pivot sayısı: {max_pivot_check}")

        # Son pivotun fiyatı ve tarihini al
        last_price = last_ph['price']
        last_time = last_ph['timestamp']

        # Son pivotu sabit tutarak, önceki max_pivot_check kadar pivotu kontrol et
        for i in range(1, max_pivot_check + 1):
            # Kontrol edilecek önceki pivot
            prev_ph = pivot_highs[-(i+1)]
            prev_price = prev_ph['price']
            prev_time = prev_ph['timestamp']

            logger.debug(f"Kontrol: Pivot {i}, Fiyat: {prev_price}, Fark: {last_price - prev_price}")

            # Lower High durumu kontrol et (fiyat düşmüş mü?)
            if last_price < prev_price:  # Lower High durumu
                # DataFrame'in indeks türünü kontrol et
                if isinstance(df.index, pd.DatetimeIndex):
                    # Datetime indeksi için
                    try:
                        # DataFrame'in indeksindeki tarih/saatleri dizeye çevir
                        df_dates = df.index.astype(str).tolist()

                        # Pivot noktalarının tarih/saatlerini dizeye çevir
                        last_date = last_time.strftime('%Y-%m-%d %H:%M:%S')
                        prev_date = prev_time.strftime('%Y-%m-%d %H:%M:%S')

                        # Tam eşleşme bulamazsan en yakın tarihleri bul
                        if last_date in df_dates:
                            last_idx = df_dates.index(last_date)
                        else:
                            # En yakın tarih al
                            for i, date in enumerate(df_dates):
                                if date > last_date:
                                    last_idx = max(0, i-1)
                                    break
                            else:
                                last_idx = len(df_dates) - 1

                        if prev_date in df_dates:
                            prev_idx = df_dates.index(prev_date)
                        else:
                            # En yakın tarih al
                            for i, date in enumerate(df_dates):
                                if date > prev_date:
                                    prev_idx = max(0, i-1)
                                    break
                            else:
                                prev_idx = len(df_dates) - 1
                    except Exception as e:
                        logger.error(f"Datetime indeksi işlenirken hata: {str(e)}")
                        return
                else:
                    # Numerik indeks için basitleştirilmiş yaklaşım
                    # Pivot noktalarının indekslerini doğrudan kullan (veya tahmin et)
                    try:
                        # Pivot noktalarının indekslerini bul (örneğin, son 20 mum içinde)
                        last_idx = df.shape[0] - 1  # Son indeks

                        # Son pivot high'a en yakın indeksi bul
                        if 'index' in last_ph:
                            last_idx = min(last_ph['index'], last_idx)

                        # Önceki pivot high'a en yakın indeksi bul
                        if 'index' in prev_ph:
                            prev_idx = min(prev_ph['index'], last_idx - 1)
                        else:
                            # Tahmini indeks (son indeksten 10 mum önce)
                            prev_idx = max(0, last_idx - 10)
                    except Exception as e:
                        logger.error(f"Numerik indeks işlenirken hata: {str(e)}")
                        return

                # Debug logu
                logger.debug(f"Bulunan indeksler: Son: {last_idx}, Önceki: {prev_idx}")

                # İndeksler arası mesafe kontrolü
                if abs(last_idx - prev_idx) > max_bars:
                    logger.debug("İndeksler arası mesafe çok fazla, atlanıyor")
                    return

                indicators = {
                    'RSI': 'rsi',
                    'MACD': 'macd',
                    'MACD Histogram': 'macd_hist',
                    'vwMACD': 'vwmacd',
                    'Stochastic': 'stoch',
                    'CCI': 'cci',
                    'OBV': 'obv',
                    'Momentum': 'momentum',
                    'CMF': 'cmf',
                    'MFI': 'mfi'
                }

                hidden_neg_divs = []

                for name, col in indicators.items():
                    if col in df.columns:
                        try:
                            # Gösterge değerlerini indekslerle al
                            last_indicator = df.iloc[last_idx][col]
                            prev_indicator = df.iloc[prev_idx][col]

                            # Debug logu
                            logger.debug(f"Gösterge: {name}, Son değer: {last_indicator}, Önceki değer: {prev_indicator}")

                            # Gösterge yükselmiş mi? (Higher High)
                            if not np.isnan(last_indicator) and not np.isnan(prev_indicator) and last_indicator > prev_indicator:
                                # Divergence doğrulama
                                is_valid = self._validate_divergence(
                                    df=df,
                                    last_idx=last_idx,
                                    prev_idx=prev_idx,
                                    last_indicator=last_indicator,
                                    prev_indicator=prev_indicator,
                                    last_price=last_price,
                                    prev_price=prev_price,
                                    div_type="hidden_negative",
                                    indicator_col=name
                                )

                                if is_valid:
                                    hidden_neg_divs.append(name)
                                    logger.debug(f"Hidden Negative Divergence tespit edildi ve doğrulandı: {name}")
                                else:
                                    logger.debug(f"Hidden Negative Divergence tespit edildi fakat doğrulanamadı: {name}")
                        except Exception as e:
                            logger.error(f"{name} göstergesi kontrolünde hata: {str(e)}")
                            continue

                if hidden_neg_divs:
                    self.divergences.append({
                        'type': 'Hidden Negative Divergence',
                        'indicators': hidden_neg_divs,
                        'pivot_time': last_ph['timestamp']
                    })
                    logger.debug(f"Hidden Negative Divergence eklendi: {hidden_neg_divs}")

    def _find_nearest_timestamp(self, index, timestamp):
        """
        Verilen timestamp'e en yakın indeks elemanını bulur.
        """
        try:
            # İndeks türünü kontrol et
            if not isinstance(index, pd.DatetimeIndex):
                logger.debug(f"İndeks DatetimeIndex türünde değil. Tür: {type(index)}")
                # Normal liste indeksleme kullan, timestamp'i yoksay
                return None

            # Timestamp türünü kontrol et ve dönüştür
            if not isinstance(timestamp, pd.Timestamp):
                try:
                    timestamp = pd.Timestamp(timestamp)
                except:
                    logger.debug(f"Timestamp dönüştürülemedi: {timestamp}, Tür: {type(timestamp)}")
                    return None

            # Önce tam eşleşmeyi dene
            if timestamp in index:
                return timestamp

            # Indeksin boş olup olmadığını kontrol et
            if len(index) == 0:
                return None

            # En yakın timestamp'i bul
            nearest_idx = index.get_indexer([timestamp], method='nearest')[0]

            # Geçersiz indeks (-1) dönerse None döndür
            if nearest_idx == -1:
                return None

            nearest_timestamp = index[nearest_idx]

            # Zaman farkını kontrol et (fazla uzaksa None döndür)
            time_diff = abs((nearest_timestamp - timestamp).total_seconds())
            if time_diff > 86400:  # 24 saat (saniye cinsinden)
                logger.debug(f"En yakın timestamp çok uzak: {timestamp} -> {nearest_timestamp}, fark: {time_diff} saniye")
                return None

            return nearest_timestamp

        except Exception as e:
            logger.error(f"En yakın timestamp bulunurken hata: {str(e)}")
            return None

    def _validate_divergence(self, df, last_idx, prev_idx, last_indicator, prev_indicator, last_price, prev_price, div_type, indicator_col=None):
        """
        İki pivot noktası arasındaki tüm noktaları kontrol ederek divergence'ı doğrular.
        TradingView'daki "sanal çizgiler" yaklaşımını kullanır.

        Args:
            df: DataFrame
            last_idx: Son pivot noktasının indeksi
            prev_idx: Önceki pivot noktasının indeksi
            last_indicator: Son pivot noktasındaki gösterge değeri
            prev_indicator: Önceki pivot noktasındaki gösterge değeri
            last_price: Son pivot noktasındaki fiyat
            prev_price: Önceki pivot noktasındaki fiyat
            div_type: Divergence tipi ("hidden_positive" veya "hidden_negative")
            indicator_col: Gösterge sütun adı (None ise otomatik tespit edilir)

        Returns:
            bool: Divergence geçerli mi?
        """
        # İndeksler arası mesafeyi hesapla
        if last_idx == prev_idx:
            return False

        # Küçük ve büyük indeksleri belirle
        min_idx = min(last_idx, prev_idx)
        max_idx = max(last_idx, prev_idx)
        distance = max_idx - min_idx

        if distance <= 1:  # Arada kontrol edilecek nokta yoksa
            return True

        # Eğimler ve sanal çizgiler
        indicator_slope = (last_indicator - prev_indicator) / distance
        price_slope = (last_price - prev_price) / distance

        # Başlangıç değerleri
        virtual_indicator = prev_indicator
        virtual_price = prev_price

        # Her bir ara noktayı kontrol et
        for i in range(min_idx + 1, max_idx):
            # Sanal çizgileri güncelle
            virtual_indicator += indicator_slope
            virtual_price += price_slope

            # Gerçek değerler
            try:
                # Gösterge değerini doğrudan kullanmak yerine, ilgili gösterge sütununu kullan
                # Bu fonksiyon her gösterge için ayrı ayrı çağrılacak, bu nedenle last_indicator ve prev_indicator
                # değerleri zaten doğru gösterge için hesaplanmış olacak
                actual_price = df.iloc[i]['close']

                # Gösterge değerini hesapla
                if indicator_col and indicator_col in df.columns:
                    # Belirtilen gösterge sütununu kullan
                    try:
                        actual_indicator = df.iloc[i][indicator_col]
                        if np.isnan(actual_indicator):
                            # NaN değer varsa, bu noktayı atla
                            continue
                    except:
                        # Hata oluşursa, bu noktayı atla
                        continue
                else:
                    # Gösterge sütunu belirtilmemişse, tüm gösterge sütunlarını dene
                    indicator_cols = ['rsi', 'macd', 'macd_hist', 'vwmacd', 'stoch', 'cci', 'obv', 'momentum', 'cmf', 'mfi']

                    # Gösterge değerini al
                    for col in indicator_cols:
                        if col in df.columns:
                            try:
                                actual_indicator = df.iloc[i][col]
                                if not np.isnan(actual_indicator):
                                    # Gösterge sütununu kaydet (sonraki kullanımlar için)
                                    indicator_col = col
                                    break
                            except:
                                continue
                    else:
                        # Hiçbir gösterge sütunu bulunamadıysa, bu noktayı atla
                        continue

            except Exception as e:
                logger.error(f"Divergence doğrulama sırasında hata: {str(e)}")
                # Veri yoksa veya hata oluşursa, bu noktayı atla
                continue

            # Divergence tipine göre kontrol
            if div_type == "hidden_positive":
                # Hidden Positive Divergence: Fiyat yükselirken (Higher Low) gösterge düşüyor (Lower Low)
                # Fiyat sanal çizginin altına düşmemeli, gösterge sanal çizginin üstüne çıkmamalı
                if actual_price < virtual_price or actual_indicator > virtual_indicator:
                    logger.debug(f"Divergence doğrulama başarısız: Fiyat={actual_price}, Sanal Fiyat={virtual_price}, Gösterge={actual_indicator}, Sanal Gösterge={virtual_indicator}")
                    return False

            elif div_type == "hidden_negative":
                # Hidden Negative Divergence: Fiyat düşerken (Lower High) gösterge yükseliyor (Higher High)
                # Fiyat sanal çizginin üstüne çıkmamalı, gösterge sanal çizginin altına düşmemeli
                if actual_price > virtual_price or actual_indicator < virtual_indicator:
                    logger.debug(f"Divergence doğrulama başarısız: Fiyat={actual_price}, Sanal Fiyat={virtual_price}, Gösterge={actual_indicator}, Sanal Gösterge={virtual_indicator}")
                    return False

        logger.debug("Divergence doğrulama başarılı")
        return True

    def _deduplicate_divergences_by_distance(self):
        """
        TradingView benzeri şekilde, aynı pivot noktalarındaki divergence'ları birleştirir.
        """
        # Mesafelere göre gruplandırılmış divergence'lar
        pivot_groups = {}

        for div in self.divergences:
            # Pivot zamanını anahtar olarak kullan
            pivot_time = div['pivot_time']
            pivot_key = str(pivot_time)
            div_type = div['type']

            # İlgili tipteki divergence gruplarına ekle
            if pivot_key not in pivot_groups:
                pivot_groups[pivot_key] = {
                    'Hidden Positive Divergence': [],
                    'Hidden Negative Divergence': []
                }

            pivot_groups[pivot_key][div_type].append(div)

        # Filtrelenmiş liste oluştur
        unique_divergences = []

        for pivot_key, div_types in pivot_groups.items():
            # Her tip için en iyi divergence'ı seç
            for div_type, divs in div_types.items():
                if not divs:
                    continue

                # En iyi divergence'ı seç (gösterge sayısına göre)
                best_div = None
                max_indicators = 0

                for div in divs:
                    num_indicators = len(div['indicators'])
                    if num_indicators > max_indicators:
                        max_indicators = num_indicators
                        best_div = div

                if best_div:
                    # Tüm göstergeleri birleştir
                    all_indicators = []
                    for div in divs:
                        all_indicators.extend(div['indicators'])

                    # Benzersiz göstergeler
                    unique_indicators = list(set(all_indicators))

                    # En iyi divergence'ı güncelle ve listeye ekle
                    best_div['indicators'] = unique_indicators
                    unique_divergences.append(best_div)

        # Filtrelenmiş listeyi atama
        self.divergences = unique_divergences