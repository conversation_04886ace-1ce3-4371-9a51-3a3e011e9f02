"""
Custom exceptions for the trading automaton.
"""

class AutomatonError(Exception):
    """Base class for exceptions in this application."""
    pass

class InvalidDataError(AutomatonError):
    """Raised when input data is invalid or missing."""
    pass

class APIConnectionError(AutomatonError):
    """Raised for errors related to API connections."""
    pass

class ConfigurationError(AutomatonError):
    """Raised for configuration-related errors."""
    pass

class CalculationError(AutomatonError):
    """Raised during an error in a technical calculation."""
    pass