# fvg_analyzer.py

import pandas as pd
from loguru import logger
from typing import List, Dict, Any, Optional
from exceptions import InvalidDataError

class FvgAnalyzer:
    """
    ICT konseptlerine göre Fair Value Gap (FVG) bölgelerini tespit eder.
    
    Bir FVG, üç mumluk bir sekans ile tanımlanır:
    - Bullish FVG: 1. mumun yükseği ile 3. mumun düşüğü arasında bir boşluk vardır.
    - Bearish FVG: 1. mumun düşüğü ile 3. mumun yükseği arasında bir boşluk vardır.
    """

    def find_fvgs(self, candles: pd.DataFrame, swing_points: Optional[List[Dict[str, Any]]] = None) -> List[Dict[str, Any]]:
        """
        Verilen mum verilerinde FVG'leri bulur ve EQ bölgelerini hesaplar.
        Swing noktaları verilmişse, sadece o aralıklarda FVG arar.

        Args:
            candles: Mum verilerini içeren Pandas DataFrame.
            swing_points: PivotAnalyzer'dan gelen swing noktalarının listesi (opsiyonel).
        
        Returns:
            Tespit edilen FVG'lerin listesi.
        """
        fvgs = []
        if candles is None or len(candles) < 3:
            symbol = getattr(candles, 'attrs', {}).get('symbol', 'Bilinmiyor')
            timeframe = getattr(candles, 'attrs', {}).get('timeframe', 'Bilinmiyor')
            raise InvalidDataError(f"FVG analizi için yetersiz mum verisi (< 3) - Sembol: {symbol}, Zaman Dilimi: {timeframe}")

        # YENİ MANTIK: swing_points varsa, sadece swing aralıklarında ara.
        if swing_points and len(swing_points) >= 2:
            logger.info(f"ICT FVG Analizi: {len(swing_points)} swing noktası aralığında FVG aranıyor.")
            fvgs = self._find_fvgs_in_swing_ranges(candles, swing_points)
        else:
            # Swing noktaları yoksa veya yetersizse, sadece yeterli veri varsa tam tarama yap.
            if len(candles) > 50: # Örneğin, 50 mumdan fazla veri varsa tam tarama yap
                logger.warning("ICT FVG Uyarısı: Swing noktaları sağlanmadı, tam tarama yapılıyor.")
                fvgs = self._find_fvgs_full_scan(candles)
            else:
                logger.warning("ICT FVG Uyarısı: Swing noktaları sağlanmadı ve veri sayısı az (<50), tam tarama atlanıyor.")

        # FVG'lerin doldurulup doldurulmadığını kontrol et
        self._check_fvg_filled(fvgs, candles)
        
        # FVG'leri kaliteye göre filtrele
        fvgs = self._apply_ict_quality_filter(fvgs, swing_points)

        logger.info(f"Toplam {len(fvgs)} FVG bulundu.")
        return fvgs

    def _find_fvgs_in_swing_ranges(self, candles: pd.DataFrame, swing_points: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """
        ICT prensiplerine uygun olarak, anlamlı swing noktaları arasında FVG tespit eder.
        
        ICT Mantığı:
        - Yüksek olasılıklı FVG'ler, önemli swing noktaları arasında oluşur
        - Piyasa genellikle bu "değer boşluklarını" doldurmak için geri döner
        - Trend yönünde oluşan FVG'ler daha yüksek olasılıkla doldurulur
        
        Args:
            candles: Mum verileri
            swing_points: Swing noktaları listesi
            
        Returns:
            Tespit edilen FVG'lerin listesi
        """
        fvgs = []
        
        # Geçerli swing noktalarını filtrele ve sırala
        valid_swings = [sp for sp in swing_points if sp.get('type')]
        if len(valid_swings) < 2:
            logger.warning(f"ICT FVG analizi: Yetersiz swing noktası ({len(valid_swings)}, en az 2 gerekli)")
            return fvgs
        elif len(valid_swings) == 2:
            logger.info(f"ICT FVG analizi: Minimum swing noktası ({len(valid_swings)}) - Sınırlı analiz yapılacak")
        else:
            logger.info(f"ICT FVG analizi: Optimal swing noktası ({len(valid_swings)}) - Tam analiz yapılacak")
            
        # Swing noktalarını zaman sırasına göre sırala
        sorted_swings = sorted(valid_swings, key=lambda x: x['timestamp'])
        
        # ICT Optimizasyonu: Swing sayısına göre analiz derinliği ayarla
        if len(sorted_swings) >= 5:
            relevant_swings = sorted_swings[-5:]  # Son 5 swing - optimal analiz
            analysis_quality = "optimal"
        elif len(sorted_swings) >= 3:
            relevant_swings = sorted_swings[-3:]  # Son 3 swing - iyi analiz
            analysis_quality = "good"
        else:
            relevant_swings = sorted_swings  # Tüm swing'ler - sınırlı analiz
            analysis_quality = "limited"
            
        logger.debug(f"ICT FVG analiz kalitesi: {analysis_quality} ({len(relevant_swings)} swing)")
        
        # Her swing çifti için FVG ara
        for i in range(len(relevant_swings) - 1):
            start_swing = relevant_swings[i]
            end_swing = relevant_swings[i+1]
            
            # Trend yönünü belirle
            trend_direction = 'bullish' if end_swing['price'] > start_swing['price'] else 'bearish'
            
            # Swing indeksleri (candles DataFrame içindeki konumlar)
            start_index = start_swing.get('candle_index', start_swing.get('index', 0))
            end_index = end_swing.get('candle_index', end_swing.get('index', 0))
            
            # Aralığı belirle (küçükten büyüğe)
            min_index = min(start_index, end_index)
            max_index = max(start_index, end_index)
            
            swing_range_info = f"{start_swing['type']} ({start_swing['price']}) -> {end_swing['type']} ({end_swing['price']})"
            logger.debug(f"ICT FVG aralığı kontrol ediliyor: {swing_range_info}")
            
            # Belirtilen aralıktaki mumları filtrele
            if 'index' in candles.columns:
                range_candles = candles[(candles['index'] >= min_index) & (candles['index'] <= max_index)]
            else:
                # iloc kullanarak indeks aralığını filtrele
                range_candles = candles.iloc[min_index:max_index+1]
            
            if len(range_candles) < 3:
                logger.debug(f"ICT FVG aralığı çok dar: {len(range_candles)} mum")
                continue
                
            # Aralıktaki mumları 3'lü gruplar halinde incele
            for i in range(2, len(range_candles)):
                prev_prev_candle = range_candles.iloc[i - 2]
                prev_candle = range_candles.iloc[i - 1]
                current_candle = range_candles.iloc[i]
                
                # ICT Bullish FVG: Önceki 2. mumun düşüğü > şimdiki mumun yükseği
                if prev_prev_candle['low'] > current_candle['high']:
                    fvg_top = prev_prev_candle['low']
                    fvg_bottom = current_candle['high']
                    fvg_eq = (fvg_top + fvg_bottom) / 2  # EQ bölgesi (Equal Quadrant)
                    
                    # FVG büyüklüğünü kontrol et - çok küçük FVG'leri filtrele
                    fvg_size = fvg_top - fvg_bottom
                    fvg_size_percent = (fvg_size / fvg_bottom) * 100
                    
                    # Çok küçük FVG'leri filtrele (<%0.1)
                    if fvg_size_percent < 0.1:
                        continue
                    
                    fvgs.append({
                        'type': 'bullish',
                        'top': fvg_top,
                        'bottom': fvg_bottom,
                        'eq': fvg_eq,
                        'timestamp': prev_candle['timestamp'],
                        'filled': False,
                        'swing_range': swing_range_info,
                        'with_trend': trend_direction == 'bullish'  # Trend yönüyle uyumlu mu?
                    })
                    logger.debug(f"ICT Bullish FVG tespit edildi @ {prev_candle['timestamp']}: "
                               f"Top={fvg_top}, Bottom={fvg_bottom}, EQ={fvg_eq}, "
                               f"Size={fvg_size_percent:.2f}%, {swing_range_info}")
                
                # ICT Bearish FVG: Önceki 2. mumun yükseği < şimdiki mumun düşüğü
                elif prev_prev_candle['high'] < current_candle['low']:
                    fvg_top = current_candle['low']
                    fvg_bottom = prev_prev_candle['high']
                    fvg_eq = (fvg_top + fvg_bottom) / 2  # EQ bölgesi (Equal Quadrant)
                    
                    # FVG büyüklüğünü kontrol et - çok küçük FVG'leri filtrele
                    fvg_size = fvg_top - fvg_bottom
                    fvg_size_percent = (fvg_size / fvg_bottom) * 100
                    
                    # Çok küçük FVG'leri filtrele (<%0.1)
                    if fvg_size_percent < 0.1:
                        continue
                    
                    fvgs.append({
                        'type': 'bearish',
                        'top': fvg_top,
                        'bottom': fvg_bottom,
                        'eq': fvg_eq,
                        'timestamp': prev_candle['timestamp'],
                        'filled': False,
                        'swing_range': swing_range_info,
                        'with_trend': trend_direction == 'bearish'  # Trend yönüyle uyumlu mu?
                    })
                    logger.debug(f"ICT Bearish FVG tespit edildi @ {prev_candle['timestamp']}: "
                               f"Top={fvg_top}, Bottom={fvg_bottom}, EQ={fvg_eq}, "
                               f"Size={fvg_size_percent:.2f}%, {swing_range_info}")
        
        return fvgs
    def _find_fvgs_full_scan(self, candles: pd.DataFrame) -> List[Dict[str, Any]]:
        """
        ICT öncesi sistem: Tüm mumları tarayarak FVG tespit eder.
        Bu yöntem artık önerilmez, yerine swing tabanlı analiz kullanın.
        
        Args:
            candles: Mum verileri
            
        Returns:
            Tespit edilen FVG'lerin listesi
        """
        fvgs = []
        logger.warning("ICT FVG Uyarı: Eski sistem (tam tarama) kullanılıyor - "
                     "Swing noktalarıyla birlikte çağırın")
        
        # Mumları 3'lü gruplar halinde kontrol et
        for i in range(2, len(candles)):
            prev_prev_candle = candles.iloc[i - 2]
            prev_candle = candles.iloc[i - 1]
            current_candle = candles.iloc[i]
            
            # Bullish FVG: Önceki 2. mumun düşüğü > şimdiki mumun yükseği
            if prev_prev_candle['low'] > current_candle['high']:
                fvg_top = prev_prev_candle['low']
                fvg_bottom = current_candle['high']
                fvg_eq = (fvg_top + fvg_bottom) / 2
                
                fvgs.append({
                    'type': 'bullish',
                    'top': fvg_top,
                    'bottom': fvg_bottom,
                    'eq': fvg_eq,
                    'timestamp': prev_candle['timestamp'],
                    'filled': False,
                    'swing_range': 'tam_tarama'  # Swing aralığı yok
                })
                
            # Bearish FVG: Önceki 2. mumun yükseği < şimdiki mumun düşüğü
            elif prev_prev_candle['high'] < current_candle['low']:
                fvg_top = current_candle['low']
                fvg_bottom = prev_prev_candle['high']
                fvg_eq = (fvg_top + fvg_bottom) / 2
                
                fvgs.append({
                    'type': 'bearish',
                    'top': fvg_top,
                    'bottom': fvg_bottom,
                    'eq': fvg_eq,
                    'timestamp': prev_candle['timestamp'],
                    'filled': False,
                    'swing_range': 'tam_tarama'  # Swing aralığı yok
                })
        
        return fvgs
    
    def _check_fvg_filled(self, fvgs: List[Dict[str, Any]], candles: pd.DataFrame) -> None:
        """
        FVG'lerin sonraki mumlar tarafından doldurulup doldurulmadığını kontrol eder.
        
        ICT Mantığı:
        - FVG'ler genellikle fiyat tarafından "doldurulur" (mitigate edilir)
        - Doldurulmamış FVG'ler gelecekteki potansiyel hedef bölgelerdir
        
        Args:
            fvgs: FVG listesi
            candles: Mum verileri
        """
        if not fvgs or candles is None or candles.empty:
            return
        
        # Son kapanış fiyatı
        last_price = candles.iloc[-1]['close']
        
        for fvg in fvgs:
            fvg_timestamp = fvg['timestamp']
            fvg_type = fvg['type']
            fvg_top = fvg['top']
            fvg_bottom = fvg['bottom']
            
            # FVG oluşumundan sonraki mumları bul
            try:
                timestamp_index = candles[candles['timestamp'] == fvg_timestamp].index
                if len(timestamp_index) == 0:
                    continue
                fvg_index = timestamp_index[0]
                future_candles = candles.loc[candles.index > fvg_index]
            except Exception as e:
                logger.error(f"ICT FVG doldurulma kontrolünde hata: {str(e)}")
                continue
            
            # FVG'nin doldurulup doldurulmadığını kontrol et
            if fvg_type == 'bullish':
                # Bullish FVG, herhangi bir mum FVG aralığına girdiğinde doldurulmuş sayılır
                is_filled = any(future_candles['low'] <= fvg_top) or last_price >= fvg_top
                
                # Kısmen doldurulma durumu için detay ekle
                if is_filled:
                    # Doldurulma yüzdesini hesapla
                    lowest_point = min(future_candles['low'].min(), last_price)
                    if lowest_point < fvg_bottom:
                        fill_percent = 100  # Tam dolduruldu
                    else:
                        fill_range = fvg_top - fvg_bottom
                        filled_amount = fvg_top - lowest_point
                        fill_percent = (filled_amount / fill_range) * 100 if fill_range > 0 else 0
                    
                    fvg['fill_percent'] = fill_percent
                    logger.debug(f"ICT Bullish FVG dolduruldu @ {fvg_timestamp}: %{fill_percent:.1f}")
            else:  # bearish
                # Bearish FVG, herhangi bir mum FVG aralığına girdiğinde doldurulmuş sayılır
                is_filled = any(future_candles['high'] >= fvg_bottom) or last_price <= fvg_bottom
                
                # Kısmen doldurulma durumu için detay ekle
                if is_filled:
                    # Doldurulma yüzdesini hesapla
                    highest_point = max(future_candles['high'].max(), last_price)
                    if highest_point > fvg_top:
                        fill_percent = 100  # Tam dolduruldu
                    else:
                        fill_range = fvg_top - fvg_bottom
                        filled_amount = highest_point - fvg_bottom
                        fill_percent = (filled_amount / fill_range) * 100 if fill_range > 0 else 0
                    
                    fvg['fill_percent'] = fill_percent
                    logger.debug(f"ICT Bearish FVG dolduruldu @ {fvg_timestamp}: %{fill_percent:.1f}")
            
            fvg['filled'] = is_filled
    
    def _apply_ict_quality_filter(self, fvgs: List[Dict[str, Any]], swing_points: Optional[List[Dict[str, Any]]] = None) -> List[Dict[str, Any]]:
        """
        ICT prensipleri doğrultusunda FVG'leri filtreler.
        
        ICT Mantığı:
        - Trend yönündeki FVG'ler daha yüksek olasılıkla doldurulur
        - Swing aralıklarındaki FVG'ler daha anlamlıdır
        - Çok küçük FVG'ler önemsizdir
        
        Args:
            fvgs: Tespit edilen FVG'ler listesi
            swing_points: Opsiyonel swing noktaları listesi
            
        Returns:
            Filtrelenmiş yüksek kaliteli FVG'ler listesi
        """
        if not fvgs:
            return []

        valid_swings = [sp for sp in swing_points if sp.get('type')] if swing_points else []
        
        # Önce tüm FVG'leri fiyata göre sırala (son fiyat hareketleri öncelikli)
        sorted_fvgs = sorted(fvgs, key=lambda x: x['timestamp'], reverse=True)
        
        # Kaliteli FVG'leri seç
        quality_fvgs = []
        for fvg in sorted_fvgs:
            # Doldurulmuş FVG'leri atla (opsiyonel)
            # if fvg.get('filled', False):
            #    continue
            
            # Trend yönündeki FVG'leri önceliklendir
            with_trend = fvg.get('with_trend', False)
            
            # Swing aralığında olmayanları düşük önceliklendir
            in_swing_range = fvg.get('swing_range', '') != 'tam_tarama'
            
            # Kalite puanı hesapla (0-100)
            quality_score = 0
            if with_trend:
                quality_score += 30  # Trend yönünde +30 puan
                
            if in_swing_range:
                quality_score += 40  # Swing aralığında +40 puan
                
            # Doldurulma durumuna göre puan ver
            if not fvg.get('filled', False):
                quality_score += 30  # Doldurulmamış +30 puan
            elif fvg.get('fill_percent', 100) < 50:
                quality_score += 15  # %50'den az doldurulmuş +15 puan
                
            # Swing sayısına göre bonus/ceza
            swing_count = len(valid_swings) if valid_swings else 0
            if swing_count >= 5:
                quality_score += 10  # Optimal swing sayısı +10 puan
            elif swing_count == 2:
                quality_score -= 10  # Minimum swing sayısı -10 puan
                
            # Kalite puanını FVG'ye ekle
            fvg['quality_score'] = quality_score
            
            # Swing sayısına göre minimum kalite puanı ayarla
            swing_count = len(valid_swings) if valid_swings else 0
            min_quality = 20 if swing_count == 2 else 30  # 2 swing için daha düşük eşik
            
            if quality_score >= min_quality:
                quality_fvgs.append(fvg)
        
        # Son FVG sayısını logla
        logger.info(f"ICT FVG kalite filtresi uygulandı: {len(sorted_fvgs)} -> {len(quality_fvgs)}")
        
        return quality_fvgs
    
    def get_highest_quality_fvgs(self, fvgs: List[Dict[str, Any]], limit: int = 3) -> List[Dict[str, Any]]:
        """
        En yüksek kalitedeki FVG'leri döndürür.
        
        Args:
            fvgs: Tüm FVG'ler listesi
            limit: Döndürülecek maksimum FVG sayısı
            
        Returns:
            En yüksek kalite puanına sahip FVG'ler
        """
        if not fvgs:
            return []
            
        # Kalite puanına göre sırala
        sorted_by_quality = sorted(fvgs, key=lambda x: x.get('quality_score', 0), reverse=True)
        
        # Belirlenen limitte döndür
        return sorted_by_quality[:limit]

    def detect_displacement_fvgs(self, candles: pd.DataFrame, 
                               displacements: List[Dict[str, Any]] = None) -> List[Dict[str, Any]]:
        """
        Displacement ile birlikte oluşan yüksek kaliteli FVG'leri tespit eder.
        
        ICT Mantığı:
        - Displacement mumları genellikle FVG oluşumuna neden olur
        - Bu FVG'ler daha yüksek kaliteli ve güvenilirdir
        - Smart Money hareketlerinin sonucudur
        
        Args:
            candles: Mum verileri
            displacements: Displacement Analyzer'dan gelen displacement'lar
            
        Returns:
            Displacement tabanlı FVG'ler listesi
        """
        if candles is None or len(candles) < 3:
            logger.warning("Displacement FVG analizi için yetersiz mum verisi")
            return []
            
        displacement_fvgs = []
        
        try:
            # Eğer displacement'lar verilmişse, onlara odaklan
            if displacements:
                for displacement in displacements:
                    candle_index = displacement.get('candle_index', 0)
                    
                    # Displacement'ın etrafındaki FVG'leri kontrol et
                    fvg_candidates = self._find_fvgs_around_displacement(candles, candle_index)
                    
                    for fvg in fvg_candidates:
                        # Displacement bilgilerini FVG'ye ekle
                        fvg['displacement_triggered'] = True
                        fvg['displacement_strength'] = displacement.get('strength', 0)
                        fvg['displacement_direction'] = displacement.get('direction', 'unknown')
                        
                        # Kalite puanını artır
                        base_quality = fvg.get('quality_score', 50)
                        displacement_bonus = min(displacement.get('strength', 0) * 5, 30)
                        fvg['quality_score'] = base_quality + displacement_bonus
                        
                        displacement_fvgs.append(fvg)
                        
                        logger.debug(f"Displacement FVG tespit edildi @ {fvg['timestamp']}: "
                                   f"Type={fvg['type']}, Strength={displacement.get('strength', 0)}")
            else:
                # Displacement bilgisi yoksa, normal FVG tespiti yap
                logger.warning("Displacement bilgisi yok - normal FVG tespiti yapılıyor")
                displacement_fvgs = self._find_fvgs_full_scan(candles)
            
            logger.info(f"Toplam {len(displacement_fvgs)} displacement FVG tespit edildi")
            return displacement_fvgs
            
        except Exception as e:
            logger.error(f"Displacement FVG analizi hatası: {str(e)}")
            return []
    
    def _find_fvgs_around_displacement(self, candles: pd.DataFrame, 
                                     displacement_index: int) -> List[Dict[str, Any]]:
        """
        Belirli bir displacement etrafındaki FVG'leri bulur.
        
        Args:
            candles: Mum verileri
            displacement_index: Displacement mumunun indeksi
            
        Returns:
            Displacement etrafındaki FVG'ler
        """
        fvgs = []
        
        # Displacement'tan önceki ve sonraki 3'er mumu kontrol et
        search_range = 3
        start_index = max(2, displacement_index - search_range)
        end_index = min(len(candles) - 1, displacement_index + search_range)
        
        for i in range(start_index, end_index - 1):
            if i < 2:
                continue
                
            prev_prev = candles.iloc[i - 2]
            prev = candles.iloc[i - 1]
            current = candles.iloc[i]
            
            # Bullish FVG kontrolü
            if prev_prev['low'] > current['high']:
                fvg_top = prev_prev['low']
                fvg_bottom = current['high']
                fvg_size = fvg_top - fvg_bottom
                
                # Minimum FVG boyutu kontrolü
                if fvg_size > 0:
                    fvgs.append({
                        'type': 'bullish',
                        'top': fvg_top,
                        'bottom': fvg_bottom,
                        'eq': (fvg_top + fvg_bottom) / 2,
                        'timestamp': prev['timestamp'],
                        'filled': False,
                        'displacement_context': True,
                        'displacement_distance': abs(i - displacement_index),
                        'size': fvg_size
                    })
            
            # Bearish FVG kontrolü
            elif prev_prev['high'] < current['low']:
                fvg_top = current['low']
                fvg_bottom = prev_prev['high']
                fvg_size = fvg_top - fvg_bottom
                
                # Minimum FVG boyutu kontrolü
                if fvg_size > 0:
                    fvgs.append({
                        'type': 'bearish',
                        'top': fvg_top,
                        'bottom': fvg_bottom,
                        'eq': (fvg_top + fvg_bottom) / 2,
                        'timestamp': prev['timestamp'],
                        'filled': False,
                        'displacement_context': True,
                        'displacement_distance': abs(i - displacement_index),
                        'size': fvg_size
                    })
        
        return fvgs

    def find_confluence_zones(self, fvgs: List[Dict[str, Any]],
                                        order_blocks: List[Dict[str, Any]],
                                        tolerance_pct: float = 0.1,
                                        time_window_hours: int = 48) -> List[Dict[str, Any]]:
        """
        FVG ve Order Block kesişimlerini tespit eder (ICT Confluence).
        
        ICT Mantığı:
        - FVG + Order Block kesişimleri en güçlü POI'lerdir
        - Bu alanlar yüksek olasılıklı giriş noktalarıdır
        - Smart Money'nin hem verimsizlik hem de emir yoğunluğu bölgeleridir
        
        Args:
            fvgs: FVG listesi
            order_blocks: Order Block listesi
            tolerance_percent: Kesişim toleransı (%2)
            time_window_hours: Zaman penceresi (48 saat)
            
        Returns:
            FVG + OB confluence alanları
        """
        confluences = []
        
        if not fvgs or not order_blocks:
            logger.warning("FVG-OB confluence analizi için yetersiz veri")
            return confluences
        
        try:
            for fvg in fvgs:
                fvg_top = fvg['top']
                fvg_bottom = fvg['bottom']
                fvg_timestamp = fvg['timestamp']
                fvg_type = fvg['type']
                
                for ob in order_blocks:
                    ob_top = ob.get('top', ob.get('high', 0))
                    ob_bottom = ob.get('bottom', ob.get('low', 0))
                    ob_timestamp = ob.get('timestamp', ob.get('time', fvg_timestamp))
                    ob_type = ob.get('type', 'unknown')
                    
                    # Tip uyumluluğu kontrolü (Bull FVG + Bull OB)
                    if fvg_type != ob_type:
                        continue
                    
                    # Zaman penceresi kontrolü
                    try:
                        fvg_time = pd.to_datetime(fvg_timestamp)
                        ob_time = pd.to_datetime(ob_timestamp)
                        time_diff = abs((fvg_time - ob_time).total_seconds() / 3600)
                        
                        if time_diff > time_window_hours:
                            continue
                    except:
                        # Zaman kontrolü başarısızsa devam et
                        pass
                    
                    # Fiyat kesişimi kontrolü
                    overlap = self._calculate_price_overlap(
                        fvg_top, fvg_bottom, ob_top, ob_bottom, tolerance_pct
                    )
                    
                    if overlap > 0:
                        confluence_top = min(fvg_top, ob_top)
                        confluence_bottom = max(fvg_bottom, ob_bottom)
                        
                        confluence = {
                            'type': 'fvg_ob_confluence',
                            'direction': fvg_type,
                            'top': confluence_top,
                            'bottom': confluence_bottom,
                            'eq': (confluence_top + confluence_bottom) / 2,
                            'timestamp': fvg_timestamp,
                            'fvg_data': fvg,
                            'ob_data': ob,
                            'overlap_percent': overlap,
                            'confluence_score': self._calculate_confluence_score(fvg, ob, overlap),
                            'quality_score': 85 + min(overlap * 0.5, 15)  # Base 85 + overlap bonus
                        }
                        
                        confluences.append(confluence)
                        
                        logger.debug(f"ICT FVG-OB Confluence tespit edildi @ {fvg_timestamp}: "
                                   f"{fvg_type.upper()}, Overlap: {overlap:.1f}%")
            
            # Kalite puanına göre sırala
            confluences.sort(key=lambda x: x['confluence_score'], reverse=True)
            
            logger.info(f"Toplam {len(confluences)} FVG-OB confluence tespit edildi")
            return confluences
            
        except Exception as e:
            logger.error(f"FVG-OB confluence analizi hatası: {str(e)}")
            return []
    
    def _calculate_price_overlap(self, fvg_top: float, fvg_bottom: float,
                               ob_top: float, ob_bottom: float,
                               tolerance_pct: float) -> float:
        """
        İki fiyat aralığı arasındaki kesişim yüzdesini hesaplar.
        
        Args:
            fvg_top, fvg_bottom: FVG fiyat aralığı
            ob_top, ob_bottom: Order Block fiyat aralığı
            tolerance_percent: Tolerans yüzdesi
            
        Returns:
            Kesişim yüzdesi (0-100)
        """
        try:
            # Tolerans değerlerini hesapla
            fvg_range = fvg_top - fvg_bottom
            ob_range = ob_top - ob_bottom
            
            fvg_tolerance = fvg_range * (tolerance_pct / 100)
            ob_tolerance = ob_range * (tolerance_pct / 100)
            
            # Genişletilmiş aralıklar
            fvg_top_extended = fvg_top + fvg_tolerance
            fvg_bottom_extended = fvg_bottom - fvg_tolerance
            ob_top_extended = ob_top + ob_tolerance
            ob_bottom_extended = ob_bottom - ob_tolerance
            
            # Kesişim hesaplama
            overlap_top = min(fvg_top_extended, ob_top_extended)
            overlap_bottom = max(fvg_bottom_extended, ob_bottom_extended)
            
            if overlap_top > overlap_bottom:
                overlap_size = overlap_top - overlap_bottom
                total_range = max(fvg_range, ob_range)
                overlap_percent = (overlap_size / total_range) * 100 if total_range > 0 else 0
                return min(overlap_percent, 100)
            
            return 0
            
        except Exception as e:
            logger.error(f"Fiyat kesişimi hesaplama hatası: {str(e)}")
            return 0
    
    def _calculate_confluence_score(self, fvg: Dict[str, Any], 
                                  ob: Dict[str, Any], overlap: float) -> float:
        """
        FVG-OB confluence için puan hesaplar.
        
        Args:
            fvg: FVG verisi
            ob: Order Block verisi
            overlap: Kesişim yüzdesi
            
        Returns:
            Confluence puanı (0-100)
        """
        score = 0
        
        # Base overlap puanı
        score += min(overlap * 0.8, 40)
        
        # FVG kalite puanı
        fvg_quality = fvg.get('quality_score', 50)
        score += min(fvg_quality * 0.3, 20)
        
        # OB kalite puanı (eğer varsa)
        ob_quality = ob.get('quality_score', ob.get('score', 50))
        score += min(ob_quality * 0.3, 20)
        
        # Displacement context bonus
        if fvg.get('displacement_triggered', False):
            score += 10
        
        # Size uyumluluğu
        fvg_size = fvg.get('size', fvg['top'] - fvg['bottom'])
        ob_size = ob.get('size', (ob.get('top', ob.get('high', 0)) - ob.get('bottom', ob.get('low', 0))))
        
        if fvg_size > 0 and ob_size > 0:
            size_ratio = min(fvg_size, ob_size) / max(fvg_size, ob_size)
            score += size_ratio * 10
        
        return min(score, 100)