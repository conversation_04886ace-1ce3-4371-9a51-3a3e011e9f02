# fvg_ob_confluence_analyzer.py

import pandas as pd
from loguru import logger
from typing import Dict, List, Optional, Any, Tuple
from datetime import datetime, timedelta
from utils import calculate_spatial_overlap_score, calculate_temporal_proximity, is_ote_enhanced
from exceptions import InvalidDataError, CalculationError
from premium_discount_analyzer import PremiumDiscountAnalyzer
from liquidity_hunt_weak_strong_analyzer import LiquidityHuntWeakStrongAnalyzer

class FVGOBConfluenceAnalyzer:
    """
    Gerçek ICT konseptine göre FVG + Order Block Confluence analizi.
    
    ICT Kuralları:
    1. BOS/MSS swing aralığı içinde FVG ve OB bulunmalı
    2. FVG ve OB spatial confluence (aynı fiyat bölgesi) göstermeli
    3. OTE seviyesinde ise bonus puan almalı
    4. Temporal proximity (zaman yakınlığı) olmalı
    """
    
    def __init__(self, config: Optional[Dict[str, Any]] = None,
                 premium_discount_analyzer: Optional[PremiumDiscountAnalyzer] = None,
                 liquidity_hunt_analyzer: Optional[LiquidityHuntWeakStrongAnalyzer] = None):
        """
        FVG-OB Confluence Analyzer'ı başlatır.
        
        Args:
            config: Konfigürasyon parametreleri
            premium_discount_analyzer: Premium/discount analiz için gerekli analizör
            liquidity_hunt_analyzer: Likidite avı analiz için gerekli analizör
        """
        self.config = config or {}
        
        # ICT Confluence parametreleri
        self.spatial_tolerance_pct = self.config.get('spatial_tolerance_pct', 2.0)
        self.temporal_window_hours = self.config.get('temporal_window_hours', 72)
        self.min_confluence_score = self.config.get('min_confluence_score', 50.0)
        # Skorlama ağırlıkları
        self.weights = self.config.get('weights', {
            'spatial': 0.30,      # Mekansal Çakışma
            'temporal': 0.20,     # Zaman Yakınlığı
            'fvg_quality': 0.20,  # FVG Kalitesi
            'ob_quality': 0.20,   # OB Kalitesi
            'ote': 0.10,          # OTE Enrichment
            'premium_discount': 0.15, # Ekstra bonus
            'liquidity_hunt': 0.25    # Ekstra bonus
        })

        # Alt analizörleri Dependency Injection ile al
        self.premium_discount_analyzer = premium_discount_analyzer
        self.liquidity_hunt_analyzer = liquidity_hunt_analyzer
        
        logger.info("FVG-OB Confluence Analyzer, Premium/Discount ve Liquidity Hunt entegrasyonu ile başlatıldı")
    
    def analyze_fvg_ob_confluence(self, 
                                 fvg_data: List[Dict[str, Any]], 
                                 order_blocks: Dict[str, Any],
                                 swing_points: List[Dict[str, Any]],
                                 structure_breaks: List[Dict[str, Any]],
                                 fibonacci_data: Optional[Dict[str, Any]] = None,
                                 premium_discount_data: Optional[Dict[str, Any]] = None,
                                 liquidity_hunt_data: Optional[List[Dict[str, Any]]] = None,
                                 trade_direction: str = 'bullish',
                                 current_price: float = 0) -> List[Dict[str, Any]]:
        """
        FVG + OB Confluence analizi yapar.
        
        Args:
            fvg_data: Fair Value Gap verileri
            order_blocks: Order Block verileri
            swing_points: Swing pivot noktaları
            structure_breaks: BOS/MSS kırılım verileri
            fibonacci_data: Fibonacci analiz verileri
            trade_direction: İşlem yönü ('bullish' veya 'bearish')
            current_price: Güncel fiyat
            
        Returns:
            List[Dict]: Geçerli FVG-OB confluence'ları
        """
        try:
            confluences = []
            
            if not fvg_data or not order_blocks or not swing_points or not structure_breaks:
                raise InvalidDataError("FVG-OB confluence analizi için FVG, OB, swing veya kırılım verileri eksik.")
            
            logger.info(f"FVG-OB Confluence analizi başlıyor - Yön: {trade_direction}")
            
            # İlgili Order Block'u al
            target_ob = order_blocks.get('bullish' if trade_direction == 'bullish' else 'bearish')
            if not target_ob:
                logger.debug(f"{trade_direction} Order Block bulunamadı")
                return confluences
            
            # Her FVG için confluence kontrolü yap
            for fvg in fvg_data:
                if not isinstance(fvg, dict):
                    continue
                
                # FVG yönü kontrolü
                if fvg.get('type') != trade_direction:
                    continue
                
                confluence = self._analyze_single_fvg_ob_confluence(
                    fvg, target_ob, swing_points, structure_breaks, 
                    fibonacci_data, premium_discount_data, liquidity_hunt_data, 
                    trade_direction, current_price
                )
                
                if confluence and confluence.get('confluence_score', 0) >= self.min_confluence_score:
                    confluences.append(confluence)
                    logger.success(f"Geçerli FVG-OB confluence bulundu: Skor {confluence['confluence_score']:.1f}")
            
            # Skorlara göre sırala
            confluences.sort(key=lambda x: x.get('confluence_score', 0), reverse=True)
            
            logger.info(f"Toplam {len(confluences)} geçerli FVG-OB confluence tespit edildi")
            return confluences

        except InvalidDataError as e:
            logger.warning(f"Geçersiz veri nedeniyle FVG-OB analizi durduruldu: {e}")
            return []
        except CalculationError as e:
            logger.error(f"FVG-OB confluence hesaplama hatası: {e}", exc_info=True)
            return []
        except Exception as e:
            logger.error(f"Beklenmedik FVG-OB confluence analizi hatası: {e}", exc_info=True)
            return []
    
    def _analyze_single_fvg_ob_confluence(self, 
                                        fvg: Dict[str, Any], 
                                        ob: Dict[str, Any],
                                        swing_points: List[Dict[str, Any]],
                                        structure_breaks: List[Dict[str, Any]],
                                        fibonacci_data: Optional[Dict[str, Any]],
                                        premium_discount_data: Optional[Dict[str, Any]],
                                        liquidity_hunt_data: Optional[List[Dict[str, Any]]],
                                        trade_direction: str,
                                        current_price: float) -> Optional[Dict[str, Any]]:
        """
        Tek bir FVG-OB çifti için confluence analizi yapar.
        """
        try:
            # 1. BOS/MSS Swing Aralığı Kontrolü
            swing_range = self._find_relevant_swing_range(fvg, ob, swing_points, structure_breaks)
            if not swing_range:
                # Bu bir hata değil, sadece bir koşul sağlanmadı.
                logger.trace("FVG-OB için geçerli swing aralığı bulunamadı")
                return None
            
            # 2. Spatial Confluence Kontrolü (utils kullanarak)
            spatial_score = calculate_spatial_overlap_score(fvg, ob)
            if spatial_score < 30:  # Minimum spatial confluence
                logger.debug(f"Yetersiz spatial confluence: {spatial_score:.1f}")
                return None
            
            # 3. Temporal Proximity Kontrolü (yeni utils fonksiyonu ile)
            fvg_time = fvg.get('timestamp')
            ob_time = ob.get('timestamp')
            temporal_score = calculate_temporal_proximity(fvg_time, ob_time, max_hours=self.temporal_window_hours) * 100  # Skoru 0-100 aralığına getir
            
            # 4. OTE Enhancement Kontrolü
            confluence_zone = {
                'top': max(fvg.get('top', float('-inf')), ob.get('top', float('-inf'))),
                'bottom': min(fvg.get('bottom', float('inf')), ob.get('bottom', float('inf')))
            }
            ote_zone = fibonacci_data.get('ote_zone') if fibonacci_data else None
            ote_enhanced = is_ote_enhanced(confluence_zone, ote_zone)
            ote_score = 100 if ote_enhanced else 0
            
            # 5. ICT Kalite Skorlaması (mesafe sınırı yok)
            distance_score = self._calculate_distance_score(fvg, ob, current_price)

            # 6. Premium/Discount Confluence Kontrolü
            pd_score = self._check_premium_discount_confluence(fvg, ob, premium_discount_data, trade_direction)

            # 7. Liquidity Hunt Confluence Kontrolü
            lh_score = self._check_liquidity_hunt_confluence(fvg, ob, liquidity_hunt_data, trade_direction)
            
            # 8. Ağırlıklı Toplam Confluence Skoru
            total_score, score_details = self._calculate_weighted_confluence_score(
                fvg, ob, spatial_score, temporal_score, ote_enhanced, pd_score > 0, lh_score > 0
            )

            
            # 7. Giriş Seviyesi Hesapla
            entry_price = self._calculate_confluence_entry_price(fvg, ob, ote_score > 0)
            
            confluence = {
                'type': 'FVG_OB_CONFLUENCE',
                'direction': trade_direction,
                'fvg_data': fvg,
                'ob_data': ob,
                'swing_range': swing_range,
                'entry_price': entry_price,
                'confluence_score': total_score,
                'score_details': score_details,
                'spatial_score': spatial_score,
                'temporal_score': temporal_score,
                'ote_score': ote_score,
                'is_ote_enhanced': ote_enhanced,
                'description': f'{trade_direction.title()} FVG-OB Confluence (Score: {total_score:.1f})',
                'timestamp': datetime.now()
            }
            
            return confluence

        except (KeyError, TypeError) as e:
            raise CalculationError(f"Single FVG-OB analizinde veri yapısı hatası: {e}")
        except Exception as e:
            logger.error(f"Single FVG-OB confluence analizi sırasında beklenmedik hata: {e}", exc_info=True)
            # Bu seviyede None dönmek, ana döngünün devam etmesini sağlar.
            return None

    def _calculate_weighted_confluence_score(self, fvg: Dict[str, Any], ob: Dict[str, Any], 
                                             spatial_score: float, temporal_score: float, is_ote_enhanced: bool, 
                                             is_pd_enhanced: bool, is_lh_enhanced: bool) -> Tuple[float, Dict[str, float]]:
        """Kullanıcının belirttiği ağırlıklara göre confluence skorunu hesaplar."""
        
        fvg_quality = fvg.get('quality_score', 50.0) # 0-100 arası
        ob_quality = ob.get('quality_score', 50.0)   # 0-100 arası

        # Temel bileşenleri ağırlıklandır
        spatial_comp = spatial_score * self.weights['spatial']
        temporal_comp = temporal_score * self.weights['temporal']
        fvg_comp = fvg_quality * self.weights['fvg_quality']
        ob_comp = ob_quality * self.weights['ob_quality']
        ote_comp = (100 if is_ote_enhanced else 0) * self.weights['ote']

        base_score = spatial_comp + temporal_comp + fvg_comp + ob_comp + ote_comp

        # Bonusları ekle
        if is_pd_enhanced:
            base_score += 100 * self.weights['premium_discount']
        if is_lh_enhanced:
            base_score += 100 * self.weights['liquidity_hunt']

        score_details = {
            'spatial_component': spatial_comp,
            'temporal_component': temporal_comp,
            'fvg_quality_component': fvg_comp,
            'ob_quality_component': ob_comp,
            'ote_component': ote_comp,
            'premium_discount_bonus': 100 * self.weights['premium_discount'] if is_pd_enhanced else 0,
            'liquidity_hunt_bonus': 100 * self.weights['liquidity_hunt'] if is_lh_enhanced else 0,
        }

        return min(100.0, base_score), score_details

    def _check_premium_discount_confluence(self, fvg: Dict[str, Any], ob: Dict[str, Any], 
                                           premium_discount_data: Optional[Dict[str, Any]], 
                                           trade_direction: str) -> float:
        """FVG-OB confluence'ının Premium/Discount bölgeleriyle uyumunu kontrol eder."""
        if not premium_discount_data:
            return 0.0

        try:
            confluence_mid_price = (fvg['top'] + ob['bottom']) / 2
            is_in_zone = self.premium_discount_analyzer.is_price_in_premium_or_discount(
                confluence_mid_price, premium_discount_data, trade_direction
            )
            
            if is_in_zone:
                logger.trace(f"Confluence, {trade_direction} için uygun P/D bölgesinde.")
                return self.premium_discount_bonus
            return 0.0
        except (KeyError, TypeError) as e:
            logger.warning(f"Premium/Discount verisi analiz edilirken hata: {e}")
            return 0.0

    def _check_liquidity_hunt_confluence(self, fvg: Dict[str, Any], ob: Dict[str, Any], 
                                         liquidity_hunt_data: Optional[List[Dict[str, Any]]], 
                                         trade_direction: str) -> float:
        """FVG-OB confluence'ının likidite avı bölgeleriyle uyumunu kontrol eder."""
        if not liquidity_hunt_data:
            return 0.0

        try:
            confluence_zone = {'top': max(fvg['top'], ob['top']), 'bottom': min(fvg['bottom'], ob['bottom'])}
            
            for hunt in liquidity_hunt_data:
                if hunt.get('direction') != trade_direction:
                    continue

                # Likidite avı bölgesi ile FVG-OB çakışma bölgesinin kesişimini kontrol et
                hunt_zone = {'top': hunt['price_range'][1], 'bottom': hunt['price_range'][0]}
                overlap_score = calculate_spatial_overlap_score(confluence_zone, hunt_zone)
                
                if overlap_score > 10: # %10'dan fazla bir çakışma yeterli
                    logger.trace(f"Confluence, bir likidite avı bölgesiyle ({hunt.get('type')}) kesişiyor.")
                    return self.liquidity_hunt_bonus
            return 0.0
        except (KeyError, TypeError) as e:
            logger.warning(f"Likidite avı verisi analiz edilirken hata: {e}")
            return 0.0
    
    def _find_relevant_swing_range(self, 
                                  fvg: Dict[str, Any], 
                                  ob: Dict[str, Any],
                                  swing_points: List[Dict[str, Any]],
                                  structure_breaks: List[Dict[str, Any]]) -> Optional[Dict[str, Any]]:
        """
        FVG ve OB için ilgili BOS/MSS swing aralığını bulur.
        """
        try:
            fvg_timestamp = fvg.get('timestamp')
            ob_timestamp = ob.get('timestamp')
            
            if not fvg_timestamp or not ob_timestamp:
                return None
            
            # En yakın structure break'i bul
            relevant_break = None
            min_time_diff = float('inf')
            
            for brk in structure_breaks:
                brk_timestamp = brk.get('timestamp')
                if not brk_timestamp:
                    continue
                
                # FVG ve OB'nin structure break'ten sonra oluşup oluşmadığını kontrol et
                if (self._is_timestamp_after(fvg_timestamp, brk_timestamp) and 
                    self._is_timestamp_after(ob_timestamp, brk_timestamp)):
                    
                    time_diff = abs(self._timestamp_diff_hours(brk_timestamp, min(fvg_timestamp, ob_timestamp)))
                    if time_diff < min_time_diff:
                        min_time_diff = time_diff
                        relevant_break = brk
            
            if not relevant_break:
                return None
            
            # Swing aralığını belirle
            swing_high = relevant_break.get('broken_pivot_price')
            swing_low = relevant_break.get('price')  # Kırılım sonrası fiyat
            
            if trade_direction == 'bearish':
                swing_high, swing_low = swing_low, swing_high
            
            return {
                'structure_break': relevant_break,
                'swing_high': max(swing_high, swing_low),
                'swing_low': min(swing_high, swing_low),
                'range_size': abs(swing_high - swing_low)
            }
            
        except Exception as e:
            logger.error(f"Swing range bulma hatası: {e}", exc_info=True)
            return None
    

    
    def _calculate_ote_enhancement(self, 
                                  fvg: Dict[str, Any], 
                                  ob: Dict[str, Any],
                                  fibonacci_data: Optional[Dict[str, Any]],
                                  swing_range: Dict[str, Any]) -> float:
        """
        OTE seviyesinde enhancement kontrolü yapar.
        """
        try:
            if not fibonacci_data or not swing_range:
                return 0
            
            # Confluence orta noktasını hesapla
            fvg_mid = (fvg.get('top', 0) + fvg.get('bottom', 0)) / 2
            ob_mid = (ob.get('high', 0) + ob.get('low', 0)) / 2
            confluence_mid = (fvg_mid + ob_mid) / 2
            
            # OTE seviyeleri (%61.8 - %79)
            fib_levels = fibonacci_data.get('levels', {})
            ote_618 = fib_levels.get('0.618')
            ote_79 = fib_levels.get('0.79')
            
            if not ote_618 or not ote_79:
                return 0
            
            ote_bottom = min(ote_618, ote_79)
            ote_top = max(ote_618, ote_79)
            
            # Confluence OTE içinde mi?
            if ote_bottom <= confluence_mid <= ote_top:
                # OTE içinde, bonus puan ver
                ote_center = (ote_618 + ote_79) / 2
                distance_from_center = abs(confluence_mid - ote_center)
                ote_range = abs(ote_top - ote_bottom)
                
                if ote_range > 0:
                    proximity_to_center = 1 - (distance_from_center / (ote_range / 2))
                    ote_score = proximity_to_center * self.ote_bonus_points
                    return max(0, ote_score)
            
            return 0
            
        except Exception as e:
            logger.error(f"OTE enhancement hesaplama hatası: {e}", exc_info=True)
            return 0
    
    def _calculate_distance_score(self, fvg: Dict[str, Any], ob: Dict[str, Any], current_price: float) -> float:
        """
        ICT konseptine göre confluence kalite skorlaması.
        
        ICT'de mesafe sınırı yoktur - confluence geçerliyse kullanılır.
        Bu skor sadece confluence kalitesini değerlendirir.
        """
        try:
            if current_price <= 0:
                return 30  # Fiyat bilgisi yoksa orta skor
            
            # Confluence orta noktası
            fvg_mid = (fvg.get('top', 0) + fvg.get('bottom', 0)) / 2
            ob_mid = (ob.get('high', 0) + ob.get('low', 0)) / 2
            confluence_mid = (fvg_mid + ob_mid) / 2
            
            distance_pct = abs(confluence_mid - current_price) / current_price * 100
            
            # ICT Kalite Skorlaması (mesafe sınırı yok, sadece kalite değerlendirmesi)
            if distance_pct <= 0.5:  # Çok yakın - mükemmel
                return 40
            elif distance_pct <= 1.5:  # Yakın - çok iyi
                return 35
            elif distance_pct <= 3.0:  # Orta mesafe - iyi
                return 30
            elif distance_pct <= 5.0:  # Uzak - kabul edilebilir
                return 25
            elif distance_pct <= 10.0:  # Çok uzak - düşük kalite
                return 20
            else:  # Aşırı uzak - çok düşük kalite
                return 15
                
        except Exception as e:
            logger.error(f"Distance score hesaplama hatası: {e}", exc_info=True)
            return 20
    
    def _calculate_confluence_entry_price(self, fvg: Dict[str, Any], ob: Dict[str, Any], is_ote_enhanced: bool) -> float:
        """
        Confluence için optimal giriş fiyatını hesaplar.
        """
        try:
            fvg_mid = (fvg.get('top', 0) + fvg.get('bottom', 0)) / 2
            ob_mid = (ob.get('high', 0) + ob.get('low', 0)) / 2
            
            if is_ote_enhanced:
                # OTE enhanced ise, FVG ve OB'nin optimal noktasını kullan
                # FVG'nin equilibrium noktası varsa onu tercih et
                fvg_eq = fvg.get('eq', fvg_mid)
                entry_price = (fvg_eq + ob_mid) / 2
            else:
                # Normal confluence orta noktası
                entry_price = (fvg_mid + ob_mid) / 2
            
            return entry_price
            
        except Exception as e:
            logger.error(f"Entry price hesaplama hatası: {e}", exc_info=True)
            return 0
    
    def _is_timestamp_after(self, timestamp1, timestamp2) -> bool:
        """Timestamp1'in timestamp2'den sonra olup olmadığını kontrol eder."""
        try:
            if hasattr(timestamp1, 'timestamp'):
                ts1 = timestamp1.timestamp()
            else:
                ts1 = float(timestamp1)
                
            if hasattr(timestamp2, 'timestamp'):
                ts2 = timestamp2.timestamp()
            else:
                ts2 = float(timestamp2)
                
            return ts1 > ts2
        except:
            return False
    
    def _timestamp_diff_hours(self, timestamp1, timestamp2) -> float:
        """İki timestamp arasındaki saat farkını hesaplar."""
        try:
            if hasattr(timestamp1, 'timestamp'):
                ts1 = timestamp1.timestamp()
            else:
                ts1 = float(timestamp1)
                
            if hasattr(timestamp2, 'timestamp'):
                ts2 = timestamp2.timestamp()
            else:
                ts2 = float(timestamp2)
                
            return abs(ts1 - ts2) / 3600
        except:
            return float('inf')