import os
import logging
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
from typing import Optional
from bybit_client import BybitClient
from utils import format_price_standard

# Logs ve data klasörlerini oluştur
os.makedirs("logs", exist_ok=True)
os.makedirs("data", exist_ok=True)

# Logger konfigürasyonu
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s | %(levelname)s | %(message)s',
    handlers=[
        logging.FileHandler("logs/fvrp_history.log"),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class FVRPAnalyzer:
    def __init__(self):
        """Fixed Volume Range Profile analizi yapan sınıf"""
        pass # Şimdilik init boş kalabilir

    def _calculate_value_area(self, volumes_per_bin, bin_boundaries, poc_bin_index, total_volume, va_width_pct):
        """
        POC'den başlayarak Değ<PERSON> (VAH/VAL) hesaplar.
        Pine Script mantığına benzer şekilde, POC'den dışarı doğru genişler.
        """
        target_volume = total_volume * (va_width_pct / 100.0)
        if total_volume == 0 or target_volume == 0:
            # Eğer hiç hacim yoksa, VA hesaplanamaz. POC'nin olduğu bin'i VA olarak döndür.
            poc_bin_bottom, poc_bin_top = bin_boundaries[poc_bin_index]
            return poc_bin_top, poc_bin_bottom # VAH, VAL

        current_volume = volumes_per_bin[poc_bin_index]
        
        vah_index = poc_bin_index
        val_index = poc_bin_index
        num_bins = len(volumes_per_bin)

        # POC'nin hacmi zaten hedefi karşılıyorsa veya aşıyorsa
        if current_volume >= target_volume:
            poc_bin_bottom, poc_bin_top = bin_boundaries[poc_bin_index]
            return poc_bin_top, poc_bin_bottom # VAH, VAL

        # POC'den dışarı doğru genişle
        while True:
            up_index = vah_index - 1 # Fiyat arttıkça index azalır
            down_index = val_index + 1 # Fiyat azaldıkça index artar

            vol_up = volumes_per_bin[up_index] if up_index >= 0 else -1
            vol_down = volumes_per_bin[down_index] if down_index < num_bins else -1

            # Eğer her iki yönde de genişleyecek yer kalmadıysa döngüden çık
            if vol_up == -1 and vol_down == -1:
                break

            # Daha yüksek hacimli komşu seviyeyi ekle
            if vol_up >= vol_down:
                current_volume += vol_up
                vah_index = up_index
            else: # vol_down > vol_up
                current_volume += vol_down
                val_index = down_index

            # Hedef hacme ulaşıldıysa döngüden çık
            if current_volume >= target_volume:
                break
        
        # VAH: En üstteki bin'in üst sınırı
        # VAL: En alttaki bin'in alt sınırı
        val_price = bin_boundaries[vah_index][0] # En düşük index'li bin'in alt sınırı
        vah_price = bin_boundaries[val_index][1] # En yüksek index'li bin'in üst sınırı

        return vah_price, val_price
    
    def calculate_volume_profile(self, df, resolution=200, va_width_pct=70):
        """
        Fiyat aralığını belirli sayıda bölmeye ayırarak her bölmedeki işlem hacmini,
        her mumun hacmini temas ettiği seviyelere dağıtarak hesaplar.
        POC, VAH ve VAL değerlerini Pine Script'teki mantığa benzer şekilde hesaplar.
        
        Args:
            df: Mum verileri içeren DataFrame ('high', 'low', 'volume' sütunları olmalı)
            resolution: Fiyat aralığının kaç parçaya bölüneceği
            va_width_pct: Value Area genişliği yüzdesi (genellikle %70)
            
        Returns:
            dict: POC, VAH, VAL değerlerini içeren sözlük
        """
        if df.empty or resolution <= 0:
            return {'poc': None, 'vah': None, 'val': None}
        
        # Fiyat aralığını belirle
        price_high = df['high'].max()
        price_low = df['low'].min()
        price_range = price_high - price_low
        
        # Eğer fiyat hiç değişmemişse (tek bir fiyat varsa)
        if price_range == 0:
            # Tüm hacmi bu fiyata ata, VAH/VAL/POC aynı olur
            poc_price = price_low # veya price_high
            return {'poc': poc_price, 'vah': poc_price, 'val': poc_price}
            
        # Fiyat aralığını bölmelere ayır (bin'ler)
        price_delta = price_range / resolution
        bin_boundaries = []
        for i in range(resolution):
            bin_bottom = price_low + i * price_delta
            bin_top = price_low + (i + 1) * price_delta
            bin_boundaries.append((bin_bottom, bin_top))
        
        # Her bölme için hacim dağılımını hesapla
        volumes_per_bin = [0.0] * resolution
        total_volume = 0.0

        for _, candle in df.iterrows():
            o, h, l, c, v = candle['open'], candle['high'], candle['low'], candle['close'], candle['volume']
            candle_height = h - l # Kullanılmıyor ama dursun
            total_volume += v

            # Eğer hacim 0 ise atla
            if v == 0:
                continue
                
            # Gövde ve fitil bilgilerini hesapla
            body_top = max(c, o)
            body_bot = min(c, o)
            body_height = max(0, body_top - body_bot)
            top_wick_height = max(0, h - body_top)
            bot_wick_height = max(0, body_bot - l)
            
            # Pine Script'teki gibi hacmi gövde ve fitillere dağıt
            denominator = body_height + 2 * top_wick_height + 2 * bot_wick_height
            
            # Sıfıra bölme hatasını önle
            if denominator <= 0:
                # Eğer yükseklik yoksa veya sadece gövde varsa ve yüksekliği 0 ise
                # Hacmi eşit dağıtmak mantıklı olabilir veya bu mumu atlayabiliriz.
                # Şimdilik, hacmi tek bir kutucuğa (örn. kapanışa yakın) atamayı deneyebiliriz,
                # ama en basit yaklaşım bu mumu atlamak olabilir.
                # Daha önceki kodda candle_height <= 0 kontrolü vardı, benzer şekilde devam edelim.
                continue # Veya alternatif bir dağıtım stratejisi uygulanabilir.

            body_vol = v * (body_height / denominator)
            top_wick_vol = v * (2 * top_wick_height / denominator) # Pine'daki gibi 2* çarpanı
            bot_wick_vol = v * (2 * bot_wick_height / denominator) # Pine'daki gibi 2* çarpanı

            # Mumun temas ettiği her bir bin için hacmi dağıt
            for i in range(resolution):
                bin_bottom, bin_top = bin_boundaries[i]
                bin_vol_contribution = 0.0
                
                # 1. Gövde ile kesişim
                if body_height > 0:
                    body_overlap_start = max(body_bot, bin_bottom)
                    body_overlap_end = min(body_top, bin_top)
                    body_overlap_height = max(0, body_overlap_end - body_overlap_start)
                    if body_overlap_height > 0:
                        bin_vol_contribution += body_vol * (body_overlap_height / body_height)
                        
                # 2. Üst Fitil ile kesişim (Pine'daki gibi /2 ekleniyor)
                if top_wick_height > 0:
                    tw_overlap_start = max(body_top, bin_bottom)
                    tw_overlap_end = min(h, bin_top)
                    tw_overlap_height = max(0, tw_overlap_end - tw_overlap_start)
                    if tw_overlap_height > 0:
                         bin_vol_contribution += (top_wick_vol * (tw_overlap_height / top_wick_height)) / 2.0 # Pine'daki gibi /2
                
                # 3. Alt Fitil ile kesişim (Pine'daki gibi /2 ekleniyor)
                if bot_wick_height > 0:
                    bw_overlap_start = max(l, bin_bottom)
                    bw_overlap_end = min(body_bot, bin_top)
                    bw_overlap_height = max(0, bw_overlap_end - bw_overlap_start)
                    if bw_overlap_height > 0:
                        bin_vol_contribution += (bot_wick_vol * (bw_overlap_height / bot_wick_height)) / 2.0 # Pine'daki gibi /2
                        
                # Hesaplanan katkıyı bin hacmine ekle
                volumes_per_bin[i] += bin_vol_contribution
        
        # POC (Point of Control) - En çok işlem yapılan fiyat seviyesi
        poc_bin_index = -1
        max_volume_in_bin = -1.0
        if total_volume > 0:
             for i in range(resolution):
                 if volumes_per_bin[i] > max_volume_in_bin:
                     max_volume_in_bin = volumes_per_bin[i]
                     poc_bin_index = i
        
        poc_price = None
        if poc_bin_index != -1:
             # POC fiyatını, en yüksek hacimli bin'in orta noktası olarak al
             poc_bin_bottom, poc_bin_top = bin_boundaries[poc_bin_index]
             poc_price = (poc_bin_bottom + poc_bin_top) / 2.0
        elif not df.empty: # Eğer hiç hacim yoksa ama data varsa, ortalama fiyatı POC al
             poc_price = (price_high + price_low) / 2.0
             # Bu durumda poc_bin_index'i de bulalım
             for i in range(resolution):
                 bin_bottom, bin_top = bin_boundaries[i]
                 if bin_bottom <= poc_price < bin_top:
                     poc_bin_index = i
                     break
             if poc_bin_index == -1 and poc_price == bin_boundaries[-1][1]: # Son bin'in tam tepesi
                 poc_bin_index = resolution - 1
        
        # POC index hala bulunamadıysa (çok olası değil ama edge case)
        if poc_bin_index == -1:
             return {'poc': poc_price, 'vah': None, 'val': None}
             
        # Değer Alanını (VAH/VAL) hesapla
        vah_price, val_price = self._calculate_value_area(
            volumes_per_bin,
            bin_boundaries,
            poc_bin_index,
            total_volume,
            va_width_pct
        )
        
        return {
            'poc': poc_price,
            'vah': vah_price,
            'val': val_price
        }

# FVRP analizci örneği oluştur
fvrp_analyzer = FVRPAnalyzer()

def analyze_historical_fvrp(client: Optional['BybitClient'] = None):
    """
    4h zaman dilimleri için son 20 günlük FVRP değerlerini hesaplar, loglar ve CSV dosyasına kaydeder.

    Args:
        client (Optional[BybitClient]): Mevcut BybitClient instance'ı. None ise yeni oluşturulur.
    """
    logger.info("========== TARİHSEL FVRP ANALİZİ BAŞLATILIYOR ==========")

    # Dependency Injection: Mevcut client varsa kullan, yoksa yeni oluştur
    if client is not None:
        logger.info("[FVRP Init] ✅ Mevcut BybitClient instance'ı kullanılıyor (Dependency Injection)")
    else:
        client = BybitClient()
        logger.warning("[FVRP Init] ⚠️  Yeni BybitClient oluşturuldu (Dependency Injection önerilir)")
    
    # Sembolleri ve zaman dilimlerini al
    symbols = os.environ.get("SYMBOLS", "BTCUSDT,ETHUSDT,SOLUSDT").split(",")
    # timeframes = ["15"]  # Sadece 15m - Önceki deneme
    timeframes = ["240"] # Tekrar 4s (240m)
    
    # CSV için veri çerçevesi
    all_fvrp_data = []
    
    # Bugünün tarihini al
    today = datetime.now().date()
    
    # Her sembol ve zaman dilimi için işlem yap
    for symbol in symbols:
        for timeframe in timeframes:
            logger.info(f"[FVRP-BAŞLA-SEMBOL] {symbol}/{timeframe}: Analiz başlatıldı")
            
            # Verileri çek
            days_to_fetch = 30 # Kaç günlük veri çekilecek
            # candles_per_day = (24 * 60) // int(timeframe) # 15m için: (24*60)//15 = 96 - Önceki deneme
            candles_per_day = (24 * 60) // int(timeframe) # 4s için: (24*60)//240 = 6
            max_candles_needed = days_to_fetch * candles_per_day + 1 # +1 küçük bir tampon için

            
            try:
                # Verileri çek - Düzeltilmiş limit ile
                candles = client.fetch_klines(symbol, timeframe, limit=max_candles_needed)
                
                # Verileri kontrol et
                if candles is None or candles.empty or len(candles) < candles_per_day * 5: # En az 5 günlük veri olsun
                    logger.warning(f"{symbol} {timeframe}m için yeterli veri bulunamadı ({len(candles) if candles is not None else 0} mum)!")
                    continue
                
                # Tarih sütunu ekle
                candles['date'] = candles['timestamp'].dt.date
                
                # Benzersiz günleri al
                unique_days = sorted(candles['date'].unique())
                
                # Son 20 günü al (bugün hariç)
                last_20_days = [day for day in unique_days if day < today][-20:]
                
                # Log başlığı
                logger.info(f"[FVRP-VERİ-BAŞLIK] {symbol}/{timeframe}: Son 20 günlük değerler:")
                logger.info("[FVRP-TABLO-BAŞLIK] Tarih           | VAL      | POC       | VAH       ")
                logger.info("[FVRP-TABLO-AYRAÇ] " + "-" * 45)
                
                # Her gün için FVRP hesapla
                for day in last_20_days:
                    # Günlük verileri filtrele
                    day_candles = candles[candles['date'] == day].copy()
                    
                    # Her gün için en az 5 mum olduğundan emin ol
                    if len(day_candles) < 5:
                        continue
                    
                    # Bu gün için FVRP hesapla
                    resolution = 30  # Çözünürlük
                    va_width_pct = 70  # Value Area genişliği
                    
                    try:
                        volume_profile = fvrp_analyzer.calculate_volume_profile(day_candles, resolution=resolution, va_width_pct=va_width_pct)
                        
                        # POC, VAH ve VAL değerlerini al
                        poc_price = volume_profile.get('poc', None)
                        vah_price = volume_profile.get('vah', None)
                        val_price = volume_profile.get('val', None)
                        
                        # Sonuçları formatlayarak göster
                        if poc_price is not None:
                            logger.info(f"[FVRP-VERİ-SATIR] {day} | {format_price_standard(val_price)} | {format_price_standard(poc_price)} | {format_price_standard(vah_price)}")
                            
                            # CSV için veri ekle
                            all_fvrp_data.append({
                                'symbol': symbol,
                                'timeframe': timeframe,
                                'date': day,
                                'val': val_price,
                                'poc': poc_price,
                                'vah': vah_price,
                                'is_naked': True  # Başlangıçta tüm POC'lar naked kabul edilir
                            })
                        
                    except Exception as e:
                        logger.error(f"[FVRP-HESAPLAMA-HATA] {symbol}/{timeframe}: {str(e)}")
                
                logger.info("[FVRP-TABLO-AYRAÇ] " + "-" * 45)
                
            except Exception as e:
                logger.error(f"[FVRP-HESAPLAMA-HATA] {symbol}/{timeframe}: {str(e)}")
    
    # CSV dosyasına kaydet
    if all_fvrp_data:
        # İstenen sütun sırasını belirle
        column_order = ['symbol', 'timeframe', 'date', 'val', 'poc', 'vah', 'is_naked']
        df_result = pd.DataFrame(all_fvrp_data, columns=column_order)
        csv_path = "data/fvrp_data.csv"
        df_result.to_csv(csv_path, index=False)
        
        # Dosyanın gerçekten oluşturulduğunu kontrol et
        if os.path.exists(csv_path):
            logger.info(f"✅ [FVRP-CSV] FVRP verileri başarıyla '{csv_path}' dosyasına kaydedildi. Toplam {len(df_result)} kayıt.")
        else:
            logger.error(f"❌ [FVRP-KRİTİK-HATA] CSV dosyası '{csv_path}' oluşturulamadı!")
            logger.error("📋 Bu durum Naked POC analizinin çalışmamasına neden olacak!")
            logger.error("🔧 Dosya yazma izinlerini ve disk alanını kontrol edin.")
    else:
        logger.error("❌ [FVRP-KRİTİK-HATA] Kaydedilecek FVRP verisi bulunamadı!")
        logger.error("📋 Bu durum Naked POC analizinin çalışmamasına neden olacak!")
        logger.error("🔧 Bybit API bağlantısını ve sembol listesini kontrol edin.")
    
    logger.info("========== TARİHSEL FVRP ANALİZİ TAMAMLANDI ==========")

# Eğer bu dosya doğrudan çalıştırılırsa, analizi başlat
if __name__ == "__main__":
    analyze_historical_fvrp()