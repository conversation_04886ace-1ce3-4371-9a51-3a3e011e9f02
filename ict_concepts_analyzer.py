# ict_concepts_analyzer.py

"""
ICT Concepts Analyzer - teknikler.txt'deki gelişmiş ICT konseptlerini uygular.

Bu modül teknikler.txt dosyasında belirtilen aşağıdaki özellikleri içerir:
- External Liquidity Hunt Analysis
- IDM Confirmed Hunt (+2.5 puan) ve Standard Hunt (+2.0 puan)
- 12H & 24H Timeframe swing analizi
- Sell-side/Buy-side likidite tespiti
- FVG-Enhanced Order Blocks
- Smart Money Concepts
- Market Structure Events (MSS/BOS detection)
- Premium Signals (10+ puanlık mükemmel setups)
"""

from typing import Dict, List, Any, Optional, Tuple
import pandas as pd
from datetime import datetime, timedelta
from loguru import logger


class ICTConceptsAnalyzer:
    """
    ICT Concepts metodolojisini uygulayan ana analiz sınıfı.
    teknikler.txt'deki tüm gelişmiş konseptleri içerir.
    """
    
    def __init__(self, fvg_ob_confluence_analyzer=None):
        """
        ICT Concepts Analyzer'ı başlatır.
        
        Args:
            fvg_ob_confluence_analyzer: FVG-OB confluence analiz için gerekli analizör
        """
        logger.info("ICT Concepts Analyzer başlatıldı")
        
        # Dependency Injection: FVGOBConfluenceAnalyzer
        self.fvg_ob_confluence_analyzer = fvg_ob_confluence_analyzer
        
        # Valid Swing Kriterleri
        self.min_price_diff_pct = 0.05  # %0.05 minimal fiyat farkı
        
        # Round Number seviyeleri (FVG-Enhanced Order Blocks için)
        self.round_numbers = [0.00, 0.50]  # .00, .50 seviyelerine yakınlık
        self.round_number_tolerance = 0.002  # %0.2 tolerans
        
        # External Liquidity Hunt skorları (market structure'dan alınan veriler için)
        self.idm_confirmed_score = 2.5
        self.standard_hunt_score = 2.0
        
        # FVG-Enhanced Order Blocks
        self.fvg_ob_tolerance_pct = 2.0  # %2 tolerans
        self.fvg_ob_timing_window = 48 * 3600  # 48 saat window (saniye)
        
    def analyze_valid_swings(self, swing_points: List[Dict[str, Any]], 
                           current_trend: str) -> List[Dict[str, Any]]:
        """
        Valid Swing kriterlerine göre swing'leri filtreler.
        
        Kriterler:
        ✅ Trend ile uyumlu swing'ler tercih edilir
        ✅ IDM confirmed swing'ler geçersiz sayılır
        ✅ Minimal fiyat farkı kontrolü (%0.05)
        
        Args:
            swing_points: Swing noktaları listesi
            current_trend: Mevcut trend ('bullish', 'bearish', 'sideways')
            
        Returns:
            List[Dict]: Geçerli swing'ler
        """
        try:
            valid_swings = []
            
            for swing in swing_points:
                # 1. Minimal fiyat farkı kontrolü
                if not self._check_min_price_difference(swing, swing_points):
                    continue
                
                # 2. Trend uyum kontrolü
                if not self._check_trend_alignment(swing, current_trend):
                    continue
                
                # 3. IDM confirmation kontrolü
                if swing.get('idm_confirmed', False):
                    logger.debug(f"Swing @ {swing.get('price')} IDM confirmed olduğu için geçersiz")
                    continue
                
                valid_swings.append({
                    **swing,
                    'valid_swing': True,
                    'validation_reason': 'Trend uyumlu ve IDM confirmed değil'
                })
                
            logger.info(f"Valid Swing Analizi: {len(swing_points)} swing'den {len(valid_swings)} tanesi geçerli")
            return valid_swings
            
        except Exception as e:
            logger.error(f"Valid swing analizi hatası: {e}", exc_info=True)
            return []
    
    def get_inducement_from_market_structure(self, market_structure_analysis: Dict[str, Any]) -> List[Dict[str, Any]]:
        """
        Market Structure Analyzer'dan IDM verilerini alır.
        IDM tespiti artık market_structure_analyzer.py'de merkezileştirilmiştir.
        
        Args:
            market_structure_analysis: Market structure analiz sonuçları
            
        Returns:
            List[Dict]: IDM pattern'leri
        """
        try:
            idm_events = market_structure_analysis.get('idm_events', [])
            logger.debug(f"Market structure'dan {len(idm_events)} IDM olayı alındı")
            return idm_events
        except Exception as e:
            logger.error(f"IDM verisi alma hatası: {e}", exc_info=True)
            return []
    
    def analyze_market_structure_events(self, candles: pd.DataFrame, 
                                      swing_points: List[Dict[str, Any]]) -> Dict[str, Any]:
        """
        Market Structure Events (MSS/BOS) tespiti yapar.
        
        ICT Mantığı:
        📈 MSS Bullish: LL → HL → HH (trend değişimi)
        📉 MSS Bearish: HH → LH → LL (trend değişimi)  
        🔄 BOS Bullish/Bearish: Mevcut trend devamı
        
        Args:
            candles: Mum verileri
            swing_points: Swing noktaları
            
        Returns:
            Dict: Market structure analizi
        """
        try:
            if len(swing_points) < 3:
                return {'events': [], 'current_structure': 'UNCLEAR'}
            
            # Son 5 swing'i analiz et
            recent_swings = swing_points[-5:] if len(swing_points) >= 5 else swing_points
            structure_events = []
            
            for i in range(2, len(recent_swings)):
                prev_prev = recent_swings[i-2]
                prev = recent_swings[i-1]
                current = recent_swings[i]
                
                # MSS Bullish: LL → HL → HH
                if (prev_prev.get('type') == 'LL' and 
                    prev.get('type') == 'HL' and 
                    current.get('type') == 'HH'):
                    
                    event = {
                        'type': 'MSS',
                        'direction': 'BULLISH',
                        'pattern': 'LL → HL → HH',
                        'confidence': 0.9,
                        'swing_sequence': [prev_prev, prev, current],
                        'timestamp': current.get('timestamp', 0),
                        'description': 'Market Structure Shift - Trend değişimi (Bullish)'
                    }
                    structure_events.append(event)
                    logger.info("MSS Bullish tespit edildi: LL → HL → HH")
                
                # MSS Bearish: HH → LH → LL
                elif (prev_prev.get('type') == 'HH' and 
                      prev.get('type') == 'LH' and 
                      current.get('type') == 'LL'):
                    
                    event = {
                        'type': 'MSS',
                        'direction': 'BEARISH',
                        'pattern': 'HH → LH → LL',
                        'confidence': 0.9,
                        'swing_sequence': [prev_prev, prev, current],
                        'timestamp': current.get('timestamp', 0),
                        'description': 'Market Structure Shift - Trend değişimi (Bearish)'
                    }
                    structure_events.append(event)
                    logger.info("MSS Bearish tespit edildi: HH → LH → LL")
                
                # BOS kontrolü (trend devamı)
                else:
                    bos_event = self._check_bos_pattern(prev_prev, prev, current)
                    if bos_event:
                        structure_events.append(bos_event)
            
            # Mevcut yapıyı belirle
            current_structure = self._determine_current_structure(recent_swings)
            
            return {
                'events': structure_events,
                'current_structure': current_structure,
                'analysis_timestamp': datetime.now().timestamp(),
                'swing_count_analyzed': len(recent_swings)
            }
            
        except Exception as e:
            logger.error(f"Market structure events analizi hatası: {e}", exc_info=True)
            return {'events': [], 'current_structure': 'UNCLEAR'}
    
    def analyze_fvg_enhanced_order_blocks(self, fvg_data: List[Dict[str, Any]], 
                                        order_blocks: Dict[str, Any], 
                                        htf_trend: str = 'sideways',
                                        current_price: float = 0,
                                        swing_points: Optional[List[Dict[str, Any]]] = None,
                                        structure_breaks: Optional[List[Dict[str, Any]]] = None,
                                        fibonacci_data: Optional[Dict[str, Any]] = None) -> List[Dict[str, Any]]:
        """
        FVG-Enhanced Order Blocks analizi yapar.
        
        YENİ: Gerçek ICT konseptine göre FVG + OB Confluence analizi
        
        ICT Kriterler:
        - BOS/MSS swing aralığı içinde FVG ve OB bulunmalı
        - Spatial confluence (aynı fiyat bölgesi) kontrolü
        - OTE seviyesinde bonus puanlama
        - Temporal proximity (zaman yakınlığı) kontrolü
        - HTF trend yönü ile uyumluluk
        
        Args:
            fvg_data: Fair Value Gap verileri
            order_blocks: Order Block verileri
            htf_trend: HTF trend yönü
            current_price: Güncel fiyat
            swing_points: Swing pivot noktaları
            structure_breaks: BOS/MSS kırılım verileri
            fibonacci_data: Fibonacci analiz verileri
            
        Returns:
            List[Dict]: Gerçek ICT kurallarına göre FVG-OB Confluence'ları
        """
        try:
            # Gerçek ICT konseptine göre FVG-OB Confluence analizi
            if self.fvg_ob_confluence_analyzer is None:
                from fvg_ob_confluence_analyzer import FVGOBConfluenceAnalyzer
                confluence_analyzer = FVGOBConfluenceAnalyzer()
            else:
                confluence_analyzer = self.fvg_ob_confluence_analyzer
            enhanced_obs = []
            
            if not isinstance(fvg_data, list) or not isinstance(order_blocks, dict):
                logger.warning("FVG veya Order Block verileri uygun formatta değil")
                return []
            
            if not fvg_data or not swing_points or not structure_breaks:
                logger.debug("FVG-OB confluence analizi için yeterli veri yok")
                return []
            
            logger.info(f"Gerçek ICT FVG-OB Confluence analizi - HTF Trend: {htf_trend.upper()}")
            
            # Bullish confluence analizi
            if htf_trend in ['bullish', 'sideways']:
                bull_confluences = confluence_analyzer.analyze_fvg_ob_confluence(
                    fvg_data=fvg_data,
                    order_blocks=order_blocks,
                    swing_points=swing_points,
                    structure_breaks=structure_breaks,
                    fibonacci_data=fibonacci_data,
                    trade_direction='bullish',
                    current_price=current_price
                )
                
                for confluence in bull_confluences:
                    enhanced_obs.append({
                        'type': 'FVG_OB_CONFLUENCE',
                        'direction': 'bull',
                        'ob_data': confluence['ob_data'],
                        'fvg_data': confluence['fvg_data'],
                        'confluence_score': confluence['confluence_score'],
                        'spatial_score': confluence['spatial_score'],
                        'temporal_score': confluence['temporal_score'],
                        'ote_score': confluence['ote_score'],
                        'distance_score': confluence['distance_score'],
                        'entry_price': confluence['entry_price'],
                        'swing_range': confluence['swing_range'],
                        'is_ote_enhanced': confluence['is_ote_enhanced'],
                        'total_score': confluence['confluence_score'],
                        'description': confluence['description'],
                        'htf_trend_aligned': True
                    })
                    logger.success(f"ICT Bull FVG-OB Confluence: Skor {confluence['confluence_score']:.1f}")
            
            # Bearish confluence analizi
            if htf_trend in ['bearish', 'sideways']:
                bear_confluences = confluence_analyzer.analyze_fvg_ob_confluence(
                    fvg_data=fvg_data,
                    order_blocks=order_blocks,
                    swing_points=swing_points,
                    structure_breaks=structure_breaks,
                    fibonacci_data=fibonacci_data,
                    trade_direction='bearish',
                    current_price=current_price
                )
                
                for confluence in bear_confluences:
                    enhanced_obs.append({
                        'type': 'FVG_OB_CONFLUENCE',
                        'direction': 'bear',
                        'ob_data': confluence['ob_data'],
                        'fvg_data': confluence['fvg_data'],
                        'confluence_score': confluence['confluence_score'],
                        'spatial_score': confluence['spatial_score'],
                        'temporal_score': confluence['temporal_score'],
                        'ote_score': confluence['ote_score'],
                        'distance_score': confluence['distance_score'],
                        'entry_price': confluence['entry_price'],
                        'swing_range': confluence['swing_range'],
                        'is_ote_enhanced': confluence['is_ote_enhanced'],
                        'total_score': confluence['confluence_score'],
                        'description': confluence['description'],
                        'htf_trend_aligned': True
                    })
                    logger.success(f"ICT Bear FVG-OB Confluence: Skor {confluence['confluence_score']:.1f}")
            
            logger.info(f"Toplam {len(enhanced_obs)} gerçek ICT FVG-OB Confluence tespit edildi")
            return enhanced_obs
            
        except Exception as e:
            logger.error(f"FVG-Enhanced OB analizi hatası: {e}", exc_info=True)
            return []
    
    def calculate_premium_signals(self, all_analysis_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Premium Signals (10+ puanlık mükemmel setups) hesaplar.
        
        Premium Signal kriterleri:
        - IDM Confirmed Hunt (+2.5)
        - FVG-Enhanced OB (+0.5)
        - HTF Structure alignment (+2.0)
        - Multiple timeframe confluence (+2.0)
        - External liquidity proximity (+1.5)
        - Round number confluence (+1.0)
        - Smart money confirmation (+1.5)
        
        Args:
            all_analysis_data: Tüm analiz verileri
            
        Returns:
            Dict: Premium signal analizi
        """
        try:
            total_score = 0.0
            score_breakdown = []
            
            # 1. IDM Confirmed Hunt (+2.5)
            idm_patterns = all_analysis_data.get('idm_patterns', [])
            idm_confirmed_count = len([p for p in idm_patterns if p.get('quality') == 'IDM_CONFIRMED'])
            if idm_confirmed_count > 0:
                idm_score = min(idm_confirmed_count * 2.5, 5.0)  # Max 5.0
                total_score += idm_score
                score_breakdown.append(f"🥇 IDM Confirmed Hunt: +{idm_score}")
            
            # 2. FVG-Enhanced OB (+0.5)
            enhanced_obs = all_analysis_data.get('fvg_enhanced_obs', [])
            if enhanced_obs:
                fvg_score = min(len(enhanced_obs) * 0.5, 1.0)  # Max 1.0
                total_score += fvg_score
                score_breakdown.append(f"✨ FVG-Enhanced OB: +{fvg_score}")
            
            # 3. HTF Structure alignment (+2.0)
            structure_events = all_analysis_data.get('structure_events', {}).get('events', [])
            mss_events = [e for e in structure_events if e.get('type') == 'MSS']
            if mss_events:
                htf_score = 2.0
                total_score += htf_score
                score_breakdown.append(f"📈 HTF Structure MSS: +{htf_score}")
            
            # 4. External liquidity proximity (+1.5)
            # External liquidity verisi liquidity_analysis içinde
            liquidity_analysis = all_analysis_data.get('liquidity_analysis', {})
            external_liquidity = liquidity_analysis.get('external_liquidity', {}) if isinstance(liquidity_analysis, dict) else {}
            hunt_patterns = external_liquidity.get('hunt_patterns', [])
            high_quality_hunts = [h for h in hunt_patterns if h.get('hunt_quality') in ['IDM_CONFIRMED', 'HIGH']]
            if high_quality_hunts:
                ext_liq_score = 1.5
                total_score += ext_liq_score
                score_breakdown.append(f"🎯 External Liquidity Hunt: +{ext_liq_score}")
            
            # 5. Round number confluence (+1.0)
            round_number_hits = 0
            for pattern in idm_patterns:
                if pattern.get('round_number', False):
                    round_number_hits += 1
            if round_number_hits > 0:
                round_score = 1.0
                total_score += round_score
                score_breakdown.append(f"🎱 Round Number Confluence: +{round_score}")
            
            # 6. Multi-timeframe confluence (+2.0)
            # 12H & 24H Timeframe analizi
            timeframe_alignment = self._check_multi_timeframe_alignment(all_analysis_data)
            if timeframe_alignment['aligned']:
                mtf_score = 2.0
                total_score += mtf_score
                score_breakdown.append(f"⏱️ Multi-Timeframe Alignment: +{mtf_score}")
            
            # Premium Signal belirleme
            is_premium = total_score >= 10.0
            
            result = {
                'is_premium_signal': is_premium,
                'total_score': total_score,
                'score_breakdown': score_breakdown,
                'premium_threshold': 10.0,
                'quality': 'PREMIUM' if is_premium else 'STANDARD',
                'analysis_timestamp': datetime.now().timestamp()
            }
            
            if is_premium:
                logger.success(f"🏆 PREMIUM SIGNAL tespit edildi! Toplam Skor: {total_score:.1f}")
            else:
                logger.info(f"Standard sinyal: Toplam Skor: {total_score:.1f} (Premium için ≥10.0 gerekli)")
            
            return result
            
        except Exception as e:
            logger.error(f"Premium signals analizi hatası: {e}", exc_info=True)
            return {'is_premium_signal': False, 'total_score': 0.0, 'score_breakdown': []}
    
    # Yardımcı metodlar
    def _check_min_price_difference(self, swing: Dict[str, Any], all_swings: List[Dict[str, Any]]) -> bool:
        """Minimal fiyat farkı kontrolü (%0.05)."""
        swing_price = swing.get('price', 0)
        
        for other_swing in all_swings:
            if other_swing == swing:
                continue
            other_price = other_swing.get('price', 0)
            if other_price > 0:
                diff_pct = abs(swing_price - other_price) / other_price * 100
                if diff_pct < self.min_price_diff_pct:
                    return False
        return True
    
    def _check_trend_alignment(self, swing: Dict[str, Any], current_trend: str) -> bool:
        """Swing'in mevcut trend ile uyumunu kontrol eder."""
        swing_type = swing.get('type', '')
        
        if current_trend == 'bullish':
            return swing_type in ['HH', 'HL']  # Higher High, Higher Low
        elif current_trend == 'bearish':
            return swing_type in ['LH', 'LL']  # Lower High, Lower Low
        else:
            return True  # Sideways'te tüm swing'ler geçerli
    
    def _check_bos_pattern(self, prev_prev: Dict, prev: Dict, current: Dict) -> Optional[Dict[str, Any]]:
        """BOS (Break of Structure) pattern kontrolü."""
        try:
            # BOS Bullish patterns
            if (prev_prev.get('type') == 'HL' and 
                prev.get('type') == 'HH' and 
                current.get('type') == 'HL' and
                current.get('price', 0) > prev_prev.get('price', 0)):
                
                return {
                    'type': 'BOS',
                    'direction': 'BULLISH',
                    'pattern': 'HL → HH → HL (Higher)',
                    'confidence': 0.8,
                    'swing_sequence': [prev_prev, prev, current],
                    'timestamp': current.get('timestamp', 0),
                    'description': 'Break of Structure - Trend devamı (Bullish)'
                }
            
            # BOS Bearish patterns
            elif (prev_prev.get('type') == 'LH' and 
                  prev.get('type') == 'LL' and 
                  current.get('type') == 'LH' and
                  current.get('price', 0) < prev_prev.get('price', 0)):
                
                return {
                    'type': 'BOS',
                    'direction': 'BEARISH',
                    'pattern': 'LH → LL → LH (Lower)',
                    'confidence': 0.8,
                    'swing_sequence': [prev_prev, prev, current],
                    'timestamp': current.get('timestamp', 0),
                    'description': 'Break of Structure - Trend devamı (Bearish)'
                }
            
            return None
            
        except Exception as e:
            logger.error(f"BOS pattern kontrolü hatası: {e}")
            return None
    
    def _determine_current_structure(self, recent_swings: List[Dict[str, Any]]) -> str:
        """Son swing'lere göre mevcut piyasa yapısını belirler."""
        try:
            if len(recent_swings) < 2:
                return 'UNCLEAR'
            
            last_two = recent_swings[-2:]
            
            # Higher High/Higher Low pattern
            if (last_two[0].get('type') in ['HL', 'HH'] and 
                last_two[1].get('type') in ['HL', 'HH'] and
                last_two[1].get('price', 0) > last_two[0].get('price', 0)):
                return 'BULLISH_STRUCTURE'
            
            # Lower High/Lower Low pattern
            elif (last_two[0].get('type') in ['LH', 'LL'] and 
                  last_two[1].get('type') in ['LH', 'LL'] and
                  last_two[1].get('price', 0) < last_two[0].get('price', 0)):
                return 'BEARISH_STRUCTURE'
            
            else:
                return 'CORRECTIVE_STRUCTURE'
                
        except Exception as e:
            logger.error(f"Current structure belirleme hatası: {e}")
            return 'UNCLEAR'
    
    # ESKİ _check_fvg_ob_confluence metodu kaldırıldı
    # Artık gerçek ICT konseptine göre FVGOBConfluenceAnalyzer kullanılıyor
    
    def _check_multi_timeframe_alignment(self, all_analysis_data: Dict[str, Any]) -> Dict[str, Any]:
        """12H & 24H timeframe alignment kontrolü."""
        try:
            # 12H ve 24H zaman dilimlerindeki swing analizi
            h12_swings = all_analysis_data.get('swing_analysis_12h', {}).get('major_pivots', [])
            h24_swings = all_analysis_data.get('swing_analysis_24h', {}).get('major_pivots', [])
            
            # Her iki timeframe'de de aynı yönde swing var mı?
            h12_trend = self._get_timeframe_trend(h12_swings)
            h24_trend = self._get_timeframe_trend(h24_swings)
            
            aligned = (h12_trend == h24_trend and h12_trend != 'UNCLEAR')
            
            return {
                'aligned': aligned,
                'h12_trend': h12_trend,
                'h24_trend': h24_trend,
                'alignment_strength': 1.0 if aligned else 0.0
            }
            
        except Exception as e:
            logger.error(f"Multi-timeframe alignment kontrolü hatası: {e}")
            return {'aligned': False, 'h12_trend': 'UNCLEAR', 'h24_trend': 'UNCLEAR'}
    
    def _get_timeframe_trend(self, swings: List[Dict[str, Any]]) -> str:
        """Belirli bir timeframe'deki trend yönünü belirler."""
        try:
            if len(swings) < 2:
                return 'UNCLEAR'
            
            recent_swings = swings[-3:] if len(swings) >= 3 else swings
            
            hh_hl_count = len([s for s in recent_swings if s.get('type') in ['HH', 'HL']])
            lh_ll_count = len([s for s in recent_swings if s.get('type') in ['LH', 'LL']])
            
            if hh_hl_count > lh_ll_count:
                return 'BULLISH'
            elif lh_ll_count > hh_hl_count:
                return 'BEARISH'
            else:
                return 'UNCLEAR'
                
        except Exception as e:
            logger.error(f"Timeframe trend belirleme hatası: {e}")
            return 'UNCLEAR'

    def calculate_fvg_enhancement_score(self, fvg: Dict[str, Any], 
                                       order_block: Dict[str, Any],
                                       candles: Optional[pd.DataFrame] = None) -> float:
        """
        FVG ve Order Block arasındaki ilişkiyi analiz ederek 0-10 arası enhancement skoru hesaplar.
        
        Analiz Faktörleri:
        1. Yakınlık (Proximity): 0-3 puan - FVG ve OB arasındaki mesafe
        2. Büyüklük (Magnitude): 0-3 puan - FVG ve OB boyutlarının uyumu  
        3. Zamanlama (Timing): 0-2 puan - Aynı impulse hareket içinde oluşma
        4. Konum (Position): 0-2 puan - FVG'nin OB içinde veya bitişik konumu
        
        Args:
            fvg: FVG verisi (top, bottom, timestamp, type)
            order_block: Order Block verisi (high, low, timestamp)
            candles: Opsiyonel - zamanlama analizi için
            
        Returns:
            0-10 arası FVG enhancement skoru
        """
        try:
            score = 0.0
            
            # 1. YAKINLIK ANALIZI (0-3 puan)
            proximity_score = self._calculate_proximity_score(fvg, order_block)
            score += proximity_score
            
            # 2. BÜYÜKLÜK ANALIZI (0-3 puan)
            magnitude_score = self._calculate_magnitude_score(fvg, order_block)
            score += magnitude_score
            
            # 3. ZAMANLAMA ANALIZI (0-2 puan)
            timing_score = self._calculate_timing_score(fvg, order_block, candles)
            score += timing_score
            
            # 4. KONUM ANALIZI (0-2 puan)
            position_score = self._calculate_position_score(fvg, order_block)
            score += position_score
            
            # Skor sınırlaması
            final_score = min(10.0, max(0.0, score))
            
            logger.debug(f"FVG Enhancement Score: {final_score:.2f} "
                        f"(Proximity: {proximity_score:.1f}, "
                        f"Magnitude: {magnitude_score:.1f}, "
                        f"Timing: {timing_score:.1f}, "
                        f"Position: {position_score:.1f})")
            
            return final_score
            
        except Exception as e:
            logger.error(f"FVG enhancement score hesaplama hatası: {e}")
            return 0.0

    def _calculate_proximity_score(self, fvg: Dict[str, Any], 
                                  order_block: Dict[str, Any]) -> float:
        """
        FVG ve OB arasındaki yakınlığı puanlar (0-3 puan).
        
        Puanlama:
        - 3.0: FVG tamamen OB içinde
        - 2.5: FVG kısmen OB ile kesişiyor
        - 2.0: FVG, OB'ye bitişik (gap yok)
        - 1.5: FVG, OB'ye çok yakın (<%1 mesafe)
        - 1.0: FVG, OB'ye yakın (<%3 mesafe)
        - 0.5: FVG, OB'ye orta mesafede (<%5 mesafe)
        - 0.0: FVG, OB'den uzak (>%5 mesafe)
        """
        try:
            fvg_top = float(fvg.get('top', 0))
            fvg_bottom = float(fvg.get('bottom', 0))
            ob_high = float(order_block.get('high', 0))
            ob_low = float(order_block.get('low', 0))
            
            # Kesişim kontrolü
            overlap_top = min(fvg_top, ob_high)
            overlap_bottom = max(fvg_bottom, ob_low)
            
            if overlap_top > overlap_bottom:
                # Kesişim var
                fvg_size = fvg_top - fvg_bottom
                overlap_size = overlap_top - overlap_bottom
                overlap_pct = (overlap_size / fvg_size) * 100 if fvg_size > 0 else 0
                
                if overlap_pct >= 90:
                    return 3.0  # FVG tamamen OB içinde
                elif overlap_pct >= 50:
                    return 2.5  # FVG kısmen kesişiyor
                else:
                    return 2.0  # FVG kısmen kesişiyor ama az
            
            # Kesişim yok, mesafe hesapla
            if fvg_top < ob_low:
                # FVG, OB'nin altında
                distance = ob_low - fvg_top
                reference_price = ob_low
            elif fvg_bottom > ob_high:
                # FVG, OB'nin üstünde
                distance = fvg_bottom - ob_high
                reference_price = ob_high
            else:
                # Bu durumda aslında kesişim var
                return 2.0
            
            distance_pct = (distance / reference_price) * 100
            
            if distance_pct <= 0.1:
                return 2.0  # Bitişik (gap yok)
            elif distance_pct <= 1.0:
                return 1.5  # Çok yakın
            elif distance_pct <= 3.0:
                return 1.0  # Yakın
            elif distance_pct <= 5.0:
                return 0.5  # Orta mesafe
            else:
                return 0.0  # Uzak
                
        except Exception as e:
            logger.error(f"Proximity score hesaplama hatası: {e}")
            return 0.0

    def _calculate_magnitude_score(self, fvg: Dict[str, Any], 
                                  order_block: Dict[str, Any]) -> float:
        """
        FVG ve OB boyutlarının uyumunu puanlar (0-3 puan).
        
        Puanlama:
        - 3.0: FVG ve OB boyutları çok uyumlu (0.5x - 2.0x arası)
        - 2.5: FVG ve OB boyutları uyumlu (0.3x - 3.0x arası) 
        - 2.0: FVG ve OB boyutları kabul edilebilir (0.2x - 4.0x arası)
        - 1.0: FVG ve OB boyutları az uyumlu (0.1x - 6.0x arası)
        - 0.5: FVG ve OB boyutları uyumsuz (0.05x - 10.0x arası)
        - 0.0: FVG ve OB boyutları çok uyumsuz
        """
        try:
            fvg_size = float(fvg.get('top', 0)) - float(fvg.get('bottom', 0))
            ob_size = float(order_block.get('high', 0)) - float(order_block.get('low', 0))
            
            if fvg_size <= 0 or ob_size <= 0:
                return 0.0
            
            # Boyut oranını hesapla (küçük / büyük)
            size_ratio = min(fvg_size, ob_size) / max(fvg_size, ob_size)
            
            if size_ratio >= 0.5:
                return 3.0  # Çok uyumlu (50% - 100% arası)
            elif size_ratio >= 0.33:
                return 2.5  # Uyumlu (33% - 50% arası)
            elif size_ratio >= 0.2:
                return 2.0  # Kabul edilebilir (20% - 33% arası)
            elif size_ratio >= 0.1:
                return 1.0  # Az uyumlu (10% - 20% arası)
            elif size_ratio >= 0.05:
                return 0.5  # Uyumsuz (5% - 10% arası)
            else:
                return 0.0  # Çok uyumsuz (<5%)
                
        except Exception as e:
            logger.error(f"Magnitude score hesaplama hatası: {e}")
            return 0.0

    def _calculate_timing_score(self, fvg: Dict[str, Any], 
                               order_block: Dict[str, Any],
                               candles: Optional[pd.DataFrame] = None) -> float:
        """
        FVG ve OB'nin aynı impulse hareket içinde oluşma zamanlamasını puanlar (0-2 puan).
        
        Puanlama:
        - 2.0: Aynı impulse leg içinde oluşmuş (1-5 mum farkı)
        - 1.5: Yakın zamanda oluşmuş (6-10 mum farkı)
        - 1.0: Aynı swing içinde oluşmuş (11-20 mum farkı)
        - 0.5: Farklı ama yakın swing'lerde oluşmuş (21-50 mum farkı)
        - 0.0: Farklı zamanlarda oluşmuş (>50 mum farkı)
        """
        try:
            fvg_timestamp = fvg.get('timestamp')
            ob_timestamp = order_block.get('timestamp')
            
            # Timestamp kontrolü
            if not fvg_timestamp or not ob_timestamp:
                return 1.0  # Varsayılan orta puan
            
            # Zaman farkını hesapla
            if isinstance(fvg_timestamp, str):
                fvg_time = pd.to_datetime(fvg_timestamp)
            else:
                fvg_time = fvg_timestamp
                
            if isinstance(ob_timestamp, str):
                ob_time = pd.to_datetime(ob_timestamp)
            else:
                ob_time = ob_timestamp
            
            time_diff = abs((fvg_time - ob_time).total_seconds())
            
            # Candle verisi varsa mum sayısı olarak hesapla
            if candles is not None and not candles.empty:
                # Candle aralığını tahmin et (5m, 15m, 1h, 4h)
                time_diff_minutes = time_diff / 60
                
                # Timeframe tahmini
                candle_count = len(candles)
                if candle_count > 1:
                    first_time = pd.to_datetime(candles.iloc[0]['timestamp'])
                    second_time = pd.to_datetime(candles.iloc[1]['timestamp'])
                    candle_interval_minutes = (second_time - first_time).total_seconds() / 60
                    
                    candle_diff = time_diff_minutes / candle_interval_minutes
                    
                    if candle_diff <= 5:
                        return 2.0  # Aynı impulse leg
                    elif candle_diff <= 10:
                        return 1.5  # Yakın zaman
                    elif candle_diff <= 20:
                        return 1.0  # Aynı swing
                    elif candle_diff <= 50:
                        return 0.5  # Yakın swing'ler
                    else:
                        return 0.0  # Farklı zamanlar
            
            # Candle verisi yoksa saat bazında hesapla
            time_diff_hours = time_diff / 3600
            
            if time_diff_hours <= 4:
                return 2.0  # 4 saat içinde
            elif time_diff_hours <= 12:
                return 1.5  # 12 saat içinde
            elif time_diff_hours <= 24:
                return 1.0  # 24 saat içinde
            elif time_diff_hours <= 48:
                return 0.5  # 48 saat içinde
            else:
                return 0.0  # 48 saatten fazla
                
        except Exception as e:
            logger.error(f"Timing score hesaplama hatası: {e}")
            return 1.0  # Varsayılan orta puan

    def _calculate_position_score(self, fvg: Dict[str, Any], 
                                 order_block: Dict[str, Any]) -> float:
        """
        FVG'nin OB'ye göre konumsal uygunluğunu puanlar (0-2 puan).
        
        Puanlama:
        - 2.0: Mükemmel konum (Bullish FVG + Bullish OB, veya Bearish FVG + Bearish OB)
        - 1.5: İyi konum (FVG, OB'nin güçlü tarafında)
        - 1.0: Kabul edilebilir konum (FVG, OB'ye yakın)
        - 0.5: Zayıf konum (FVG, OB'nin zayıf tarafında)
        - 0.0: Kötü konum (Ters yönde FVG ve OB)
        """
        try:
            fvg_type = fvg.get('type', '').lower()
            ob_type = order_block.get('type', '').lower()
            
            # Type bilgisi eksikse, fiyat konumuna göre tahmin et
            if not fvg_type:
                fvg_mid = (float(fvg.get('top', 0)) + float(fvg.get('bottom', 0))) / 2
                ob_mid = (float(order_block.get('high', 0)) + float(order_block.get('low', 0))) / 2
                fvg_type = 'bullish' if fvg_mid > ob_mid else 'bearish'
            
            if not ob_type:
                # Order block type bilgisi yoksa neutral kabul et
                ob_type = 'neutral'
            
            # Tip uyumu kontrolü
            if ('bull' in fvg_type and 'bull' in ob_type) or \
               ('bear' in fvg_type and 'bear' in ob_type):
                return 2.0  # Mükemmel uyum
            
            # Konumsal analiz
            fvg_top = float(fvg.get('top', 0))
            fvg_bottom = float(fvg.get('bottom', 0))
            ob_high = float(order_block.get('high', 0))
            ob_low = float(order_block.get('low', 0))
            
            fvg_mid = (fvg_top + fvg_bottom) / 2
            ob_mid = (ob_high + ob_low) / 2
            
            # FVG'nin OB'ye göre konumu
            if fvg_mid > ob_high:
                # FVG, OB'nin üstünde
                if 'bull' in fvg_type:
                    return 1.5  # Bullish FVG üstte, iyi
                else:
                    return 0.5  # Bearish FVG üstte, zayıf
            elif fvg_mid < ob_low:
                # FVG, OB'nin altında
                if 'bear' in fvg_type:
                    return 1.5  # Bearish FVG altta, iyi
                else:
                    return 0.5  # Bullish FVG altta, zayıf
            else:
                # FVG, OB içinde veya yakınında
                return 1.0  # Kabul edilebilir
                
        except Exception as e:
            logger.error(f"Position score hesaplama hatası: {e}")
            return 1.0  # Varsayılan orta puan

    def analyze_fvg_ob_relationships(self, fvg_list: List[Dict[str, Any]], 
                                   order_block_data: Dict[str, Any],
                                   candles: Optional[pd.DataFrame] = None) -> List[Dict[str, Any]]:
        """
        Tüm FVG'ler ve Order Block'lar arasındaki ilişkileri analiz eder.
        
        Args:
            fvg_list: FVG listesi
            order_block_data: Order Block analiz sonuçları
            candles: Opsiyonel candle verisi
            
        Returns:
            Enhanced FVG-OB ilişkileri listesi
        """
        try:
            enhanced_relationships = []
            
            # Order Block'ları al
            all_obs = []
            
            bullish_obs = order_block_data.get('bullish_obs', [])
            bearish_obs = order_block_data.get('bearish_obs', [])
            
            if isinstance(bullish_obs, list):
                all_obs.extend(bullish_obs)
            if isinstance(bearish_obs, list):
                all_obs.extend(bearish_obs)
            
            logger.debug(f"FVG-OB ilişki analizi: {len(fvg_list)} FVG, {len(all_obs)} OB")
            
            # Her FVG için en iyi OB eşleşmelerini bul
            for fvg in fvg_list:
                best_relationships = []
                
                for ob in all_obs:
                    enhancement_score = self.calculate_fvg_enhancement_score(fvg, ob, candles)
                    
                    if enhancement_score >= 2.0:  # Minimum kalite eşiği
                        relationship = {
                            'fvg': fvg,
                            'order_block': ob,
                            'enhancement_score': enhancement_score,
                            'quality_rating': self._get_enhancement_quality_rating(enhancement_score),
                            'proximity_details': self._get_proximity_details(fvg, ob),
                            'magnitude_details': self._get_magnitude_details(fvg, ob)
                        }
                        best_relationships.append(relationship)
                
                # En iyi ilişkileri puanla ve sırala
                best_relationships.sort(key=lambda x: x['enhancement_score'], reverse=True)
                enhanced_relationships.extend(best_relationships[:3])  # En iyi 3 ilişki
            
            logger.success(f"FVG-OB Enhanced ilişkiler: {len(enhanced_relationships)} yüksek kalite ilişki")
            
            return enhanced_relationships
            
        except Exception as e:
            logger.error(f"FVG-OB ilişki analizi hatası: {e}")
            return []

    def _get_enhancement_quality_rating(self, score: float) -> str:
        """Enhancement score'a göre kalite derecelendirmesi."""
        if score >= 8.0:
            return "EXCELLENT"
        elif score >= 6.0:
            return "VERY_GOOD"
        elif score >= 4.0:
            return "GOOD"
        elif score >= 2.0:
            return "FAIR"
        else:
            return "POOR"

    def _get_proximity_details(self, fvg: Dict[str, Any], order_block: Dict[str, Any]) -> Dict[str, Any]:
        """FVG ve OB arasındaki yakınlık detayları."""
        try:
            fvg_top = float(fvg.get('top', 0))
            fvg_bottom = float(fvg.get('bottom', 0))
            ob_high = float(order_block.get('high', 0))
            ob_low = float(order_block.get('low', 0))
            
            # Kesişim kontrolü
            overlap_top = min(fvg_top, ob_high)
            overlap_bottom = max(fvg_bottom, ob_low)
            
            has_overlap = overlap_top > overlap_bottom
            
            if has_overlap:
                overlap_size = overlap_top - overlap_bottom
                fvg_size = fvg_top - fvg_bottom
                overlap_pct = (overlap_size / fvg_size) * 100 if fvg_size > 0 else 0
                
                return {
                    'has_overlap': True,
                    'overlap_percentage': overlap_pct,
                    'overlap_size': overlap_size,
                    'relationship_type': 'intersecting'
                }
            else:
                # Mesafe hesapla
                if fvg_top < ob_low:
                    distance = ob_low - fvg_top
                    position = 'below'
                else:
                    distance = fvg_bottom - ob_high
                    position = 'above'
                
                distance_pct = (distance / min(ob_high, fvg_top)) * 100
                
                return {
                    'has_overlap': False,
                    'distance': distance,
                    'distance_percentage': distance_pct,
                    'position': position,
                    'relationship_type': 'adjacent' if distance_pct <= 1.0 else 'separated'
                }
                
        except Exception as e:
            logger.error(f"Proximity details hesaplama hatası: {e}")
            return {'has_overlap': False, 'relationship_type': 'unknown'}

    def _get_magnitude_details(self, fvg: Dict[str, Any], order_block: Dict[str, Any]) -> Dict[str, Any]:
        """FVG ve OB boyut uyumu detayları."""
        try:
            fvg_size = float(fvg.get('top', 0)) - float(fvg.get('bottom', 0))
            ob_size = float(order_block.get('high', 0)) - float(order_block.get('low', 0))
            
            if fvg_size <= 0 or ob_size <= 0:
                return {'size_ratio': 0, 'compatibility': 'unknown'}
            
            size_ratio = min(fvg_size, ob_size) / max(fvg_size, ob_size)
            
            if size_ratio >= 0.5:
                compatibility = 'highly_compatible'
            elif size_ratio >= 0.33:
                compatibility = 'compatible'
            elif size_ratio >= 0.2:
                compatibility = 'moderately_compatible'
            elif size_ratio >= 0.1:
                compatibility = 'weakly_compatible'
            else:
                compatibility = 'incompatible'
            
            return {
                'fvg_size': fvg_size,
                'ob_size': ob_size,
                'size_ratio': size_ratio,
                'compatibility': compatibility,
                'larger_element': 'fvg' if fvg_size > ob_size else 'order_block'
            }
            
        except Exception as e:
            logger.error(f"Magnitude details hesaplama hatası: {e}")
            return {'size_ratio': 0, 'compatibility': 'unknown'}
