# ifvg_analyzer.py

import pandas as pd
from loguru import logger
from typing import List, Dict, Any, Optional

class IFVGAnalyzer:
    """
    Inversion Fair Value Gap (iFVG) analizi yapar.

    ICT Konsepti:
    - Bir FVG (Fair Value Gap), fiyat tarafından destek/direnç olarak saygı görmeyip
      tamamen kırıldığında rolünü tersine çevirir ve bir iFVG'ye dönüşür.
    - Bullish FVG (destek) kırıldığında, Bearish iFVG (direnç) olur.
    - Bearish FVG (direnç) kırıldığında, Bullish iFVG (destek) olur.
    - Bu iFVG seviyeleri, fiyat geri çekildiğinde test edilme eğilimindedir.
    """

    def __init__(self, tolerance_pct: float = 0.01):
        """
        IFVGAnalyzer'ı başlatır.

        Args:
            tolerance_pct (float): FVG kırılımı için mum kapanış toleransı (yüzde).
        """
        self.tolerance_pct = tolerance_pct / 100
        logger.info("Inversion FVG (iFVG) Analyzer başlatıldı.")

    def analyze(self, candles: pd.DataFrame, fvg_list: List[Dict[str, Any]]) -> Dict[str, Any]:
        """
        Verilen FVG'leri analiz ederek iFVG'leri tespit eder.

        Args:
            candles (pd.DataFrame): Mum verileri. 'timestamp', 'open', 'high', 'low', 'close' içermelidir.
            fvg_list (List[Dict[str, Any]]): FvgAnalyzer'dan gelen FVG listesi.

        Returns:
            Dict[str, Any]: iFVG analiz sonuçları (inverse_fvgs listesi içerir).
        """
        if candles is None or candles.empty or not fvg_list:
            logger.debug("iFVG analizi için yetersiz veri (mum veya FVG listesi eksik).")
            return {'inverse_fvgs': []}

        inverted_fvgs = []
        processed_fvg_indices = set()

        # Her FVG'yi kontrol et
        for i, fvg in enumerate(fvg_list):
            if i in processed_fvg_indices:
                continue

            fvg_type = fvg.get('type', '').lower()
            fvg_start_index = fvg.get('start_index', 0)
            fvg_top = fvg.get('top', 0)
            fvg_bottom = fvg.get('bottom', 0)

            if not all([fvg_type, fvg_start_index, fvg_top, fvg_bottom]):
                continue

            # FVG oluştuktan sonraki mumları analiz et
            subsequent_candles = candles.iloc[fvg_start_index + 1:]

            for candle_index, candle in subsequent_candles.iterrows():
                close_price = candle['close']
                inversion_found = False

                # Bullish FVG'nin aşağı yönlü kırılımı (Bearish iFVG oluşumu)
                if 'bullish' in fvg_type:
                    # Mum, FVG'nin alt sınırının altında mı kapandı?
                    if close_price < fvg_bottom * (1 - self.tolerance_pct):
                        inverted_fvg = self._create_ifvg_dict(fvg, candle, 'bearish_resistance')
                        inverted_fvgs.append(inverted_fvg)
                        inversion_found = True
                        logger.success(f"🔥 Bearish iFVG (Direnç) tespit edildi. Orijinal Bullish FVG @ {fvg_bottom:.4f}-{fvg_top:.4f} kırıldı.")

                # Bearish FVG'nin yukarı yönlü kırılımı (Bullish iFVG oluşumu)
                elif 'bearish' in fvg_type:
                    # Mum, FVG'nin üst sınırının üstünde mi kapandı?
                    if close_price > fvg_top * (1 + self.tolerance_pct):
                        inverted_fvg = self._create_ifvg_dict(fvg, candle, 'bullish_support')
                        inverted_fvgs.append(inverted_fvg)
                        inversion_found = True
                        logger.success(f"🔥 Bullish iFVG (Destek) tespit edildi. Orijinal Bearish FVG @ {fvg_bottom:.4f}-{fvg_top:.4f} kırıldı.")

                if inversion_found:
                    processed_fvg_indices.add(i)
                    break # Bu FVG için analizi bitir ve bir sonrakine geç

        logger.info(f"iFVG analizi tamamlandı. Toplam {len(inverted_fvgs)} iFVG tespit edildi.")
        
        return {
            'inverse_fvgs': inverted_fvgs,
            'total_count': len(inverted_fvgs),
            'analysis_timestamp': pd.Timestamp.now().isoformat()
        }

    def _create_ifvg_dict(self, original_fvg: Dict[str, Any], inversion_candle: pd.Series, new_role: str) -> Dict[str, Any]:
        """
        Tespit edilen iFVG için bir sözlük oluşturur.
        """
        return {
            'type': 'iFVG',
            'role': new_role,  # 'bullish_support' veya 'bearish_resistance'
            'top': original_fvg['top'],
            'bottom': original_fvg['bottom'],
            'original_fvg_type': original_fvg['type'],
            'inversion_timestamp': inversion_candle['timestamp'],
            'inversion_price': inversion_candle['close'],
            'inversion_candle_index': inversion_candle.name,
            'is_tested': False, # Başlangıçta test edilmemiş
            'test_count': 0
        }
