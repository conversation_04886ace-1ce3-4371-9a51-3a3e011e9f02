# invalidation_manager.py

from loguru import logger
from typing import Dict, Any, List, Optional

class InvalidationManager:
    """
    Bekleyen ticaret emirlerinin (limit emirler) ne zaman geçersiz kılınacağını
    belirleyen kuralları yönetir. Bu, piyasa koşulları beklenenin tersi yönde
    değiştiğinde gereksiz risk almayı önler.
    """

    def __init__(self):
        """InvalidationManager'ı başlatır."""
        logger.info("Geçersiz Kılma Yöneticisi (InvalidationManager) başlatıldı.")

    # invalidation_manager.py

from loguru import logger
from typing import Dict, Any, List, Optional

class InvalidationManager:
    """
    Bekleyen ticaret emirlerinin (limit emirler) ne zaman geçersiz kılınacağını
    belirleyen kuralları yönetir. Bu, piyasa koşulları beklenenin tersi yönde
    değiştiğinde gereksiz risk almayı önler.
    """

    def __init__(self):
        """InvalidationManager'ı başlatır."""
        logger.info("Geçersiz Kılma Yöneticisi (InvalidationManager) başlatıldı.")

    def check_invalidation_conditions(
        self,
        pending_trade: Dict[str, Any],
        all_symbol_data: Dict[str, Any],
        current_price: float
    ) -> Optional[str]:
        """
        Bekleyen bir ticaretin geçersiz kılınıp kılınmadığını kontrol eder.
        ICT konseptlerine göre gelişmiş invalidation rules uygular.

        Args:
            pending_trade (Dict[str, Any]): Kontrol edilecek bekleyen işlem.
            all_symbol_data (Dict[str, Any]): İlgili sembol için tüm analiz verileri.
            current_price (float): Sembolün güncel fiyatı.

        Returns:
            Optional[str]: Eğer işlem geçersiz kılındıysa, geçersiz kılma nedenini
                           bir string olarak döndürür. Aksi takdirde None döndürür.
        """
        trade_direction = pending_trade.get('direction', '').lower()
        if not trade_direction:
            return "İşlem yönü belirsiz."

        symbol = pending_trade.get('symbol', 'N/A')
        
        # Piyasa yapısı analizini al
        structure_analysis = all_symbol_data.get('structure_analysis', {})
        recent_structure_events = structure_analysis.get('events', [])

        # 1. Karşıt Yönlü Güçlü Piyasa Yapısı Kırılımı (MSB) Kontrolü
        if recent_structure_events:
            for event in recent_structure_events[-3:]: # Son 3 olayı kontrol et
                event_type = event.get('type', '').upper()
                event_direction = event.get('direction', '').lower()
                
                # Sadece güçlü kırılımları (MSB) dikkate al
                if 'MSB' in event_type:
                    # Long sinyal için Bearish MSB oluşursa -> İPTAL
                    if trade_direction in ['bull', 'bullish'] and event_direction == 'bearish':
                        reason = f"Bearish MSB @ {event.get('price')} tespit edildi."
                        logger.warning(f"[{symbol}] PENDING LONG sinyali iptal ediliyor: {reason}")
                        return reason
                        
                    # Short sinyal için Bullish MSB oluşursa -> İPTAL
                    if trade_direction in ['bear', 'bearish'] and event_direction == 'bullish':
                        reason = f"Bullish MSB @ {event.get('price')} tespit edildi."
                        logger.warning(f"[{symbol}] PENDING SHORT sinyali iptal ediliyor: {reason}")
                        return reason

        # Diğer potansiyel geçersiz kılma kontrolleri buraya eklenebilir
        # (Örn: Karşıt yönde oluşan yüksek kaliteli bir FVG veya Order Block)

        return None

    def _check_opposite_poi_test(self, pending_trade: Dict[str, Any], 
                                all_symbol_data: Dict[str, Any], 
                                current_price: float, 
                                is_old_signal: bool) -> Optional[str]:
        """
        Karşıt POI (Point of Interest) test kontrolü.
        Eğer bekleyen long sinyal varsa ve fiyat yukarıdaki bearish POI'yi test ederse, 
        bu long sinyalin geçerliliğini zayıflatır.
        """
        try:
            trade_direction = pending_trade.get('direction', '').lower()
            symbol = pending_trade.get('symbol', 'N/A')
            entry_price = float(pending_trade.get('entry_price', 0))
            
            # Order Block analizi al
            order_block_data = all_symbol_data.get('order_block_analysis', {})
            if not isinstance(order_block_data, dict):
                return None
            
            bullish_obs = order_block_data.get('bullish_obs', [])
            bearish_obs = order_block_data.get('bearish_obs', [])
            
            # Long pozisyon için Bearish OB testi
            if trade_direction in ['bullish', 'bull', 'long']:
                for bearish_ob in bearish_obs:
                    if not isinstance(bearish_ob, dict):
                        continue
                        
                    ob_high = bearish_ob.get('high', 0)
                    ob_low = bearish_ob.get('low', 0)
                    ob_strength = bearish_ob.get('strength', 'medium')
                    
                    # Fiyat bearish OB'nin üzerinde ve test ediyor mu?
                    if ob_high > entry_price and ob_low <= current_price <= ob_high:
                        # Güçlü bearish OB testi -> kesin iptal
                        if ob_strength in ['strong', 'high', 'very_strong']:
                            return f"Long sinyal iptal: Güçlü Bearish OB testi ({ob_strength}) @ {ob_high:.4f}"
                        # Eski sinyaller için orta kuvvet de yeterli
                        elif is_old_signal and ob_strength in ['medium']:
                            return f"Eski long sinyal iptal: Orta Bearish OB testi @ {ob_high:.4f}"
            
            # Short pozisyon için Bullish OB testi
            elif trade_direction in ['bearish', 'bear', 'short']:
                for bullish_ob in bullish_obs:
                    if not isinstance(bullish_ob, dict):
                        continue
                        
                    ob_high = bullish_ob.get('high', 0)
                    ob_low = bullish_ob.get('low', 0)
                    ob_strength = bullish_ob.get('strength', 'medium')
                    
                    # Fiyat bullish OB'nin altında ve test ediyor mu?
                    if ob_low < entry_price and ob_low <= current_price <= ob_high:
                        # Güçlü bullish OB testi -> kesin iptal
                        if ob_strength in ['strong', 'high', 'very_strong']:
                            return f"Short sinyal iptal: Güçlü Bullish OB testi ({ob_strength}) @ {ob_low:.4f}"
                        # Eski sinyaller için orta kuvvet de yeterli
                        elif is_old_signal and ob_strength in ['medium']:
                            return f"Eski short sinyal iptal: Orta Bullish OB testi @ {ob_low:.4f}"
            
            # FVG POI Testi de ekleyelim
            fvg_analysis = all_symbol_data.get('fvg_analysis', [])
            if isinstance(fvg_analysis, list):
                for fvg in fvg_analysis:
                    if not isinstance(fvg, dict):
                        continue
                        
                    fvg_type = fvg.get('type', '').lower()
                    fvg_top = fvg.get('top', 0)
                    fvg_bottom = fvg.get('bottom', 0)
                    fvg_mitigated = fvg.get('mitigated', False)
                    
                    # Sadece aktif (mitigated olmayan) FVG'leri kontrol et
                    if fvg_mitigated:
                        continue
                    
                    # Long pozisyon için Bearish FVG testi
                    if (trade_direction in ['bullish', 'bull', 'long'] and 
                        'bearish' in fvg_type and 
                        fvg_top > entry_price and 
                        fvg_bottom <= current_price <= fvg_top):
                        return f"Long sinyal iptal: Bearish FVG POI testi @ {fvg_top:.4f}-{fvg_bottom:.4f}"
                    
                    # Short pozisyon için Bullish FVG testi
                    if (trade_direction in ['bearish', 'bear', 'short'] and 
                        'bullish' in fvg_type and 
                        fvg_bottom < entry_price and 
                        fvg_bottom <= current_price <= fvg_top):
                        return f"Short sinyal iptal: Bullish FVG POI testi @ {fvg_top:.4f}-{fvg_bottom:.4f}"
            
            return None
            
        except Exception as e:
            logger.error(f"Karşıt POI test kontrolü hatası: {e}")
            return None

    def _check_liquidity_target_change(self, pending_trade: Dict[str, Any], 
                                      all_symbol_data: Dict[str, Any], 
                                      is_old_signal: bool) -> Optional[str]:
        """
        Likidite hedefi değişimi kontrolü.
        Weak/Strong swing analizi sonucunda ana likidite hedefi alınırsa,
        momentumun tükendiği anlamına gelir.
        """
        try:
            trade_direction = pending_trade.get('direction', '').lower()
            symbol = pending_trade.get('symbol', 'N/A')
            
            # Weak/Strong swings analizi al
            ws_analysis = all_symbol_data.get('weak_strong_swings', {})
            if not isinstance(ws_analysis, dict):
                return None
            
            labeled_swings = ws_analysis.get('labeled_swings', [])
            liquidity_targets = ws_analysis.get('liquidity_targets', [])
            
            # Son swing'leri kontrol et
            if labeled_swings:
                recent_swings = labeled_swings[-3:]  # Son 3 swing
                
                for swing in recent_swings:
                    swing_type = swing.get('strength_type', '').lower()
                    swing_direction = swing.get('direction', '').lower()
                    swing_price = swing.get('price', 0)
                    
                    # Long pozisyon için Weak High alınması
                    if (trade_direction in ['bullish', 'bull', 'long'] and 
                        'weak' in swing_type and 
                        'high' in swing_direction):
                        # Weak high'ın alınması bullish momentum tükenmesi
                        return f"Long sinyal iptal: Weak High likidite hedefi alındı @ {swing_price:.4f}"
                    
                    # Short pozisyon için Weak Low alınması
                    if (trade_direction in ['bearish', 'bear', 'short'] and 
                        'weak' in swing_type and 
                        'low' in swing_direction):
                        # Weak low'un alınması bearish momentum tükenmesi
                        return f"Short sinyal iptal: Weak Low likidite hedefi alındı @ {swing_price:.4f}"
                    
                    # Eski sinyaller için Strong swing'lerin karşıt yönde alınması da iptal sebebi
                    if is_old_signal:
                        if (trade_direction in ['bullish', 'bull', 'long'] and 
                            'strong' in swing_type and 
                            'low' in swing_direction):
                            return f"Eski long sinyal iptal: Strong Low kırıldı @ {swing_price:.4f}"
                        
                        if (trade_direction in ['bearish', 'bear', 'short'] and 
                            'strong' in swing_type and 
                            'high' in swing_direction):
                            return f"Eski short sinyal iptal: Strong High kırıldı @ {swing_price:.4f}"
            
            return None
            
        except Exception as e:
            logger.error(f"Likidite hedefi değişimi kontrolü hatası: {e}")
            return None

    def _check_fvg_fill_invalidation(self, pending_trade: Dict[str, Any], 
                                    all_symbol_data: Dict[str, Any], 
                                    current_price: float) -> Optional[str]:
        """
        FVG fill kontrolü (gelişmiş).
        Sinyale bağlı olan FVG fill olursa sinyal geçersiz.
        """
        try:
            trade_direction = pending_trade.get('direction', '').lower()
            symbol = pending_trade.get('symbol', 'N/A')
            pattern_name = pending_trade.get('pattern_name', '').upper()
            
            # Sadece FVG tabanlı sinyaller için kontrol et
            if 'FVG' not in pattern_name:
                return None
            
            fvg_analysis = all_symbol_data.get('fvg_analysis', [])
            if not isinstance(fvg_analysis, list):
                return None
            
            # Son FVG'leri kontrol et
            for fvg in fvg_analysis[-5:]:  # Son 5 FVG
                if not isinstance(fvg, dict):
                    continue
                    
                fvg_type = fvg.get('type', '').lower()
                fvg_top = fvg.get('top', 0)
                fvg_bottom = fvg.get('bottom', 0)
                fvg_mitigated = fvg.get('mitigated', False)
                
                # Sinyal yönüne uygun FVG'yi kontrol et
                if ((trade_direction in ['bullish', 'bull', 'long'] and 'bullish' in fvg_type) or
                    (trade_direction in ['bearish', 'bear', 'short'] and 'bearish' in fvg_type)):
                    
                    # FVG fill kontrolü
                    if not fvg_mitigated:
                        # Bullish FVG için: fiyat FVG'nin altına düştü mü?
                        if ('bullish' in fvg_type and current_price < fvg_bottom):
                            return f"FVG sinyal iptal: Bullish FVG fill oldu @ {fvg_bottom:.4f} (Fiyat: {current_price:.4f})"
                        
                        # Bearish FVG için: fiyat FVG'nin üstüne çıktı mı?
                        if ('bearish' in fvg_type and current_price > fvg_top):
                            return f"FVG sinyal iptal: Bearish FVG fill oldu @ {fvg_top:.4f} (Fiyat: {current_price:.4f})"
            
            return None
            
        except Exception as e:
            logger.error(f"FVG fill invalidation kontrolü hatası: {e}")
            return None

    def _check_po3_phase_invalidation(self, pending_trade: Dict[str, Any], 
                                     all_symbol_data: Dict[str, Any], 
                                     is_old_signal: bool) -> Optional[str]:
        """
        Power of Three (Po3) faz değişimi kontrolü.
        Po3 fazı değişirse bu momentumda değişiklik anlamına gelir.
        """
        try:
            trade_direction = pending_trade.get('direction', '').lower()
            symbol = pending_trade.get('symbol', 'N/A')
            
            # Po3 analizi al
            po3_analysis = all_symbol_data.get('po3_analysis', {})
            if not isinstance(po3_analysis, dict):
                return None
            
            current_phase = po3_analysis.get('current_phase', '').lower()
            phase_confidence = po3_analysis.get('confidence', 0.0)
            
            # Yüksek güvenli faz değişiklikleri için kontrol
            if phase_confidence >= 0.7:  # %70+ güven
                
                # Long pozisyon için Distribution fazına geçiş
                if (trade_direction in ['bullish', 'bull', 'long'] and 
                    current_phase in ['distribution', 'manipulation']):
                    return f"Long sinyal iptal: Po3 {current_phase.title()} fazına geçiş (Güven: {phase_confidence:.1%})"
                
                # Short pozisyon için Accumulation fazına geçiş
                if (trade_direction in ['bearish', 'bear', 'short'] and 
                    current_phase in ['accumulation', 'manipulation']):
                    return f"Short sinyal iptal: Po3 {current_phase.title()} fazına geçiş (Güven: {phase_confidence:.1%})"
                
                # Eski sinyaller için manipulation fazı da iptal sebebi
                if is_old_signal and current_phase == 'manipulation':
                    return f"Eski sinyal iptal: Po3 Manipulation fazı (Market belirsiz)"
            
            return None
            
        except Exception as e:
            logger.error(f"Po3 faz invalidation kontrolü hatası: {e}")
            return None

    def _check_volume_imbalance_invalidation(self, pending_trade: Dict[str, Any], 
                                           all_symbol_data: Dict[str, Any], 
                                           current_price: float) -> Optional[str]:
        """
        Volume Imbalance karşıt yönlü test kontrolü.
        Karşıt yönde güçlü hacim dengesizliği oluşursa sinyal geçersiz.
        """
        try:
            trade_direction = pending_trade.get('direction', '').lower()
            symbol = pending_trade.get('symbol', 'N/A')
            
            # Volume imbalance analizi al
            vi_analysis = all_symbol_data.get('volume_imbalance', {})
            if not isinstance(vi_analysis, dict):
                return None
            
            active_imbalances = vi_analysis.get('active_imbalances', [])
            
            for vi in active_imbalances:
                if not isinstance(vi, dict):
                    continue
                    
                vi_type = vi.get('type', '').lower()
                vi_price = vi.get('price', 0)
                vi_quality = vi.get('quality_score', 0)
                vi_volume_ratio = vi.get('volume_ratio', 1.0)
                
                # Yüksek kalite VI'ler için kontrol (≥7)
                if vi_quality >= 7:
                    
                    # Long pozisyon için Bearish VI test
                    if (trade_direction in ['bullish', 'bull', 'long'] and 
                        'bearish' in vi_type and 
                        abs(current_price - vi_price) / current_price <= 0.005):  # %0.5 yakınlık
                        
                        return f"Long sinyal iptal: Güçlü Bearish Volume Imbalance test @ {vi_price:.4f} (Kalite: {vi_quality}/10)"
                    
                    # Short pozisyon için Bullish VI test
                    if (trade_direction in ['bearish', 'bear', 'short'] and 
                        'bullish' in vi_type and 
                        abs(current_price - vi_price) / current_price <= 0.005):  # %0.5 yakınlık
                        
                        return f"Short sinyal iptal: Güçlü Bullish Volume Imbalance test @ {vi_price:.4f} (Kalite: {vi_quality}/10)"
            
            return None
            
        except Exception as e:
            logger.error(f"Volume imbalance invalidation kontrolü hatası: {e}")
            return None

    def _check_htf_poi_ltf_mss_invalidation(self, pending_trade: Dict[str, Any], 
                                           all_symbol_data: Dict[str, Any], 
                                           is_old_signal: bool) -> Optional[str]:
        """
        HTF POI + LTF MSS sinyallerinin karşıt yönlü testi.
        Karşıt yönde güçlü HTF POI + LTF MSS sinyali oluşursa mevcut sinyal geçersiz.
        """
        try:
            trade_direction = pending_trade.get('direction', '').lower()
            symbol = pending_trade.get('symbol', 'N/A')
            
            # HTF POI + LTF MSS analizi al
            htf_poi_analysis = all_symbol_data.get('htf_poi_ltf_mss', {})
            if not isinstance(htf_poi_analysis, dict):
                return None
            
            htf_poi_signals = htf_poi_analysis.get('signals', [])
            
            for signal in htf_poi_signals:
                if not isinstance(signal, dict):
                    continue
                    
                signal_direction = signal.get('direction', '').lower()
                confluence_score = signal.get('confluence_score', 0)
                signal_type = signal.get('signal_type', 'UNKNOWN')
                
                # Yüksek kalite karşıt yönlü sinyal (≥75)
                if confluence_score >= 75:
                    
                    # Long pozisyona karşı Bearish HTF POI + LTF MSS
                    if (trade_direction in ['bullish', 'bull', 'long'] and 
                        signal_direction in ['bearish', 'bear', 'short']):
                        return f"Long sinyal iptal: Güçlü Bearish HTF POI + LTF MSS ({signal_type}) - Confluence: {confluence_score}/100"
                    
                    # Short pozisyona karşı Bullish HTF POI + LTF MSS
                    if (trade_direction in ['bearish', 'bear', 'short'] and 
                        signal_direction in ['bullish', 'bull', 'long']):
                        return f"Short sinyal iptal: Güçlü Bullish HTF POI + LTF MSS ({signal_type}) - Confluence: {confluence_score}/100"
                
                # Eski sinyaller için daha düşük eşik (≥60)
                elif is_old_signal and confluence_score >= 60:
                    
                    if (trade_direction in ['bullish', 'bull', 'long'] and 
                        signal_direction in ['bearish', 'bear', 'short']):
                        return f"Eski long sinyal iptal: Bearish HTF POI + LTF MSS ({signal_type}) - Confluence: {confluence_score}/100"
                    
                    if (trade_direction in ['bearish', 'bear', 'short'] and 
                        signal_direction in ['bullish', 'bull', 'long']):
                        return f"Eski short sinyal iptal: Bullish HTF POI + LTF MSS ({signal_type}) - Confluence: {confluence_score}/100"
            
            return None
            
        except Exception as e:
            logger.error(f"HTF POI + LTF MSS invalidation kontrolü hatası: {e}")
            return None
