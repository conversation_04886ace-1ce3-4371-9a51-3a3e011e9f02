# killzone_analyzer.py

from datetime import datetime
import pytz
from loguru import logger

class KillzoneAnalyzer:
    """
    Mevcut zamanın hangi ICT Killzone'u içinde olduğunu belirler.
    Tüm saatler UTC'ye dönüştürülerek hesaplanır.
    
    ICT Killzone'ları (YAZ SAATİ / DST):
    - London Killzone: 06:00 - 09:00 UTC (London açılış öncesi)
    - New York AM Killzone: 11:30 - 12:30 UTC (NY açılış öncesi)
    - New York Lunch Killzone: 15:30 - 16:30 UTC (NY öğle arası)
    - London Close Killzone: 15:00 - 17:00 UTC (London kapanış)
    
    Bu zaman dilimleri, kurumsal likidite ve manipülasyon için en aktif saatlerdir.
    """

    def __init__(self):
        """
        KillzoneAnalyzer'ı başlatır ve ICT killzone tanımlarını yükler.
        YAZ SAATİ (DST) modunda çalışır.
        """
        # ICT Killzone tanımları (UTC saatleri - YAZ SAATİ)
        self.killzones = {
            'London': {
                'start': 6, 'end': 9, 
                'description': 'London Killzone - Avrupa Açılış Likidite Avı',
                'importance': 'very_high'
            },
            'NY_AM': {
                'start': 11.5, 'end': 12.5, 
                'description': 'New York AM Killzone - Amerika Açılış Likidite Avı',
                'importance': 'very_high'
            },
            'NY_Lunch': {
                'start': 15.5, 'end': 16.5, 
                'description': 'New York Lunch Killzone - Amerika Öğle Manipulation',
                'importance': 'high'
            },
            'London_Close': {
                'start': 15, 'end': 17, 
                'description': 'London Close Killzone - Avrupa Kapanış Manipulation',
                'importance': 'high'
            }
        }
        
        logger.info("ICT Killzone Analiz Modülü başlatıldı - YAZ SAATİ (DST) modunda.")
    


    def get_current_killzone(self, custom_time: datetime = None) -> dict:
        """
        Mevcut UTC zamanına göre aktif olan Killzone'u döndürür.
        
        Args:
            custom_time (datetime, optional): Belirli bir zaman için kontrol etmek.
                                            Verilmezse mevcut zaman kullanılır.

        Returns:
            dict: Killzone bilgilerini içeren sözlük:
            {
                'zone': str,           # "London", "NY_AM", "NY_Lunch", "London_Close", veya "None"
                'is_active': bool,     # Killzone aktif mi?
                'description': str,    # Killzone açıklaması
                'importance': str,     # Önem seviyesi
                'utc_time': str,       # Mevcut UTC zamanı
                'local_hour': float    # UTC saati (ondalık)
            }
        """
        # Mevcut UTC zamanını al
        if custom_time:
            now_utc = custom_time.astimezone(pytz.utc) if custom_time.tzinfo else pytz.utc.localize(custom_time)
        else:
            now_utc = datetime.now(pytz.utc)
            
        current_hour = now_utc.hour + now_utc.minute / 60.0  # Ondalık saat formatı

        # Aktif killzone'u belirle
        active_zone = "None"
        zone_description = "Killzone dışı - düşük volatilite beklenen"
        zone_importance = "low"
        is_active = False

        for zone_name, zone_info in self.killzones.items():
            start_hour = zone_info['start']
            end_hour = zone_info['end']
            
            if start_hour <= current_hour < end_hour:
                active_zone = zone_name
                zone_description = zone_info['description']
                zone_importance = zone_info['importance']
                is_active = True
                break

        result = {
            'zone': active_zone,
            'is_active': is_active,
            'description': zone_description,
            'importance': zone_importance,
            'utc_time': now_utc.strftime('%H:%M:%S UTC'),
            'local_hour': current_hour
        }

        if is_active:
            importance_emoji = "🔥" if zone_importance == "very_high" else "⚡"
            logger.info(f"{importance_emoji} ICT Killzone AKTIF: {active_zone} ({zone_description}) - UTC: {result['utc_time']}")
        else:
            logger.debug(f"⏰ ICT Killzone: {active_zone} - UTC: {result['utc_time']}")

        return result

    def get_next_killzone(self, custom_time: datetime = None) -> dict:
        """
        Bir sonraki aktif olacak killzone'u ve ne kadar süre sonra başlayacağını döndürür.
        
        Args:
            custom_time (datetime, optional): Belirli bir zaman referansı
            
        Returns:
            dict: Sonraki killzone bilgileri
        """
        if custom_time:
            now_utc = custom_time.astimezone(pytz.utc) if custom_time.tzinfo else pytz.utc.localize(custom_time)
        else:
            now_utc = datetime.now(pytz.utc)
            
        current_hour = now_utc.hour + now_utc.minute / 60.0
        
        # Killzone'ları saatlerine göre sırala
        sorted_zones = sorted(self.killzones.items(), key=lambda x: x[1]['start'])
        
        # Sonraki killzone'u bul
        for zone_name, zone_info in sorted_zones:
            start_hour = zone_info['start']
            
            if start_hour > current_hour:
                hours_until = start_hour - current_hour
                start_time_str = f"{int(start_hour):02d}:{int((start_hour % 1) * 60):02d}"
                next_zone = {
                    'zone': zone_name,
                    'description': zone_info['description'],
                    'importance': zone_info['importance'],
                    'hours_until': hours_until,
                    'start_time_utc': f"{start_time_str} UTC"
                }
                
                logger.debug(f"Sonraki ICT Killzone: {zone_name} ({hours_until:.1f} saat sonra)")
                return next_zone
        
        # Eğer hiçbir killzone kalmadıysa, ertesi günün ilkini döndür
        first_zone = sorted_zones[0]
        zone_name, zone_info = first_zone
        hours_until = (24 - current_hour) + zone_info['start']
        start_time_str = f"{int(zone_info['start']):02d}:{int((zone_info['start'] % 1) * 60):02d}"
        
        next_zone = {
            'zone': zone_name,
            'description': zone_info['description'],
            'importance': zone_info['importance'],
            'hours_until': hours_until,
            'start_time_utc': f"{start_time_str} UTC (ertesi gün)"
        }
        
        logger.debug(f"Sonraki ICT Killzone: {zone_name} ({hours_until:.1f} saat sonra)")
        return next_zone

    def is_high_impact_time(self, custom_time: datetime = None) -> bool:
        """
        Mevcut zamanın yüksek etkili trading saatleri içinde olup olmadığını kontrol eder.
        
        Args:
            custom_time (datetime, optional): Kontrol edilecek zaman
            
        Returns:
            bool: Yüksek etkili zaman dilimi içindeyse True
        """
        current_killzone = self.get_current_killzone(custom_time)
        
        # Very High önem seviyesindeki killzone'lar yüksek impact kabul edilir
        if current_killzone['is_active'] and current_killzone['importance'] == 'very_high':
            return True
        
        return False

    def get_all_killzones_status(self, custom_time: datetime = None) -> dict:
        """
        Tüm killzone'ların durumunu ve hangi saatlerde aktif olduklarını döndürür.
        
        Args:
            custom_time (datetime, optional): Referans zaman
            
        Returns:
            dict: Tüm killzone durumları
        """
        current_info = self.get_current_killzone(custom_time)
        next_info = self.get_next_killzone(custom_time)
        
        status = {
            'current': current_info,
            'next': next_info,
            'all_zones': {}
        }
        
        # Tüm zone'ların detayları
        for zone_name, zone_info in self.killzones.items():
            start_str = f"{int(zone_info['start']):02d}:{int((zone_info['start'] % 1) * 60):02d}"
            end_str = f"{int(zone_info['end']):02d}:{int((zone_info['end'] % 1) * 60):02d}"
            duration = zone_info['end'] - zone_info['start']
            
            status['all_zones'][zone_name] = {
                'start_utc': start_str,
                'end_utc': end_str,
                'description': zone_info['description'],
                'importance': zone_info['importance'],
                'duration_hours': duration
            }
        
        return status

# Test fonksiyonu
if __name__ == '__main__':
    analyzer = KillzoneAnalyzer()
    
    # Mevcut killzone'u test et
    current = analyzer.get_current_killzone()
    print(f"Mevcut Killzone: {current}")
    
    # Sonraki killzone'u test et
    next_zone = analyzer.get_next_killzone()
    print(f"Sonraki Killzone: {next_zone}")
    
    # Yüksek etkili zaman kontrolü
    is_high_impact = analyzer.is_high_impact_time()
    print(f"Yüksek Etkili Zaman: {is_high_impact}")
    
    # Tüm killzone durumları
    all_status = analyzer.get_all_killzones_status()
    print(f"Tüm Killzone Durumları: {all_status}")
