# killzone_session_manipulation_analyzer.py

import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import pytz
from typing import Dict, List, Any, Optional, Tuple
from loguru import logger
from market_structure_analyzer import MarketStructureAnalyzer
from fvg_analyzer import FvgAnalyzer
from order_block_analyzer import OrderBlockAnalyzer

class KillzoneSessionManipulationAnalyzer:
    """
    Killzone + Seans Açılışı Manipülasyonu Analyzer
    
    ICT Konsepti:
    - Londra ve New York seansları açılışlarında önceki seans likiditesinin temizlenmesi
    - Stop Hunt sonrası güçlü trend yönlü hareket
    - Zamanlama + Likidite + Yapı değişimi kombinasyonu
    
    Analiz Adımları:
    1. Aktif killzone tespit et (Londra/New York açılış)
    2. Önceki seans H/L seviyelerini belirle
    3. Manipülasyon (sweep) izle
    4. MSS/CHoCH + FVG/OB teyidi bekle
    """
    
    def __init__(self, market_structure_analyzer: MarketStructureAnalyzer, 
                 fvg_analyzer: FvgAnalyzer, 
                 order_block_analyzer: OrderBlockAnalyzer):
        """
        Killzone Session Manipulation Analyzer'ı başlat.
        
        Args:
            market_structure_analyzer: Market structure analiz için gerekli analizör
            fvg_analyzer: FVG analiz için gerekli analizör  
            order_block_analyzer: Order block analiz için gerekli analizör
        """
        self.market_structure_analyzer = market_structure_analyzer
        self.fvg_analyzer = fvg_analyzer
        self.order_block_analyzer = order_block_analyzer
        # Killzone tanımları (UTC saatleri - YAZ SAATİ DST)
        self.killzones = {
            'london_open': {
                'start_utc': '06:00',  # Yaz saati: 06:00 UTC
                'end_utc': '09:00',    # Yaz saati: 09:00 UTC
                'name': 'London Open Killzone',
                'manipulation_window': 120,  # İlk 2 saat
                'previous_session': 'asia'
            },
            'new_york_open': {
                'start_utc': '12:00',  # Yaz saati: 12:00 UTC
                'end_utc': '15:00',    # Yaz saati: 15:00 UTC
                'name': 'New York Open Killzone',
                'manipulation_window': 120,  # İlk 2 saat
                'previous_session': 'london'
            },
            'london_close': {
                'start_utc': '15:00',  # Yaz saati: 15:00 UTC
                'end_utc': '17:00',    # Yaz saati: 17:00 UTC
                'name': 'London Close Killzone',
                'manipulation_window': 90,  # 1.5 saat
                'previous_session': 'london_main'
            }
        }
        
        # Seans tanımları (UTC - YAZ SAATİ)
        self.sessions = {
            'asia': {'start': '00:00', 'end': '09:00'},
            'london': {'start': '07:00', 'end': '16:00'},       # Yaz saati
            'london_main': {'start': '08:00', 'end': '15:00'},  # Yaz saati
            'new_york': {'start': '12:00', 'end': '21:00'}      # Yaz saati
        }
        
        self.manipulation_tolerance_pct = 0.15  # %0.15 sweep tolerance
        self.min_manipulation_pips = 3.0  # Minimum 3 pip sweep
        self.max_sweep_age_minutes = 30  # Sweep sonrası max 30 dakika
        
        logger.info("Killzone Session Manipulation Analyzer başlatıldı - YAZ SAATİ (DST) modunda.")
    
    def analyze(self, candles: pd.DataFrame, symbol: str, 
                liquidity_data: Dict[str, Any] = None,
                market_structure_data: Dict[str, Any] = None) -> Dict[str, Any]:
        """
        Ana analiz fonksiyonu: Killzone + seans manipülasyonu sinyallerini tespit eder.
        
        Args:
            candles: 5m veya 15m timeframe mum verileri
            symbol: Analiz edilen sembol
            liquidity_data: Likidite analiz sonuçları
            market_structure_data: Market structure analiz sonuçları
            
        Returns:
            Dict: Analiz sonuçları ve sinyaller
        """
        try:
            logger.info(f"[{symbol}] Killzone Session Manipulation analizi başlıyor...")
            
            if candles is None or len(candles) < 50:
                logger.warning(f"[{symbol}] Yetersiz candle verisi: {len(candles) if candles is not None else 0}")
                return self._empty_result()
            
            # Zaman verilerini düzenle
            candles = self._prepare_time_data(candles)
            
            # Aktif killzone tespit et
            current_killzone = self._detect_active_killzone(candles)
            if not current_killzone:
                logger.debug(f"[{symbol}] Aktif killzone bulunamadı")
                return self._empty_result()
            
            logger.info(f"[{symbol}] Aktif killzone: {current_killzone['name']}")
            
            # Önceki seans seviyelerini hesapla
            previous_session_levels = self._calculate_previous_session_levels(
                candles, current_killzone
            )
            
            if not previous_session_levels:
                logger.debug(f"[{symbol}] Önceki seans seviyeleri hesaplanamadı")
                return self._empty_result()
            
            # Manipülasyon (sweep) analizi
            manipulation_analysis = self._analyze_session_manipulation(
                candles, current_killzone, previous_session_levels
            )
            
            # Market structure teyidi
            structure_confirmation = self._analyze_structure_confirmation(
                candles, manipulation_analysis, market_structure_data
            )
            
            # Sinyalleri oluştur
            signals = self._generate_killzone_signals(
                current_killzone, manipulation_analysis, structure_confirmation,
                previous_session_levels, candles
            )
            
            result = {
                'active_killzone': current_killzone,
                'previous_session_levels': previous_session_levels,
                'manipulation_analysis': manipulation_analysis,
                'structure_confirmation': structure_confirmation,
                'signals': signals,
                'analysis_timestamp': pd.Timestamp.now().isoformat(),
                'total_signals': len(signals)
            }
            
            if signals:
                logger.success(f"[{symbol}] Killzone Session Manipulation: {len(signals)} sinyal bulundu")
            else:
                logger.info(f"[{symbol}] Killzone analizi tamamlandı, sinyal bulunamadı")
            
            return result
            
        except Exception as e:
            logger.error(f"[{symbol}] Killzone Session Manipulation analiz hatası: {e}", exc_info=True)
            return self._empty_result()
    
    def _prepare_time_data(self, candles: pd.DataFrame) -> pd.DataFrame:
        """
        Zaman verilerini UTC'ye çevirir ve saat bilgilerini ekler.
        """
        df = candles.copy()
        
        # Timestamp column'ını datetime'a çevir
        if 'timestamp' in df.columns:
            df['datetime'] = pd.to_datetime(df['timestamp'])
        elif df.index.dtype == 'datetime64[ns]':
            df['datetime'] = df.index
        else:
            df['datetime'] = pd.to_datetime(df.index)
        
        # UTC'ye çevir
        if df['datetime'].dt.tz is None:
            df['datetime'] = df['datetime'].dt.tz_localize('UTC')
        else:
            df['datetime'] = df['datetime'].dt.tz_convert('UTC')
        
        # Saat ve dakika bilgilerini ekle
        df['hour'] = df['datetime'].dt.hour
        df['minute'] = df['datetime'].dt.minute
        df['time_str'] = df['datetime'].dt.strftime('%H:%M')
        df['weekday'] = df['datetime'].dt.weekday  # 0=Monday, 6=Sunday
        
        return df
    
    def _detect_active_killzone(self, candles: pd.DataFrame) -> Optional[Dict[str, Any]]:
        """
        Şu anda aktif olan killzone'u tespit eder.
        """
        if len(candles) == 0:
            return None
        
        # Son mum zamanı
        last_candle_time = candles.iloc[-1]['datetime']
        current_time_str = last_candle_time.strftime('%H:%M')
        current_weekday = last_candle_time.weekday()
        
        # Hafta sonu kontrolü
        if current_weekday >= 5:  # Saturday=5, Sunday=6
            return None
        
        # Her killzone için kontrol et
        for kz_name, kz_config in self.killzones.items():
            if self._is_time_in_range(current_time_str, kz_config['start_utc'], kz_config['end_utc']):
                # Killzone başlangıcından ne kadar süre geçti?
                start_time = datetime.strptime(kz_config['start_utc'], '%H:%M').time()
                current_time = last_candle_time.time()
                
                # Aynı günde olduğundan emin ol
                start_datetime = last_candle_time.replace(
                    hour=start_time.hour, 
                    minute=start_time.minute, 
                    second=0, 
                    microsecond=0
                )
                
                time_since_start = (last_candle_time - start_datetime).total_seconds() / 60
                
                return {
                    **kz_config,
                    'killzone_name': kz_name,
                    'start_time': start_datetime,
                    'minutes_since_start': time_since_start,
                    'is_manipulation_window': time_since_start <= kz_config['manipulation_window']
                }
        
        return None
    
    def _is_time_in_range(self, current_time: str, start_time: str, end_time: str) -> bool:
        """
        Belirtilen zaman aralığında olup olmadığını kontrol eder.
        """
        try:
            current = datetime.strptime(current_time, '%H:%M').time()
            start = datetime.strptime(start_time, '%H:%M').time()
            end = datetime.strptime(end_time, '%H:%M').time()
            
            if start <= end:
                return start <= current <= end
            else:  # Gece geçiş durumu (örn. 22:00 - 06:00)
                return current >= start or current <= end
        except:
            return False
    
    def _calculate_previous_session_levels(self, candles: pd.DataFrame, 
                                          current_killzone: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """
        Önceki seansın yüksek/düşük seviyelerini hesaplar.
        """
        previous_session_name = current_killzone['previous_session']
        previous_session_config = self.sessions.get(previous_session_name)
        
        if not previous_session_config:
            return None
        
        # Önceki seans için zaman aralığını hesapla
        killzone_start = current_killzone['start_time']
        
        # Önceki günün aynı seansını veya aynı günün önceki seansını bul
        if previous_session_name == 'asia':
            # Asya seansı önceki gün veya aynı gün (eğer Londra açılışından önceyse)
            session_date = killzone_start.date()
            if killzone_start.hour < 9:  # Aynı gün Asya seansı
                session_start = killzone_start.replace(hour=0, minute=0, second=0, microsecond=0)
                session_end = killzone_start.replace(hour=9, minute=0, second=0, microsecond=0)
            else:  # Önceki gün Asya seansı
                prev_date = session_date - timedelta(days=1)
                session_start = killzone_start.replace(
                    year=prev_date.year, month=prev_date.month, day=prev_date.day,
                    hour=0, minute=0, second=0, microsecond=0
                )
                session_end = killzone_start.replace(
                    year=prev_date.year, month=prev_date.month, day=prev_date.day,
                    hour=9, minute=0, second=0, microsecond=0
                )
        else:
            # Londra/New York için aynı gün önceki seans
            start_time = datetime.strptime(previous_session_config['start'], '%H:%M').time()
            end_time = datetime.strptime(previous_session_config['end'], '%H:%M').time()
            
            session_start = killzone_start.replace(
                hour=start_time.hour, minute=start_time.minute, second=0, microsecond=0
            )
            session_end = killzone_start.replace(
                hour=end_time.hour, minute=end_time.minute, second=0, microsecond=0
            )
        
        # Önceki seans candlelerini filtrele
        session_candles = candles[
            (candles['datetime'] >= session_start) & 
            (candles['datetime'] < session_end)
        ].copy()
        
        if len(session_candles) < 3:
            logger.warning(f"Önceki seans ({previous_session_name}) için yetersiz veri")
            return None
        
        # Yüksek/Düşük seviyeleri
        session_high = session_candles['high'].max()
        session_low = session_candles['low'].min()
        session_range = session_high - session_low
        
        # Yüksek/düşük mumların zamanları
        high_time = session_candles[session_candles['high'] == session_high]['datetime'].iloc[0]
        low_time = session_candles[session_candles['low'] == session_low]['datetime'].iloc[0]
        
        return {
            'session_name': previous_session_name,
            'session_start': session_start.isoformat(),
            'session_end': session_end.isoformat(),
            'session_high': session_high,
            'session_low': session_low,
            'session_range': session_range,
            'high_time': high_time.isoformat(),
            'low_time': low_time.isoformat(),
            'total_candles': len(session_candles),
            'range_pct': (session_range / session_low) * 100 if session_low > 0 else 0
        }
    
    def _analyze_session_manipulation(self, candles: pd.DataFrame,
                                     current_killzone: Dict[str, Any],
                                     previous_levels: Dict[str, Any]) -> Dict[str, Any]:
        """
        Seans açılışı manipülasyonunu (sweep) analiz eder.
        """
        killzone_start = current_killzone['start_time']
        manipulation_window = current_killzone['manipulation_window']
        
        # Killzone başlangıcından itibaren manipulation window içindeki candleler
        window_end = killzone_start + timedelta(minutes=manipulation_window)
        
        manipulation_candles = candles[
            (candles['datetime'] >= killzone_start) &
            (candles['datetime'] <= window_end)
        ].copy()
        
        if len(manipulation_candles) < 3:
            return {'manipulation_detected': False, 'reason': 'Yetersiz manipulation window verisi'}
        
        session_high = previous_levels['session_high']
        session_low = previous_levels['session_low']
        
        # Sweep analizi
        sweeps = []
        
        # High sweep kontrolü
        high_sweep = self._detect_level_sweep(
            manipulation_candles, session_high, 'high', 'BSL_SWEEP'
        )
        if high_sweep:
            sweeps.append(high_sweep)
        
        # Low sweep kontrolü
        low_sweep = self._detect_level_sweep(
            manipulation_candles, session_low, 'low', 'SSL_SWEEP'
        )
        if low_sweep:
            sweeps.append(low_sweep)
        
        # En güçlü sweep'i seç
        best_sweep = None
        if sweeps:
            best_sweep = max(sweeps, key=lambda x: x['strength'])
        
        manipulation_detected = best_sweep is not None
        
        result = {
            'manipulation_detected': manipulation_detected,
            'killzone_window_minutes': manipulation_window,
            'analyzed_candles': len(manipulation_candles),
            'detected_sweeps': sweeps,
            'best_sweep': best_sweep,
            'manipulation_strength': best_sweep['strength'] if best_sweep else 0,
            'manipulation_type': best_sweep['sweep_type'] if best_sweep else None
        }
        
        if manipulation_detected:
            logger.info(f"Manipülasyon tespit edildi: {best_sweep['sweep_type']} "
                       f"(Güç: {best_sweep['strength']}/10)")
        
        return result
    
    def _detect_level_sweep(self, candles: pd.DataFrame, target_level: float,
                           level_type: str, sweep_type: str) -> Optional[Dict[str, Any]]:
        """
        Belirli bir seviyenin sweep edilip edilmediğini kontrol eder.
        """
        if len(candles) == 0:
            return None
        
        sweep_tolerance = target_level * (self.manipulation_tolerance_pct / 100)
        
        # Sweep için gerekli seviye
        if level_type == 'high':
            sweep_level = target_level + sweep_tolerance
            sweep_condition = candles['high'] >= sweep_level
        else:  # low
            sweep_level = target_level - sweep_tolerance
            sweep_condition = candles['low'] <= sweep_level
        
        sweep_candles = candles[sweep_condition]
        
        if len(sweep_candles) == 0:
            return None
        
        # İlk sweep mumu
        first_sweep_candle = sweep_candles.iloc[0]
        sweep_time = first_sweep_candle['datetime']
        
        # Sweep gücünü hesapla
        if level_type == 'high':
            max_penetration = sweep_candles['high'].max()
            penetration_distance = max_penetration - target_level
        else:
            min_penetration = sweep_candles['low'].min()
            penetration_distance = target_level - min_penetration
        
        penetration_pips = penetration_distance * 10000  # USD pairs için
        
        # Gücü 0-10 arası skala et
        strength = min(10, max(1, penetration_pips / 2))
        
        # Sweep sonrası reversal kontrol et
        reversal_analysis = self._analyze_post_sweep_reversal(
            candles, sweep_time, level_type, target_level
        )
        
        return {
            'sweep_type': sweep_type,
            'level_type': level_type,
            'target_level': target_level,
            'sweep_level': sweep_level,
            'sweep_time': sweep_time.isoformat(),
            'penetration_distance': penetration_distance,
            'penetration_pips': penetration_pips,
            'strength': strength,
            'reversal_analysis': reversal_analysis,
            'sweep_candle_count': len(sweep_candles)
        }
    
    def _analyze_post_sweep_reversal(self, candles: pd.DataFrame, sweep_time: pd.Timestamp,
                                    level_type: str, target_level: float) -> Dict[str, Any]:
        """
        Sweep sonrası reversal kalitesini analiz eder.
        """
        # Sweep sonrası 30 dakikalık window
        reversal_window_end = sweep_time + timedelta(minutes=self.max_sweep_age_minutes)
        
        post_sweep_candles = candles[
            (candles['datetime'] > sweep_time) &
            (candles['datetime'] <= reversal_window_end)
        ].copy()
        
        if len(post_sweep_candles) < 2:
            return {'reversal_detected': False, 'reason': 'Yetersiz post-sweep veri'}
        
        # Reversal için minimum hareket mesafesi
        min_reversal_distance = target_level * 0.001  # %0.1
        
        if level_type == 'high':
            # High sweep sonrası aşağı hareket bekliyoruz
            post_sweep_low = post_sweep_candles['low'].min()
            reversal_distance = target_level - post_sweep_low
            reversal_detected = reversal_distance >= min_reversal_distance
        else:
            # Low sweep sonrası yukarı hareket bekliyoruz
            post_sweep_high = post_sweep_candles['high'].max()
            reversal_distance = post_sweep_high - target_level
            reversal_detected = reversal_distance >= min_reversal_distance
        
        # Reversal gücü
        reversal_strength = 0
        if reversal_detected:
            reversal_pips = reversal_distance * 10000
            reversal_strength = min(10, max(1, reversal_pips / 5))
        
        return {
            'reversal_detected': reversal_detected,
            'reversal_distance': reversal_distance,
            'reversal_pips': reversal_distance * 10000,
            'reversal_strength': reversal_strength,
            'post_sweep_candles': len(post_sweep_candles)
        }
    
    def _analyze_structure_confirmation(self, candles: pd.DataFrame,
                                       manipulation_analysis: Dict[str, Any],
                                       market_structure_data: Dict[str, Any] = None) -> Dict[str, Any]:
        """
        Manipülasyon sonrası market structure confirmation analizi.
        """
        if not manipulation_analysis.get('manipulation_detected'):
            return {'confirmation_available': False, 'reason': 'Manipülasyon tespit edilmedi'}

        best_sweep = manipulation_analysis['best_sweep']
        if not best_sweep:
            return {'confirmation_available': False, 'reason': 'Geçerli sweep bulunamadı'}

        sweep_time = pd.to_datetime(best_sweep['sweep_time'])

        # Sweep sonrası confirmation window (15-30 dakika)
        confirmation_start = sweep_time
        confirmation_end = sweep_time + timedelta(minutes=30)

        confirmation_candles = candles[
            (candles['datetime'] >= confirmation_start) &
            (candles['datetime'] <= confirmation_end)
        ].copy()

        if len(confirmation_candles) < 5:
            return {'confirmation_available': False, 'reason': 'Yetersiz confirmation window verisi'}

        # MarketStructureAnalyzer kullanarak MSS tespiti
        mss_analysis_result = self.market_structure_analyzer.analyze(confirmation_candles)
        
        # MSS sonucunu işle
        mss_detected = False
        mss_type = None
        if mss_analysis_result and mss_analysis_result.get('breaks'):
            for b in mss_analysis_result['breaks']:
                if b['type'] == 'MSS':
                    mss_detected = True
                    mss_type = f"{b['direction'].upper()}_MSS"
                    break

        # FVG/Order Block arayışı
        poi_analysis = self._detect_poi_after_sweep(
            confirmation_candles, best_sweep['level_type']
        )

        # Confirmation strength hesapla
        confirmation_strength = 0
        confirmation_factors = []

        if mss_detected:
            confirmation_strength += 40
            confirmation_factors.append(f"MSS_{mss_type}")

        if poi_analysis['poi_found']:
            confirmation_strength += 35
            confirmation_factors.append(f"POI_{poi_analysis['poi_type']}")

        # Reversal strength ekleme
        reversal_strength = best_sweep['reversal_analysis']['reversal_strength']
        confirmation_strength += reversal_strength * 2.5  # max 25 puan

        return {
            'confirmation_available': confirmation_strength >= 40,
            'confirmation_strength': min(100, confirmation_strength),
            'confirmation_factors': confirmation_factors,
            'mss_analysis': {'mss_found': mss_detected, 'mss_type': mss_type},
            'poi_analysis': poi_analysis,
            'reversal_quality': reversal_strength,
            'total_confirmation_score': confirmation_strength
        }
    
    
    
    def _detect_poi_after_sweep(self, candles: pd.DataFrame, 
                               sweep_level_type: str) -> Dict[str, Any]:
        """
        Sweep sonrası POI (FVG/Order Block) tespit eder.
        """
        if len(candles) < 4:
            return {'poi_found': False, 'reason': 'Yetersiz veri'}

        # FvgAnalyzer ve OrderBlockAnalyzer kullanarak POI tespiti
        fvgs = self.fvg_analyzer.find_fvgs(candles)
        # OrderBlockAnalyzer.find_order_blocks bir sözlük döndürür, bu yüzden birleştiriyoruz
        order_blocks_dict = self.order_block_analyzer.find_order_blocks(candles, structure_breaks=[]) # Basit kullanım, gerekirse structure_breaks sağlanabilir
        order_blocks = order_blocks_dict.get('bullish', []) + order_blocks_dict.get('bearish', [])

        # Son FVG ve Order Block'u bul
        last_fvg = fvgs[-1] if fvgs else None
        last_ob = order_blocks[-1] if order_blocks else None

        poi_found = bool(last_fvg or last_ob)
        poi_type = None
        if last_fvg and last_ob:
            poi_type = "FVG_and_OB"
        elif last_fvg:
            poi_type = "FVG"
        elif last_ob:
            poi_type = "OB"

        return {
            'poi_found': poi_found,
            'poi_type': poi_type,
            'fvg_detected': bool(last_fvg),
            'ob_detected': bool(last_ob),
            'last_fvg': last_fvg,
            'last_ob': last_ob
        }
    
    def _generate_killzone_signals(self, current_killzone: Dict[str, Any],
                                  manipulation_analysis: Dict[str, Any],
                                  structure_confirmation: Dict[str, Any],
                                  previous_levels: Dict[str, Any],
                                  candles: pd.DataFrame) -> List[Dict[str, Any]]:
        """
        Killzone + Session Manipulation sinyallerini oluşturur.
        """
        signals = []
        
        if not manipulation_analysis.get('manipulation_detected'):
            return signals
        
        if not structure_confirmation.get('confirmation_available'):
            return signals
        
        best_sweep = manipulation_analysis['best_sweep']
        current_price = candles.iloc[-1]['close']
        
        # Sinyal yönünü belirle
        if best_sweep['level_type'] == 'high':
            # High sweep sonrası bearish sinyal
            signal_direction = 'BEARISH'
            entry_bias = 'SELL'
        else:
            # Low sweep sonrası bullish sinyal
            signal_direction = 'BULLISH'
            entry_bias = 'BUY'
        
        # Confluence skorunu hesapla
        confluence_score = self._calculate_killzone_confluence_score(
            current_killzone, manipulation_analysis, structure_confirmation
        )
        
        # Giriş ve çıkış seviyelerini hesapla
        entry_levels = self._calculate_killzone_entry_levels(
            best_sweep, structure_confirmation, current_price, signal_direction
        )
        
        if not entry_levels['primary_entry']:
            return signals
        
        # Stop loss ve take profit
        sl_tp_levels = self._calculate_killzone_sl_tp(
            entry_levels['primary_entry'], signal_direction, 
            best_sweep, previous_levels
        )
        
        # Ana sinyali oluştur
        main_signal = {
            'type': 'KILLZONE_SESSION_MANIPULATION',
            'direction': signal_direction,
            'entry_bias': entry_bias,
            'primary_entry': entry_levels['primary_entry'],
            'alternative_entries': entry_levels.get('alternative_entries', []),
            'stop_loss': sl_tp_levels['stop_loss'],
            'take_profits': sl_tp_levels['take_profits'],
            'confluence_score': confluence_score,
            'killzone_name': current_killzone['killzone_name'],
            'session_manipulation': {
                'swept_level': best_sweep['target_level'],
                'sweep_type': best_sweep['sweep_type'],
                'manipulation_strength': best_sweep['strength'],
                'reversal_strength': best_sweep['reversal_analysis']['reversal_strength']
            },
            'structure_confirmation': {
                'mss_type': structure_confirmation['mss_analysis'].get('mss_type'),
                'poi_type': structure_confirmation['poi_analysis'].get('poi_type'),
                'confirmation_strength': structure_confirmation['confirmation_strength']
            },
            'timing_info': {
                'killzone': current_killzone['name'],
                'minutes_since_killzone_start': current_killzone['minutes_since_start'],
                'previous_session': previous_levels['session_name']
            },
            'risk_reward_ratio': sl_tp_levels.get('risk_reward', 2.5),
            'signal_strength': 'HIGH' if confluence_score >= 75 else 'MEDIUM',
            'analysis_timestamp': pd.Timestamp.now().isoformat()
        }
        
        signals.append(main_signal)
        
        # İkincil sinyaller (eğer confluence çok yüksekse)
        if confluence_score >= 85:
            # Scalping sinyali ekle
            scalping_signal = self._create_scalping_signal(main_signal, entry_levels)
            if scalping_signal:
                signals.append(scalping_signal)
        
        return signals
    
    def _calculate_killzone_confluence_score(self, current_killzone: Dict[str, Any],
                                           manipulation_analysis: Dict[str, Any],
                                           structure_confirmation: Dict[str, Any]) -> float:
        """
        Killzone Session Manipulation confluence skorunu hesaplar (0-100).
        """
        score = 0.0
        
        # Killzone timing kalitesi (25 puan)
        if current_killzone['is_manipulation_window']:
            score += 25
        else:
            score += 15  # Killzone içinde ama manipulation window dışında
        
        # Manipulation strength (30 puan)
        manipulation_strength = manipulation_analysis['manipulation_strength']
        score += (manipulation_strength / 10) * 30
        
        # Structure confirmation (30 puan)
        confirmation_strength = structure_confirmation['confirmation_strength']
        score += (confirmation_strength / 100) * 30
        
        # Killzone türü bonusu (15 puan)
        killzone_name = current_killzone['killzone_name']
        if killzone_name in ['london_open', 'new_york_open']:
            score += 15  # Ana açılış killzone'ları
        elif killzone_name == 'london_close':
            score += 10  # Kapanış killzone'u
        else:
            score += 5   # Diğer killzone'lar
        
        return min(100.0, max(0.0, score))
    
    def _calculate_killzone_entry_levels(self, best_sweep: Dict[str, Any],
                                        structure_confirmation: Dict[str, Any],
                                        current_price: float, signal_direction: str) -> Dict[str, Any]:
        """
        Killzone stratejisi için optimum giriş seviyelerini hesaplar.
        """
        swept_level = best_sweep['target_level']
        
        # POI varsa onu kullan
        poi_analysis = structure_confirmation.get('poi_analysis', {})
        
        primary_entry = None
        alternative_entries = []
        
        if poi_analysis.get('poi_found'):
            # POI seviyesinden giriş (FVG/OB)
            if signal_direction == 'BULLISH':
                # Bullish için swept level üstünde bir seviye
                primary_entry = swept_level * 1.0008  # %0.08 üstünde
            else:
                # Bearish için swept level altında bir seviye
                primary_entry = swept_level * 0.9992  # %0.08 altında
        else:
            # POI yoksa current price yakınından giriş
            buffer_pct = 0.0005  # %0.05
            if signal_direction == 'BULLISH':
                primary_entry = current_price * (1 - buffer_pct)
            else:
                primary_entry = current_price * (1 + buffer_pct)
        
        # Alternatif seviyeler
        if primary_entry:
            # %0.1 üst ve alt alternatifler
            alt_buffer = 0.001
            alt1 = primary_entry * (1 + alt_buffer) if signal_direction == 'BEARISH' else primary_entry * (1 - alt_buffer)
            alt2 = primary_entry * (1 - alt_buffer) if signal_direction == 'BEARISH' else primary_entry * (1 + alt_buffer)
            
            alternative_entries = [alt1, alt2]
        
        return {
            'primary_entry': primary_entry,
            'alternative_entries': alternative_entries,
            'entry_method': 'poi_based' if poi_analysis.get('poi_found') else 'market_based'
        }
    
    def _calculate_killzone_sl_tp(self, entry_price: float, signal_direction: str,
                                 best_sweep: Dict[str, Any], 
                                 previous_levels: Dict[str, Any]) -> Dict[str, Any]:
        """
        Killzone stratejisi için Stop Loss ve Take Profit hesaplar.
        """
        swept_level = best_sweep['target_level']
        
        # Stop Loss: Swept level'ın ötesine koy
        sl_buffer_pct = 0.0015  # %0.15
        
        if signal_direction == 'BULLISH':
            # Bullish için swept level (low) altına SL
            stop_loss = swept_level * (1 - sl_buffer_pct)
        else:
            # Bearish için swept level (high) üstüne SL
            stop_loss = swept_level * (1 + sl_buffer_pct)
        
        # Risk miktarı
        risk_amount = abs(entry_price - stop_loss)
        
        # Take Profit seviyeleri
        take_profits = []
        rr_ratios = [2.0, 3.5, 5.0]  # Agresif RR oranları
        
        for rr in rr_ratios:
            if signal_direction == 'BULLISH':
                tp = entry_price + (risk_amount * rr)
            else:
                tp = entry_price - (risk_amount * rr)
            
            take_profits.append({
                'price': tp,
                'rr_ratio': rr,
                'risk_reward': f"1:{rr}"
            })
        
        # Previous session range'ine göre TP ayarlaması
        session_range = previous_levels['session_range']
        if session_range > 0:
            # İlk TP'yi session range'inin %80'i kadar yap (eğer makul mesafedeyse)
            target_distance = session_range * 0.8
            target_rr = target_distance / risk_amount
            
            if 1.5 <= target_rr <= 4.0:
                if signal_direction == 'BULLISH':
                    adjusted_tp = entry_price + target_distance
                else:
                    adjusted_tp = entry_price - target_distance
                
                take_profits[0] = {
                    'price': adjusted_tp,
                    'rr_ratio': target_rr,
                    'risk_reward': f"1:{target_rr:.1f}",
                    'type': 'session_range_target'
                }
        
        return {
            'stop_loss': stop_loss,
            'take_profits': take_profits,
            'risk_reward': rr_ratios[0],
            'risk_percentage': (risk_amount / entry_price) * 100
        }
    
    def _create_scalping_signal(self, main_signal: Dict[str, Any],
                               entry_levels: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """
        Yüksek confluence durumunda scalping sinyali oluşturur.
        """
        scalping_signal = main_signal.copy()
        scalping_signal['type'] = 'KILLZONE_SCALPING'
        scalping_signal['signal_strength'] = 'EXCELLENT'
        
        # Scalping için daha sıkı SL/TP
        entry_price = scalping_signal['primary_entry']
        main_sl = scalping_signal['stop_loss']
        
        # Risk miktarını %50 azalt
        risk_amount = abs(entry_price - main_sl) * 0.5
        
        if scalping_signal['direction'] == 'BULLISH':
            scalping_sl = entry_price - risk_amount
            scalping_tp = entry_price + (risk_amount * 1.5)  # 1:1.5 RR
        else:
            scalping_sl = entry_price + risk_amount
            scalping_tp = entry_price - (risk_amount * 1.5)
        
        scalping_signal['stop_loss'] = scalping_sl
        scalping_signal['take_profits'] = [{
            'price': scalping_tp,
            'rr_ratio': 1.5,
            'risk_reward': '1:1.5',
            'type': 'scalping_target'
        }]
        scalping_signal['risk_reward_ratio'] = 1.5
        
        return scalping_signal
    
    def _empty_result(self) -> Dict[str, Any]:
        """
        Boş analiz sonucu döner.
        """
        return {
            'active_killzone': None,
            'previous_session_levels': None,
            'manipulation_analysis': {'manipulation_detected': False},
            'structure_confirmation': {'confirmation_available': False},
            'signals': [],
            'analysis_timestamp': pd.Timestamp.now().isoformat(),
            'total_signals': 0
        }
