# liquidity_analyzer.py
# Unified ICT Liquidity Analysis Module
# Birleştirilmiş: external_liquidity_analyzer.py + liqsfp_analyzer.py + liquidity_analyzer.py

import pandas as pd
import numpy as np
from loguru import logger
from typing import List, Dict, Any, Optional, Tuple
from datetime import datetime, timed<PERSON>ta
from exceptions import InvalidDataError

class LiquidityAnalyzer:
    """
    ICT (Inner Circle Trader) konseptlerine göre kapsamlı likidite analizi.
    
    Bu birleştirilmiş modül şunları içerir:
    - External Liquidity (BSL/SSL bölgeleri)  
    - LIQSFP (Liquidity + SFP kombinasyonu)
    - Swing Failure Pattern (SFP) detection
    - Liquidity Hunt ve Reversal analizi
    - IDM (Institutional Dollar Millionaire) konfirmasyonu
    
    ICT Konseptleri:
    - BSL (Buy Side Liquidity): Swing high'ların üzerindeki stop loss'lar
    - SSL (Sell Side Liquidity): Swing low'ların altındaki stop loss'lar  
    - Liquidity Hunt: Likidite bölgelerine kısa süreli giriş
    - SFP: Swing Failure Pattern - reversal sinyali
    - IDM: Institutional confirmation patterns
    """
    
    def __init__(self, 
                 hunt_tolerance_percent: float = 0.2,
                 min_swing_strength: int = 3,
                 liquidity_expiry_hours: int = 72,
                 hunt_confirmation_pips: float = 5.0,
                 liqsfp_tolerance_pct: float = 0.0):
        """
        Unified Liquidity Analyzer'ı başlatır.
        
        Args:
            hunt_tolerance_percent: Hunt toleransı (% olarak)
            min_swing_strength: Minimum swing gücü (kaç mumda pivot)
            liquidity_expiry_hours: Likidite bölgesinin geçerlilik süresi
            hunt_confirmation_pips: Hunt onayı için minimum pip hareketi
            liqsfp_tolerance_pct: LIQSFP detection için tolerans (% olarak)
        """
        self.hunt_tolerance = hunt_tolerance_percent / 100
        self.min_swing_strength = min_swing_strength
        self.liquidity_expiry_hours = liquidity_expiry_hours
        self.hunt_confirmation_pips = hunt_confirmation_pips
        self.liqsfp_tolerance_pct = liqsfp_tolerance_pct
        
        logger.info(f"ICT Unified Liquidity Analyzer başlatıldı - "
                   f"Hunt Tolerance: {hunt_tolerance_percent}%, "
                   f"Min Swing: {min_swing_strength}, "
                   f"Expiry: {liquidity_expiry_hours}h, "
                   f"LIQSFP Tolerance: {liqsfp_tolerance_pct}%")

    def analyze(self, candles: pd.DataFrame, all_pivots: list, symbol: str = '') -> dict:
        """
        Ana likidite analiz metodu - tüm liquidity türlerini analiz eder.
        
        Bu metod backward compatibility için korundu ve artık
        comprehensive liquidity analysis yapar.
        
        Args:
            candles (pd.DataFrame): Mum verileri
            all_pivots (list): Tüm pivot noktaları (major + internal birleşik)
            
        Returns:
            dict: Unified liquidity analysis sonuçları
        """
        if candles is None or len(candles) < 10:
            raise InvalidDataError(f"Likidite analizi için yetersiz mum verisi. Gerekli: 10, Mevcut: {len(candles) if candles is not None else 0}")
            
        logger.info(f"🌊 ICT Unified Liquidity Analysis başlıyor - "
                   f"{len(candles)} mum, {len(all_pivots)} toplam pivot")
        
        # 1. External Liquidity Analysis (BSL/SSL zones)
        external_liquidity = self.analyze_external_liquidity(candles, all_pivots)
        
        # 2. LIQSFP Analysis (Advanced SFP with liquidity hunt)
        liqsfp_results = self.detect_liqsfp(candles, all_pivots, symbol)
        
        # 3. Traditional SFP Analysis (Tüm pivotlar)
        sfps = self._find_sfps(candles, all_pivots)
        
        # 4. External Liquidity Hunt Detection
        external_hunts = self.detect_external_liquidity_hunt(candles, all_pivots)
        
        # 5. Equal Highs/Lows Analysis (LuxAlgo SMC Enhancement)
        equal_levels = self.analyze_equal_highs_lows(candles, all_pivots)
        
        # 6. Unified Trading Signals Generation
        unified_signals = self._generate_unified_liquidity_signals(
            external_liquidity, liqsfp_results, external_hunts, equal_levels
        )
        
        # Sonuçları birleştir
        unified_result = {
            # Backward compatibility için eski format
            'sfps': sfps,
            
            # Yeni unified format
            'external_liquidity': external_liquidity,
            'liqsfp_analysis': liqsfp_results,
            'external_hunts': external_hunts,
            'equal_levels': equal_levels,
            
            # Standart sinyal çıktısı (scoring_system için)
            'signals': unified_signals,
            
            # Summary
            'summary': {
                'total_sfps': len(sfps),
                'total_liqsfp': len(liqsfp_results.get('detected_liqsfp', [])),
                'active_bsl_zones': len(external_liquidity.get('bsl_zones', [])),
                'active_ssl_zones': len(external_liquidity.get('ssl_zones', [])),
                'high_quality_hunts': len([h for h in external_hunts if h.get('quality_score', 0) > 7]),
                'equal_highs': len(equal_levels.get('equal_highs', [])),
                'equal_lows': len(equal_levels.get('equal_lows', [])),
                'total_signals': len(unified_signals),
                'high_quality_signals': len([s for s in unified_signals if s.get('quality_score', 0) >= 8])
            }
        }
        
        logger.success(f"✅ Unified Liquidity Analysis tamamlandı: "
                      f"SFP={len(sfps)}, LIQSFP={len(liqsfp_results.get('detected_liqsfp', []))}, "
                      f"BSL={len(external_liquidity.get('bsl_zones', []))}, "
                      f"SSL={len(external_liquidity.get('ssl_zones', []))}, "
                      f"EQH={len(equal_levels.get('equal_highs', []))}, "
                      f"EQL={len(equal_levels.get('equal_lows', []))}")
        
        return unified_result

    def analyze_external_liquidity(self, candles: pd.DataFrame, 
                                 swing_points: List[Dict[str, Any]]) -> Dict[str, Any]:
        """
        External liquidity analizi yapar.
        
        ICT Mantığı:
        - Swing high/low'lar üzerinde/altında likidite bulunur
        - Institutional traders bu likiditeyi "hunt" eder
        - Hunt sonrası genellikle güçlü reversal gelir
        
        Args:
            candles: Mum verileri
            swing_points: Pivot/swing noktaları
            
        Returns:
            External liquidity analiz sonuçları
        """
        if candles is None or len(candles) < 10:
            raise InvalidDataError(f"External liquidity analizi için yetersiz mum verisi. Gerekli: 10, Mevcut: {len(candles) if candles is not None else 0}")
        
        if not swing_points or len(swing_points) < 2:
            raise InvalidDataError(f"External liquidity analizi için yetersiz swing noktası. Gerekli: 2, Mevcut: {len(swing_points) if swing_points is not None else 0}")
        
        if not swing_points or len(swing_points) < 2:
            logger.warning("External liquidity analizi için yetersiz swing noktası")
            return self._empty_liquidity_result()
        
        logger.debug(f"🔍 External Liquidity analizi - "
                    f"{len(candles)} mum, {len(swing_points)} swing")
        
        # Aktif likidite bölgelerini tespit et
        bsl_zones = self._identify_bsl_zones(swing_points, candles)
        ssl_zones = self._identify_ssl_zones(swing_points, candles)
        
        # Hunt analizini gerçekleştir
        hunts = self._analyze_liquidity_hunts(candles, bsl_zones + ssl_zones)
        
        # Likidite bölgelerini skorla
        scored_bsl = self._score_liquidity_zones(bsl_zones, candles)
        scored_ssl = self._score_liquidity_zones(ssl_zones, candles)
        
        # En yakın likiditeyi bul
        current_price = float(candles.iloc[-1]['close'])
        nearest_liquidity = self._find_nearest_liquidity(scored_bsl + scored_ssl, current_price)
        
        # Hunt sinyalleri üret
        hunt_signals = self._generate_hunt_signals(hunts)
        
        result = {
            'bsl_zones': scored_bsl,
            'ssl_zones': scored_ssl,
            'all_zones': scored_bsl + scored_ssl,
            'hunts': hunts,
            'hunt_signals': hunt_signals,
            'nearest_liquidity': nearest_liquidity,
            'analysis_timestamp': datetime.now().isoformat(),
            'zone_count': {
                'bsl': len(scored_bsl),
                'ssl': len(scored_ssl),
                'total': len(scored_bsl + scored_ssl)
            },
            'hunt_count': {
                'total': len(hunts),
                'high_quality': len([h for h in hunts if h.get('quality_score', 0) > 7]),
                'signals': len(hunt_signals)
            }
        }
        
        logger.debug(f"✅ External Liquidity: BSL={len(scored_bsl)}, SSL={len(scored_ssl)}, Hunts={len(hunts)}")
        return result

    def detect_liqsfp(self, candles: pd.DataFrame, swing_points: List[Dict[str, Any]], symbol: str = '') -> Dict[str, Any]:
        """
        LIQSFP (Liquidity + SFP) detection yapar.
        
        GELIŞTIRILMIŞ VERSIYON: Eski liqsfp_analyzer mantığı entegre edildi.
        
        LIQSFP, liquidity hunt ile SFP'nin birleşimi olan güçlü bir ICT sinyalidir.
        1. LIQSFP_REV (Reversal): Likidite alındıktan sonra ters yöne kırılma (Stop Hunt)
        
        Args:
            candles: Mum verileri
            swing_points: Swing noktaları
            
        Returns:
            LIQSFP analiz sonuçları
        """
        detected_liqsfp = []
        recent_sfps = []
        
        if not swing_points or len(candles) < 20:
            return {
                'detected_liqsfp': detected_liqsfp,
                'recent_sfps': recent_sfps,
                'analysis_timestamp': pd.Timestamp.now().isoformat()
            }
        
        logger.debug(f"🔍 GELIŞMIŞ LIQSFP analizi başlıyor - {len(swing_points)} swing point")
        
        last_candle = candles.iloc[-1]
        
        # --- YÜKSEK PIVOTların Likiditesini Arama (Eski mantık adapte edildi) ---
        swing_highs = []
        for swing in swing_points:
            swing_type = swing.get('type', '')
            swing_ict_type = swing.get('ict_type', '')
            swing_index = swing.get('candle_index', swing.get('index', 0))
            
            # High tipindeki swing'ler (hem temel tip hem de ICT tip)
            if (swing_type == 'high' or swing_ict_type in ['HH', 'LH']) and swing_index < len(candles) - 2:
                swing_highs.append(swing)
        
        # En son high pivot'u bul (eski kodun mantığı)
        if swing_highs:
            swing_highs_sorted = sorted(swing_highs, key=lambda x: x.get('candle_index', x.get('index', 0)), reverse=True)
            target_high_pivot = swing_highs_sorted[0]
            target_price = float(target_high_pivot.get('price', 0))
            
            # Koşul 1: Son mumun yükseği hedef pivotun yükseğini geçti mi (Likidite Sweep)
            if last_candle['high'] > target_price * (1 + self.liqsfp_tolerance_pct / 100):
                # Koşul 2a: Fiyat, hedef pivotun ALTINDA mı kapattı? -> BEARISH REVERSAL (Stop Hunt)
                if last_candle['close'] < target_price:
                    liqsfp_event = {
                        'name': 'LIQSFP_REV_BEAR',  # Eski format uyumluluğu
                        'type': 'BEARISH_REVERSAL_LIQSFP',
                        'direction': 'BEARISH',
                        'pattern': 'LIQSFP_REV',  # Eski format
                        'quality': 'HIGH',
                        'symbol': symbol,  # Symbol bilgisi eklendi
                        'swing_price': target_price,
                        'hunt_price': float(last_candle['high']),
                        'close_price': float(last_candle['close']),
                        'candle_index': len(candles) - 1,
                        'timestamp': last_candle.get('timestamp', pd.Timestamp.now()),
                        'last_pivot_index': last_candle.name,  # Eski format
                        'idm_confirmed': self._check_idm_confirmation(last_candle, target_price, 'BEARISH'),
                        'near_round_number': self._is_near_round_number(target_price),
                        'hunt_strength': abs(float(last_candle['high']) - target_price) / target_price * 100
                    }
                    detected_liqsfp.append(liqsfp_event)
                    logger.success(f"🔥 BEARISH REVERSAL LIQSFP (Stop Hunt) tespit edildi! "
                                 f"Hedef High: {target_price}, Close: {last_candle['close']}")
        
        # --- DÜŞÜK PIVOTların Likiditesini Arama (Eski mantık adapte edildi) ---
        swing_lows = []
        for swing in swing_points:
            swing_type = swing.get('type', '')
            swing_ict_type = swing.get('ict_type', '')
            swing_index = swing.get('candle_index', swing.get('index', 0))
            
            # Low tipindeki swing'ler (hem temel tip hem de ICT tip)
            if (swing_type == 'low' or swing_ict_type in ['LL', 'HL']) and swing_index < len(candles) - 2:
                swing_lows.append(swing)
        
        # En son low pivot'u bul (eski kodun mantığı)
        if swing_lows:
            swing_lows_sorted = sorted(swing_lows, key=lambda x: x.get('candle_index', x.get('index', 0)), reverse=True)
            target_low_pivot = swing_lows_sorted[0]
            target_price = float(target_low_pivot.get('price', 0))
            
            # Koşul 1: Son mumun düşüğü hedef pivotun düşüğünü geçti mi (Likidite Sweep)
            if last_candle['low'] < target_price * (1 - self.liqsfp_tolerance_pct / 100):
                # Koşul 2a: Fiyat, hedef pivotun ÜSTÜNDE mi kapattı? -> BULLISH REVERSAL (Stop Hunt)
                if last_candle['close'] > target_price:
                    liqsfp_event = {
                        'name': 'LIQSFP_REV_BULL',  # Eski format uyumluluğu
                        'type': 'BULLISH_REVERSAL_LIQSFP',
                        'direction': 'BULLISH',
                        'pattern': 'LIQSFP_REV',  # Eski format
                        'quality': 'HIGH',
                        'symbol': symbol,  # Symbol bilgisi eklendi
                        'swing_price': target_price,
                        'hunt_price': float(last_candle['low']),
                        'close_price': float(last_candle['close']),
                        'candle_index': len(candles) - 1,
                        'timestamp': last_candle.get('timestamp', pd.Timestamp.now()),
                        'last_pivot_index': last_candle.name,  # Eski format
                        'idm_confirmed': self._check_idm_confirmation(last_candle, target_price, 'BULLISH'),
                        'near_round_number': self._is_near_round_number(target_price),
                        'hunt_strength': abs(target_price - float(last_candle['low'])) / target_price * 100
                    }
                    detected_liqsfp.append(liqsfp_event)
                    logger.success(f"🔥 BULLISH REVERSAL LIQSFP (Stop Hunt) tespit edildi! "
                                 f"Hedef Low: {target_price}, Close: {last_candle['close']}")
        
        # Son LIQSFP'leri recent_sfps listesine de ekle (backward compatibility)
        recent_sfps = [event for event in detected_liqsfp if 'REVERSAL' in event.get('type', '')]
        
        result = {
            'detected_liqsfp': detected_liqsfp,
            'recent_sfps': recent_sfps,
            'reversal_count': len([e for e in detected_liqsfp if 'REVERSAL' in e.get('type', '')]),
            'high_quality_count': len([e for e in detected_liqsfp if e.get('quality') == 'HIGH']),
            'analysis_timestamp': pd.Timestamp.now().isoformat()
        }
        
        if detected_liqsfp:
            logger.info(f"✅ GELIŞMIŞ LIQSFP analizi: {len(detected_liqsfp)} event, "
                       f"{result['reversal_count']} reversal")
        
        return result

    def detect_external_liquidity_hunt(self, candles: pd.DataFrame, 
                                     all_pivots: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """
        External liquidity hunt detection - gelişmiş hunt analizi.
        
        Args:
            candles: Mum verileri
            all_pivots: Tüm swing points (major + internal birleşik)
            
        Returns:
            Detected hunt events
        """
        detected_hunts = []
        
        if not all_pivots or len(candles) < 50:
            return detected_hunts
        
        logger.debug(f"🎯 External Liquidity Hunt analizi - {len(all_pivots)} toplam pivot")
        
        # HTF (Higher Time Frame) swing'leri filtrele
        htf_swings = self._filter_htf_swings(all_pivots, timeframe_filter='4h')
        
        # Son 100 mumu analiz et
        recent_candles = candles.tail(100)
        
        for swing in htf_swings[-10:]:  # Son 10 HTF swing
            hunt_result = self._analyze_liquidity_hunt_at_swing(
                recent_candles, swing
            )
            
            if hunt_result and hunt_result.get('hunt_confirmed'):
                detected_hunts.append(hunt_result)
                
                hunt_type = hunt_result.get('hunt_type', 'UNKNOWN')
                quality = hunt_result.get('quality_score', 0)
                logger.info(f"🎯 External Liquidity Hunt tespit edildi: {hunt_type}, "
                           f"Quality: {quality}/10")
        
        return detected_hunts

    # =================================================================================
    # PRIVATE HELPER METHODS - Eski modüllerden konsolide edilmiş
    # =================================================================================

    def _find_sfps(self, candles: pd.DataFrame, pivots: list) -> list:
        """Traditional SFP detection (backward compatibility)."""
        sfps = []
        if not pivots or len(candles) == 0:
            return sfps

        for pivot in pivots:
            start_index = pivot.get('candle_index', 0) + 1
            end_index = min(start_index + 5, len(candles))

            if start_index >= len(candles):
                continue

            pivot_price = float(pivot.get('price', 0))
            pivot_type = pivot.get('type', '')

            for i in range(start_index, end_index):
                candle = candles.iloc[i]
                
                # Hem temel tip hem de ICT tip kontrolü
                if (pivot_type == 'high' or pivot.get('ict_type') in ['HH', 'LH']) and candle['high'] > pivot_price:
                    if candle['close'] < pivot_price:
                        sfp = {
                            'type': 'bearish_sfp',
                            'pivot_index': pivot.get('candle_index', 0),
                            'sfp_candle_index': i,
                            'pivot_price': pivot_price,
                            'hunt_price': float(candle['high']),
                            'close_price': float(candle['close']),
                            'timestamp': candle.get('timestamp', datetime.now())
                        }
                        sfps.append(sfp)
                        logger.debug(f"🔻 Bearish SFP tespit edildi: {pivot_price} -> {candle['high']}")
                
                elif (pivot_type == 'low' or pivot.get('ict_type') in ['LL', 'HL']) and candle['low'] < pivot_price:
                    if candle['close'] > pivot_price:
                        sfp = {
                            'type': 'bullish_sfp',
                            'pivot_index': pivot.get('candle_index', 0),
                            'sfp_candle_index': i,
                            'pivot_price': pivot_price,
                            'hunt_price': float(candle['low']),
                            'close_price': float(candle['close']),
                            'timestamp': candle.get('timestamp', datetime.now())
                        }
                        sfps.append(sfp)
                        logger.debug(f"🔺 Bullish SFP tespit edildi: {pivot_price} -> {candle['low']}")

        return sfps

    def _identify_bsl_zones(self, swing_points: List[Dict[str, Any]], 
                           candles: pd.DataFrame) -> List[Dict[str, Any]]:
        """BSL (Buy Side Liquidity) zones identification."""
        bsl_zones = []
        
        # Swing high'ları filtrele (hem temel tip hem de ICT tip destekli)
        swing_highs = [s for s in swing_points if s.get('type') == 'high' or s.get('ict_type') in ['HH', 'LH']]
        
        for swing in swing_highs[-20:]:  # Son 20 swing high
            swing_price = float(swing.get('price', 0))
            swing_time = swing.get('timestamp', pd.Timestamp.now())
            
            # Expiry kontrolü
            if isinstance(swing_time, str):
                swing_time = pd.to_datetime(swing_time)
            
            current_time = pd.Timestamp.now()
            age_hours = (current_time - swing_time).total_seconds() / 3600
            if age_hours > self.liquidity_expiry_hours:
                continue
            
            # BSL zone oluştur
            bsl_zone = {
                'type': 'BSL',
                'price': swing_price,
                'swing_data': swing,
                'age_hours': age_hours,
                'strength': self._calculate_zone_strength(swing, candles),
                'hunted': self._check_if_hunted(candles, swing_price, 'high'),
                'distance_from_current': abs(swing_price - float(candles.iloc[-1]['close'])) / float(candles.iloc[-1]['close']) * 100
            }
            bsl_zones.append(bsl_zone)
        
        return bsl_zones

    def _identify_ssl_zones(self, swing_points: List[Dict[str, Any]], 
                           candles: pd.DataFrame) -> List[Dict[str, Any]]:
        """SSL (Sell Side Liquidity) zones identification."""
        ssl_zones = []
        
        # Swing low'ları filtrele (hem temel tip hem de ICT tip destekli)
        swing_lows = [s for s in swing_points if s.get('type') == 'low' or s.get('ict_type') in ['LL', 'HL']]
        
        for swing in swing_lows[-20:]:  # Son 20 swing low
            swing_price = float(swing.get('price', 0))
            swing_time = swing.get('timestamp', pd.Timestamp.now())
            
            # Expiry kontrolü
            if isinstance(swing_time, str):
                swing_time = pd.to_datetime(swing_time)
            
            current_time = pd.Timestamp.now()
            age_hours = (current_time - swing_time).total_seconds() / 3600
            if age_hours > self.liquidity_expiry_hours:
                continue
            
            # SSL zone oluştur
            ssl_zone = {
                'type': 'SSL',
                'price': swing_price,
                'swing_data': swing,
                'age_hours': age_hours,
                'strength': self._calculate_zone_strength(swing, candles),
                'hunted': self._check_if_hunted(candles, swing_price, 'low'),
                'distance_from_current': abs(swing_price - float(candles.iloc[-1]['close'])) / float(candles.iloc[-1]['close']) * 100
            }
            ssl_zones.append(ssl_zone)
        
        return ssl_zones

    def _calculate_zone_strength(self, swing: Dict[str, Any], candles: pd.DataFrame) -> str:
        """Zone strength calculation."""
        # Basit strength calculation
        swing_index = swing.get('candle_index', 0)
        if swing_index < len(candles) - 1:
            swing_candle = candles.iloc[swing_index]
            volume = swing_candle.get('volume', 1)
            
            # Volume-based strength
            avg_volume = candles['volume'].tail(50).mean()
            if volume > avg_volume * 1.5:
                return 'high'
            elif volume > avg_volume:
                return 'medium'
        
        return 'low'

    def _check_if_hunted(self, candles: pd.DataFrame, level: float, 
                        direction: str) -> bool:
        """Check if liquidity level has been hunted."""
        recent_candles = candles.tail(20)
        tolerance = level * self.hunt_tolerance
        
        for _, candle in recent_candles.iterrows():
            if direction == 'high' and candle['high'] > level + tolerance:
                return True
            elif direction == 'low' and candle['low'] < level - tolerance:
                return True
        
        return False

    def _analyze_liquidity_hunts(self, candles: pd.DataFrame, 
                               zones: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """Analyze liquidity hunts for given zones."""
        hunts = []
        
        for zone in zones:
            zone_price = zone['price']
            zone_type = zone['type']
            
            # Hunt detection
            hunt_candle = self._find_hunt_candle(candles, zone_price, zone_type)
            
            if hunt_candle is not None:
                # Post-hunt reversal analysis
                reversal_data = self._analyze_post_hunt_reversal(
                    candles, hunt_candle, zone_price, zone_type
                )
                
                hunt_event = {
                    'zone': zone,
                    'hunt_candle_index': hunt_candle,
                    'hunt_price': zone_price,
                    'hunt_type': zone_type,
                    'reversal_data': reversal_data,
                    'quality_score': self._score_hunt_quality({
                        'zone': zone,
                        'reversal_data': reversal_data
                    }, candles),
                    'timestamp': datetime.now().isoformat()
                }
                hunts.append(hunt_event)
        
        return hunts

    def _find_hunt_candle(self, candles: pd.DataFrame, level: float, 
                         zone_type: str) -> Optional[int]:
        """Find the candle that hunted the liquidity level."""
        recent_candles = candles.tail(50)
        tolerance = level * self.hunt_tolerance
        
        for i, (idx, candle) in enumerate(recent_candles.iterrows()):
            if zone_type == 'BSL' and candle['high'] > level + tolerance:
                return len(candles) - len(recent_candles) + i
            elif zone_type == 'SSL' and candle['low'] < level - tolerance:
                return len(candles) - len(recent_candles) + i
        
        return None

    def _analyze_post_hunt_reversal(self, candles: pd.DataFrame, 
                                   hunt_candle_index: int, level: float, 
                                   zone_type: str) -> Dict[str, Any]:
        """Analyze reversal after liquidity hunt."""
        if hunt_candle_index >= len(candles) - 1:
            return {'reversal_confirmed': False}
        
        hunt_candle = candles.iloc[hunt_candle_index]
        post_hunt_candles = candles.iloc[hunt_candle_index + 1:hunt_candle_index + 6]
        
        if len(post_hunt_candles) == 0:
            return {'reversal_confirmed': False}
        
        # Reversal strength calculation
        reversal_confirmed = False
        reversal_strength = 0
        
        if zone_type == 'BSL':  # Expect bearish reversal
            for _, candle in post_hunt_candles.iterrows():
                if candle['close'] < level:
                    reversal_confirmed = True
                    reversal_strength = (level - candle['close']) / level * 100
                    break
        
        elif zone_type == 'SSL':  # Expect bullish reversal
            for _, candle in post_hunt_candles.iterrows():
                if candle['close'] > level:
                    reversal_confirmed = True
                    reversal_strength = (candle['close'] - level) / level * 100
                    break
        
        return {
            'reversal_confirmed': reversal_confirmed,
            'reversal_strength': reversal_strength,
            'post_hunt_candles': len(post_hunt_candles)
        }

    def _score_liquidity_zones(self, zones: List[Dict[str, Any]], 
                              candles: pd.DataFrame) -> List[Dict[str, Any]]:
        """Score liquidity zones based on various factors."""
        scored_zones = []
        
        for zone in zones:
            score = 5.0  # Base score
            
            # Age factor (fresher is better)
            age_hours = zone.get('age_hours', 0)
            if age_hours < 24:
                score += 1.5
            elif age_hours < 48:
                score += 0.5
            else:
                score -= 0.5
            
            # Strength factor
            strength = zone.get('strength', 'medium')
            if strength == 'high':
                score += 2.0
            elif strength == 'medium':
                score += 1.0
            
            # Distance factor (closer is better for immediate trade)
            distance = zone.get('distance_from_current', 999)
            if distance < 1.0:  # %1'den yakın
                score += 1.5
            elif distance < 2.0:  # %2'den yakın
                score += 1.0
            elif distance > 5.0:  # %5'ten uzak
                score -= 1.0
            
            # Hunted factor (unhunted is better)
            if not zone.get('hunted', False):
                score += 1.0
            else:
                score -= 2.0
            
            zone_copy = zone.copy()
            zone_copy['score'] = max(0, min(10, score))  # 0-10 arası normalize et
            scored_zones.append(zone_copy)
        
        # Score'a göre sırala
        scored_zones.sort(key=lambda x: x['score'], reverse=True)
        return scored_zones

    def _score_hunt_quality(self, hunt_data: Dict[str, Any], 
                           candles: pd.DataFrame) -> float:
        """Score the quality of a liquidity hunt."""
        score = 5.0  # Base score
        
        zone = hunt_data.get('zone', {})
        reversal_data = hunt_data.get('reversal_data', {})
        
        # Zone strength factor
        strength = zone.get('strength', 'medium')
        if strength == 'high':
            score += 2.0
        elif strength == 'medium':
            score += 1.0
        
        # Reversal confirmation factor
        if reversal_data.get('reversal_confirmed', False):
            score += 2.5
            
            # Reversal strength factor
            reversal_strength = reversal_data.get('reversal_strength', 0)
            if reversal_strength > 1.0:  # %1'den fazla reversal
                score += 1.0
            if reversal_strength > 2.0:  # %2'den fazla reversal
                score += 0.5
        
        # Zone age factor
        age_hours = zone.get('age_hours', 999)
        if age_hours < 24:
            score += 1.0
        elif age_hours < 48:
            score += 0.5
        
        return max(0, min(10, score))  # 0-10 normalize

    def _find_nearest_liquidity(self, zones: List[Dict[str, Any]], 
                               current_price: float) -> Optional[Dict[str, Any]]:
        """Find the nearest liquidity zone to current price."""
        if not zones:
            return None
        
        nearest_zone = None
        min_distance = float('inf')
        
        for zone in zones:
            distance = abs(zone['price'] - current_price)
            if distance < min_distance:
                min_distance = distance
                nearest_zone = zone
        
        if nearest_zone:
            nearest_zone['distance'] = min_distance
            nearest_zone['distance_pct'] = (min_distance / current_price) * 100
        
        return nearest_zone

    def _generate_hunt_signals(self, hunts: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """Generate trading signals from hunt events."""
        signals = []
        
        for hunt in hunts:
            quality_score = hunt.get('quality_score', 0)
            
            # Sadece yüksek kaliteli hunt'ler için sinyal üret
            if quality_score >= 7.0:
                reversal_data = hunt.get('reversal_data', {})
                
                if reversal_data.get('reversal_confirmed', False):
                    signal = {
                        'type': 'LIQUIDITY_HUNT_REVERSAL',
                        'direction': 'BEARISH' if hunt.get('hunt_type') == 'BSL' else 'BULLISH',
                        'entry_level': hunt.get('hunt_price'),
                        'quality_score': quality_score,
                        'reversal_strength': reversal_data.get('reversal_strength', 0),
                        'timestamp': hunt.get('timestamp'),
                        'hunt_data': hunt
                    }
                    signals.append(signal)
        
        return signals

    def _filter_htf_swings(self, swing_points: List[Dict[str, Any]], 
                          timeframe_filter: str = '4h') -> List[Dict[str, Any]]:
        """Filter swings to higher timeframe significant ones."""
        # Basit filtreleme - güçlü swing'leri al
        htf_swings = []
        
        for swing in swing_points:
            # Strength veya importance göstergesi varsa kullan
            strength = swing.get('strength', 1)
            if strength >= self.min_swing_strength:
                htf_swings.append(swing)
        
        return htf_swings[-50:]  # Son 50 güçlü swing

    def _analyze_liquidity_hunt_at_swing(self, recent_candles: pd.DataFrame,
                                       swing: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """Analyze liquidity hunt at specific swing level."""
        swing_price = float(swing.get('price', 0))
        swing_type = swing.get('type', '')
        
        # Hunt detection
        hunt_confirmed = False
        hunt_candle = None
        
        tolerance = swing_price * self.hunt_tolerance
        
        for i, (idx, candle) in enumerate(recent_candles.iterrows()):
            if swing_type == 'high' and candle['high'] > swing_price + tolerance:
                hunt_confirmed = True
                hunt_candle = candle
                break
            elif swing_type == 'low' and candle['low'] < swing_price - tolerance:
                hunt_confirmed = True
                hunt_candle = candle
                break
        
        if not hunt_confirmed:
            return None
        
        # Hunt quality analysis
        hunt_result = {
            'hunt_confirmed': True,
            'swing_data': swing,
            'hunt_candle': hunt_candle.to_dict(),
            'hunt_type': f"{swing_type}_HUNT",
            'hunt_price': swing_price,
            'actual_hunt_price': float(hunt_candle['high'] if swing_type == 'high' else hunt_candle['low']),
            'quality_score': self._calculate_hunt_quality_score(swing, hunt_candle),
            'timestamp': datetime.now().isoformat()
        }
        
        return hunt_result

    def _calculate_hunt_quality_score(self, swing: Dict[str, Any], 
                                    hunt_candle: pd.Series) -> float:
        """Calculate hunt quality score."""
        score = 5.0  # Base score
        
        # Volume factor
        volume = hunt_candle.get('volume', 1)
        if volume > 1000000:  # Yüksek volume
            score += 1.5
        
        # Price action factor
        swing_price = float(swing.get('price', 0))
        hunt_price = float(hunt_candle['high'] if swing.get('type') == 'high' else hunt_candle['low'])
        
        hunt_strength = abs(hunt_price - swing_price) / swing_price * 100
        if hunt_strength > 0.5:  # %0.5'ten fazla penetration
            score += 1.0
        if hunt_strength > 1.0:  # %1'den fazla penetration
            score += 0.5
        
        # Confluence check removed - artık unified pivot approach kullanıyoruz
        # Bu özellik gelecekte geliştirilebilir
        
        return max(0, min(10, score))

    def _check_idm_confirmation(self, last_candle: pd.Series, swing_price: float,
                               direction: str) -> bool:
        """Check for IDM (Institutional Dollar Millionaire) confirmation."""
        # Basit IDM kontrolü - gerçek implementasyon daha karmaşık
        body_size = abs(last_candle['close'] - last_candle['open'])
        total_range = last_candle['high'] - last_candle['low']
        
        # Strong body (>50% of range) IDM göstergesi
        if total_range > 0 and (body_size / total_range) > 0.5:
            if direction == 'BEARISH' and last_candle['close'] < last_candle['open']:
                return True
            elif direction == 'BULLISH' and last_candle['close'] > last_candle['open']:
                return True
        
        return False

    def _is_near_round_number(self, price: float, tolerance: float = 0.001) -> bool:
        """Check if price is near a round number (psychological level)."""
        # Round numbers: 1.0000, 1.1000, 1.2000, etc.
        rounded = round(price, 1)  # 1 decimal
        return abs(price - rounded) / price < tolerance

    def _empty_liquidity_result(self) -> Dict[str, Any]:
        """Return empty liquidity analysis result."""
        return {
            'bsl_zones': [],
            'ssl_zones': [],
            'all_zones': [],
            'hunts': [],
            'hunt_signals': [],
            'nearest_liquidity': None,
            'analysis_timestamp': datetime.now().isoformat(),
            'zone_count': {'bsl': 0, 'ssl': 0, 'total': 0},
            'hunt_count': {'total': 0, 'high_quality': 0, 'signals': 0},
            'external_liquidity': {
                'bsl_zones': [],
                'ssl_zones': []
            },
            'equal_levels': {
                'equal_highs': [],
                'equal_lows': [],
                'liquidity_events': [],
                'bsl_sweeps': [],
                'ssl_sweeps': []
            }
        }

    def analyze_equal_highs_lows(self, candles: pd.DataFrame, 
                                swing_points: List[Dict[str, Any]]) -> Dict[str, Any]:
        """
        Equal Highs/Lows (EQH/EQL) analizi - LuxAlgo SMC konsepti.
        
        EQH/EQL, aynı seviyelerde oluşan swing high/low'ların
        güçlü likidite bölgeleri oluşturduğu ICT konseptidir.
        
        ICT Mantığı:
        - EQH (Equal Highs): Aynı seviyedeki swing high'lar
        - EQL (Equal Lows): Aynı seviyedeki swing low'lar
        - Bu seviyeler üzerinde/altında institutional stop order'lar bulunur
        - Breakout sonrası genellikle strong impulsive move gelir
        
        Args:
            candles: Mum verileri
            swing_points: Pivot/swing noktaları
            
        Returns:
            Equal Highs/Lows analiz sonuçları
        """
        if candles is None or len(candles) < 10:
            logger.warning("Equal Highs/Lows analizi için yetersiz mum verisi")
            return self._empty_equal_levels_result()
        
        if not swing_points or len(swing_points) < 3:
            logger.warning("Equal Highs/Lows analizi için yetersiz swing noktası")
            return self._empty_equal_levels_result()
        
        logger.info(f"⚖️ Equal Highs/Lows analizi başlıyor - "
                   f"{len(candles)} mum, {len(swing_points)} swing")
        
        # 1. Swing high'ları ve low'ları ayır
        swing_highs = [s for s in swing_points if s.get('type') == 'high']
        swing_lows = [s for s in swing_points if s.get('type') == 'low']
        
        # 2. Equal Highs detection
        equal_highs = self._detect_equal_highs(swing_highs, candles)
        
        # 3. Equal Lows detection  
        equal_lows = self._detect_equal_lows(swing_lows, candles)
        
        # 4. Liquidity Sweep Events Analysis (ICT Mantığı)
        eqh_liquidity_events = self._analyze_eqh_breakouts(equal_highs, candles)
        eql_liquidity_events = self._analyze_eql_breakouts(equal_lows, candles)
        
        # 5. Quality scoring
        scored_eqh = self._score_equal_levels(equal_highs, 'HIGH')
        scored_eql = self._score_equal_levels(equal_lows, 'LOW')
        
        # 6. Combine all liquidity events
        all_liquidity_events = eqh_liquidity_events + eql_liquidity_events
        
        result = {
            'equal_highs': scored_eqh,
            'equal_lows': scored_eql,
            'liquidity_events': all_liquidity_events,  # Artık sinyal değil, olay
            'bsl_sweeps': eqh_liquidity_events,  # Buy Side Liquidity Sweeps
            'ssl_sweeps': eql_liquidity_events,  # Sell Side Liquidity Sweeps
            'summary': {
                'total_eqh': len(scored_eqh),
                'total_eql': len(scored_eql),
                'high_quality_eqh': len([e for e in scored_eqh if e.get('quality_score', 0) >= 7]),
                'high_quality_eql': len([e for e in scored_eql if e.get('quality_score', 0) >= 7]),
                'recent_liquidity_events': len(all_liquidity_events),
                'bsl_sweep_count': len(eqh_liquidity_events),
                'ssl_sweep_count': len(eql_liquidity_events)
            },
            'analysis_timestamp': datetime.now().isoformat()
        }
        
        logger.success(f"✅ Equal Highs/Lows analizi tamamlandı: "
                      f"EQH={len(scored_eqh)}, EQL={len(scored_eql)}, "
                      f"Events={len(all_liquidity_events)}")
        
        return result

    def _detect_equal_highs(self, swing_highs: List[Dict[str, Any]], 
                           candles: pd.DataFrame, 
                           tolerance_pct: float = 0.1) -> List[Dict[str, Any]]:
        """
        Equal Highs detection yapar.
        """
        equal_highs = []
        
        if len(swing_highs) < 2:
            return equal_highs
        
        # Son 20 swing high'ı al (performans için)
        recent_highs = swing_highs[-20:] if len(swing_highs) > 20 else swing_highs
        
        for i, high1 in enumerate(recent_highs):
            if i >= len(recent_highs) - 1:
                continue
                
            price1 = float(high1.get('price', 0))
            if price1 <= 0:
                continue
            
            equal_group = [high1]
            base_price = price1
            
            # Diğer swing high'larla karşılaştır
            for j, high2 in enumerate(recent_highs[i+1:], i+1):
                price2 = float(high2.get('price', 0))
                if price2 <= 0:
                    continue
                
                # Price tolerance check
                price_diff_pct = abs(price2 - base_price) / base_price * 100
                
                if price_diff_pct <= tolerance_pct:
                    equal_group.append(high2)
            
            # En az 2 equal high olmalı
            if len(equal_group) >= 2:
                # Bu grup daha önce eklendi mi kontrol et
                group_exists = False
                for existing_eqh in equal_highs:
                    if any(h['index'] == equal_group[0]['index'] for h in existing_eqh['swing_points']):
                        group_exists = True
                        break
                
                if not group_exists:
                    # Equal High grubunu oluştur
                    avg_price = sum(float(h.get('price', 0)) for h in equal_group) / len(equal_group)
                    
                    # Zaman aralığı
                    timestamps = [h.get('timestamp') for h in equal_group if h.get('timestamp')]
                    time_span = None
                    if len(timestamps) >= 2:
                        timestamps.sort()
                        time_diff = timestamps[-1] - timestamps[0]
                        time_span = time_diff / np.timedelta64(1, 's') / 3600  # hours
                    
                    eqh = {
                        'type': 'EQUAL_HIGHS',
                        'level': avg_price,
                        'swing_points': equal_group,
                        'count': len(equal_group),
                        'tolerance_pct': tolerance_pct,
                        'time_span_hours': time_span,
                        'strength': 'HIGH' if len(equal_group) >= 3 else 'MEDIUM',
                        'broken': False,
                        'breakout_candle_index': None,
                        'created_at': datetime.now().isoformat()
                    }
                    
                    equal_highs.append(eqh)
                    
                    logger.debug(f"🎯 Equal Highs detected: {len(equal_group)} highs @ {avg_price:.6f}")
        
        return equal_highs

    def _detect_equal_lows(self, swing_lows: List[Dict[str, Any]], 
                          candles: pd.DataFrame, 
                          tolerance_pct: float = 0.1) -> List[Dict[str, Any]]:
        """
        Equal Lows detection yapar.
        """
        equal_lows = []
        
        if len(swing_lows) < 2:
            return equal_lows
        
        # Son 20 swing low'ı al (performans için)
        recent_lows = swing_lows[-20:] if len(swing_lows) > 20 else swing_lows
        
        for i, low1 in enumerate(recent_lows):
            if i >= len(recent_lows) - 1:
                continue
                
            price1 = float(low1.get('price', 0))
            if price1 <= 0:
                continue
            
            equal_group = [low1]
            base_price = price1
            
            # Diğer swing low'larla karşılaştır
            for j, low2 in enumerate(recent_lows[i+1:], i+1):
                price2 = float(low2.get('price', 0))
                if price2 <= 0:
                    continue
                
                # Price tolerance check
                price_diff_pct = abs(price2 - base_price) / base_price * 100
                
                if price_diff_pct <= tolerance_pct:
                    equal_group.append(low2)
            
            # En az 2 equal low olmalı
            if len(equal_group) >= 2:
                # Bu grup daha önce eklendi mi kontrol et
                group_exists = False
                for existing_eql in equal_lows:
                    if any(l['index'] == equal_group[0]['index'] for l in existing_eql['swing_points']):
                        group_exists = True
                        break
                
                if not group_exists:
                    # Equal Low grubunu oluştur
                    avg_price = sum(float(l.get('price', 0)) for l in equal_group) / len(equal_group)
                    
                    # Zaman aralığı
                    timestamps = [l.get('timestamp') for l in equal_group if l.get('timestamp')]
                    time_span = None
                    if len(timestamps) >= 2:
                        timestamps.sort()
                        time_diff = timestamps[-1] - timestamps[0]
                    time_span = time_diff / np.timedelta64(1, 's') / 3600  # hours
                    
                    eql = {
                        'type': 'EQUAL_LOWS',
                        'level': avg_price,
                        'swing_points': equal_group,
                        'count': len(equal_group),
                        'tolerance_pct': tolerance_pct,
                        'time_span_hours': time_span,
                        'strength': 'HIGH' if len(equal_group) >= 3 else 'MEDIUM',
                        'broken': False,
                        'breakout_candle_index': None,
                        'created_at': datetime.now().isoformat()
                    }
                    
                    equal_lows.append(eql)
                    
                    logger.debug(f"🎯 Equal Lows detected: {len(equal_group)} lows @ {avg_price:.6f}")
        
        return equal_lows

    def _analyze_eqh_breakouts(self, equal_highs: List[Dict[str, Any]], 
                              candles: pd.DataFrame) -> List[Dict[str, Any]]:
        """
        Equal Highs likidite alım olaylarını tespit eder.
        ICT Mantığı: Bu artık ticaret sinyali değil, likidite olayı raporudur.
        """
        liquidity_events = []
        
        for eqh in equal_highs:
            level = eqh['level']
            last_swing_index = max(h.get('index', 0) for h in eqh['swing_points'])
            
            # Bu EQH'dan sonraki mumları kontrol et
            subsequent_candles = candles.iloc[last_swing_index + 1:]
            
            for i, (idx, candle) in enumerate(subsequent_candles.iterrows()):
                candle_high = float(candle['high'])
                candle_close = float(candle['close'])
                
                # BSL (Buy Side Liquidity) Sweep - EQH'ın üzerine çıkma
                if candle_high > level * 1.001:  # 0.1% buffer
                    liquidity_event = {
                        'event_type': 'BSL_SWEEP',  # Buy Side Liquidity Sweep
                        'level_type': 'EQH',
                        'swept_level': level,
                        'sweep_candle_index': last_swing_index + 1 + i,
                        'sweep_price': candle_high,
                        'sweep_timestamp': candle.get('timestamp', datetime.now()),
                        'sweep_strength': 'STRONG' if candle_close > level else 'WEAK',
                        'eqh_data': eqh,
                        'candle_data': candle.to_dict(),
                        'potential_reversal_direction': 'BEARISH'  # BSL sweep sonrası potansiyel yön
                    }
                    liquidity_events.append(liquidity_event)
                    
                    # EQH'ı swept olarak işaretle
                    eqh['swept'] = True
                    eqh['sweep_candle_index'] = last_swing_index + 1 + i
                    
                    logger.info(f"🌊 BSL SWEEP (EQH): @ {candle_high:.6f} "
                               f"(Level: {level:.6f}) - Potansiyel BEARISH reversal")
                    break
        
        return liquidity_events

    def _analyze_eql_breakouts(self, equal_lows: List[Dict[str, Any]], 
                              candles: pd.DataFrame) -> List[Dict[str, Any]]:
        """
        Equal Lows likidite alım olaylarını tespit eder.
        ICT Mantığı: Bu artık ticaret sinyali değil, likidite olayı raporudur.
        """
        liquidity_events = []
        
        for eql in equal_lows:
            level = eql['level']
            last_swing_index = max(l.get('index', 0) for l in eql['swing_points'])
            
            # Bu EQL'dan sonraki mumları kontrol et
            subsequent_candles = candles.iloc[last_swing_index + 1:]
            
            for i, (idx, candle) in enumerate(subsequent_candles.iterrows()):
                candle_low = float(candle['low'])
                candle_close = float(candle['close'])
                
                # SSL (Sell Side Liquidity) Sweep - EQL'ın altına inme
                if candle_low < level * 0.999:  # 0.1% buffer
                    liquidity_event = {
                        'event_type': 'SSL_SWEEP',  # Sell Side Liquidity Sweep
                        'level_type': 'EQL',
                        'swept_level': level,
                        'sweep_candle_index': last_swing_index + 1 + i,
                        'sweep_price': candle_low,
                        'sweep_timestamp': candle.get('timestamp', datetime.now()),
                        'sweep_strength': 'STRONG' if candle_close < level else 'WEAK',
                        'eql_data': eql,
                        'candle_data': candle.to_dict(),
                        'potential_reversal_direction': 'BULLISH'  # SSL sweep sonrası potansiyel yön
                    }
                    liquidity_events.append(liquidity_event)
                    
                    # EQL'ı swept olarak işaretle
                    eql['swept'] = True
                    eql['sweep_candle_index'] = last_swing_index + 1 + i
                    
                    logger.info(f"🌊 SSL SWEEP (EQL): @ {candle_low:.6f} "
                               f"(Level: {level:.6f}) - Potansiyel BULLISH reversal")
                    break
        
        return liquidity_events

    # Sinyal üretme fonksiyonları kaldırıldı - ICT mantığına göre
    # Bu modül artık sadece likidite olaylarını raporlar, sinyal üretmez

    def _score_equal_levels(self, equal_levels: List[Dict[str, Any]], 
                           level_type: str) -> List[Dict[str, Any]]:
        """
        Equal levels'ları kaliteye göre puanlar.
        """
        scored_levels = []
        
        for level in equal_levels:
            score = 5.0  # Base score
            
            # Count factor (daha fazla equal point = daha güçlü)
            count = level.get('count', 0)
            if count >= 4:
                score += 3.0
            elif count >= 3:
                score += 2.0
            elif count >= 2:
                score += 1.0
            
            # Time span factor (longer span = more significant)
            time_span = level.get('time_span_hours', 0)
            if time_span:
                if time_span >= 72:  # 3+ gün
                    score += 2.0
                elif time_span >= 24:  # 1+ gün
                    score += 1.0
            
            # Broken status (unbroken levels more valuable)
            if not level.get('broken', False):
                score += 1.5
            
            # Recent level (recency bonus)
            created_at = level.get('created_at')
            if created_at:
                # Son 24 saatte oluşturulmuşsa bonus
                try:
                    created_time = datetime.fromisoformat(created_at.replace('Z', '+00:00'))
                    hours_ago = (datetime.now() - created_time).total_seconds() / 3600
                    if hours_ago <= 24:
                        score += 1.0
                except:
                    pass
            
            level_copy = level.copy()
            level_copy['quality_score'] = max(0, min(10, score))  # 0-10 normalize
            scored_levels.append(level_copy)
        
        # Score'a göre sırala
        scored_levels.sort(key=lambda x: x['quality_score'], reverse=True)
        
        return scored_levels

    def _empty_equal_levels_result(self) -> Dict[str, Any]:
        """
        Boş Equal Highs/Lows sonucu döner.
        """
        return {
            'equal_highs': [],
            'equal_lows': [],
            'liquidity_events': [],
            'bsl_sweeps': [],
            'ssl_sweeps': [],
            'summary': {
                'total_eqh': 0,
                'total_eql': 0,
                'high_quality_eqh': 0,
                'high_quality_eql': 0,
                'recent_liquidity_events': 0,
                'bsl_sweep_count': 0,
                'ssl_sweep_count': 0
            },
            'analysis_timestamp': datetime.now().isoformat()
        }
    def _generate_unified_liquidity_signals(self, external_liquidity: Dict[str, Any], 
                                          liqsfp_results: Dict[str, Any], 
                                          external_hunts: List[Dict[str, Any]], 
                                          equal_levels: Dict[str, Any]) -> List[Dict[str, Any]]:
        """
        Unified liquidity signals generation - tüm liquidity analizlerinden sinyaller üretir.
        
        Bu metod scoring_system'den taşınan LIQSFP_REV sinyal oluşturma mantığını içerir.
        
        Args:
            external_liquidity: External liquidity analiz sonuçları
            liqsfp_results: LIQSFP analiz sonuçları
            external_hunts: External hunt events
            equal_levels: Equal highs/lows analizi
            
        Returns:
            Unified liquidity signals list - scoring_system için hazır sinyaller
        """
        unified_signals = []
        
        # 1. LIQSFP Reversal Signals (En yüksek öncelik) - scoring_system'den taşınan mantık
        detected_liqsfp = liqsfp_results.get('detected_liqsfp', [])
        if detected_liqsfp:
            recent_sweeps = [s for s in detected_liqsfp if 'REVERSAL' in s.get('type', '')][-3:]
            
            for sweep in recent_sweeps:
                if sweep.get('idm_confirmed', False) or sweep.get('quality') == 'HIGH':
                    liqsfp_signal = {
                        'type': 'LIQSFP_REV',
                        'symbol': sweep.get('symbol', ''),  # Symbol bilgisini koruyoruz
                        'direction': 'bear' if 'BEARISH' in sweep.get('type') else 'bull',
                        'price': sweep.get('swing_price'),
                        'confidence': 0.9 if sweep.get('idm_confirmed') else 0.8,
                        'reason': 'IDM Confirmed Liquidity Hunt' if sweep.get('idm_confirmed') else 'High Quality Liquidity Sweep',
                        'sweep_data': sweep,
                        'hunt_price': sweep.get('hunt_price', sweep.get('swing_price')),
                        'hunt_strength': sweep.get('hunt_strength', 0),
                        'liquidity_data': sweep.get('liquidity_data', {}),
                        'idm_confirmed': sweep.get('idm_confirmed', False),
                        'priority_level': 7,  # scoring_system'deki öncelik seviyesi
                        'confluence_score': sweep.get('confluence_score', 80),
                        'quality_score': 9 if sweep.get('idm_confirmed') else 8
                    }
                    unified_signals.append(liqsfp_signal)
                    logger.info(f"🥇 LIQSFP_REV Sinyali oluşturuldu: {sweep.get('type')} @ {sweep.get('swing_price')}")
        
        # 2. External Liquidity Hunt Signals
        for hunt in external_hunts:
            if hunt.get('quality_score', 0) >= 7:
                reversal_data = hunt.get('reversal_data', {})
                if reversal_data.get('reversal_confirmed', False):
                    signal = {
                        'type': 'EXTERNAL_LIQUIDITY_HUNT',
                        'direction': 'bear' if hunt.get('hunt_type') == 'BSL' else 'bull',
                        'price': hunt.get('hunt_price'),
                        'confidence': min(0.85, hunt.get('quality_score', 0) / 10),
                        'reason': f"External {hunt.get('hunt_type')} Hunt with Reversal",
                        'quality_score': hunt.get('quality_score', 0),
                        'priority_level': 2,
                        'source_data': hunt
                    }
                    unified_signals.append(signal)
        
        # 3. Equal Highs/Lows Breakout Signals
        for eq_high in equal_levels.get('equal_highs', []):
            if eq_high.get('breakout_confirmed', False):
                signal = {
                    'type': 'EQUAL_HIGHS_BREAKOUT',
                    'direction': 'bull',
                    'price': eq_high.get('level'),
                    'confidence': 0.75,
                    'reason': 'Equal Highs Breakout',
                    'quality_score': 7,
                    'priority_level': 3,
                    'source_data': eq_high
                }
                unified_signals.append(signal)
        
        for eq_low in equal_levels.get('equal_lows', []):
            if eq_low.get('breakout_confirmed', False):
                signal = {
                    'type': 'EQUAL_LOWS_BREAKOUT',
                    'direction': 'bear',
                    'price': eq_low.get('level'),
                    'confidence': 0.75,
                    'reason': 'Equal Lows Breakout',
                    'quality_score': 7,
                    'priority_level': 3,
                    'source_data': eq_low
                }
                unified_signals.append(signal)
        
        # Priority'ye göre sırala
        unified_signals.sort(key=lambda x: x.get('priority_level', 999))
        
        return unified_signals
        
    def _check_idm_confirmation(self, candle: pd.Series, target_price: float, direction: str) -> bool:
        """
        IDM (Institutional Dollar Millionaire) confirmation kontrolü yapar.
        
        Args:
            candle: Kontrol edilecek mum
            target_price: Hedef fiyat
            direction: Yön ('BULLISH' veya 'BEARISH')
            
        Returns:
            IDM confirmation durumu
        """
        try:
            # Basit IDM confirmation - volume ve price action bazlı
            volume = candle.get('volume', 0)
            close_price = float(candle['close'])
            
            # Volume threshold (ortalama volume'un 1.5 katı)
            volume_threshold = volume > 0  # Basit kontrol
            
            # Price action confirmation
            if direction == 'BEARISH':
                # Bearish IDM: Hunt sonrası güçlü aşağı kapanış
                price_confirmation = close_price < target_price * 0.995  # %0.5 altında kapanış
            else:  # BULLISH
                # Bullish IDM: Hunt sonrası güçlü yukarı kapanış
                price_confirmation = close_price > target_price * 1.005  # %0.5 üstünde kapanış
            
            return volume_threshold and price_confirmation
            
        except Exception as e:
            logger.warning(f"IDM confirmation kontrolü hatası: {e}")
            return False

    def _is_near_round_number(self, price: float) -> bool:
        """
        Fiyatın round number'a yakın olup olmadığını kontrol eder.
        
        Args:
            price: Kontrol edilecek fiyat
            
        Returns:
            Round number yakınlık durumu
        """
        try:
            # Basit round number kontrolü
            # 0.1, 0.5, 1.0, 5.0, 10.0 gibi seviyelere yakınlık
            round_levels = [0.1, 0.5, 1.0, 5.0, 10.0, 50.0, 100.0, 500.0, 1000.0]
            
            for level in round_levels:
                if abs(price - level) / level < 0.02:  # %2 tolerans
                    return True
            
            # Decimal places kontrolü
            price_str = f"{price:.6f}"
            if price_str.endswith('0000') or price_str.endswith('5000'):
                return True
                
            return False
            
        except Exception as e:
            logger.warning(f"Round number kontrolü hatası: {e}")
            return False