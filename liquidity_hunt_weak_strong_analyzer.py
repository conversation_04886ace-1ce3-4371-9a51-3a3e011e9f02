# liquidity_hunt_weak_strong_analyzer.py

from typing import Dict, Any, List, Optional
import pandas as pd
from loguru import logger

class LiquidityHuntWeakStrongAnalyzer:
    """
    Likidite Avı + Zayıf/Güçlü Swing Konfluansı analiz sınıfı.
    
    Bu ICT stratejisi Smart Money'nin likidite avcılığı davranışını takip eder:
    1. <PERSON><PERSON><PERSON>f swing'lerdeki likiditeyi tespit et
    2. Bu likiditenin avlanmasını (sweep) bekle
    3. Ana trend yönünde devam sinyali ile giriş yap
    4. Güçlü swing'leri SL referansı olarak kullan
    """
    
    def __init__(self):
        """Likidite Avı + Zayıf/Güçlü Swing Analizörünü başlatır."""
        logger.info("Likidite Avı + Zayıf/Güçlü Swing Analizörü başlatıldı")
        
        # Likidite avı parametreleri
        self.max_hunt_age_candles = 5  # Son 5 mum içinde hunt olmalı
        self.min_hunt_strength = 6     # Minimum hunt gücü (1-10)
        self.min_hunt_completion = 70  # Minimum %70 tamamlanma
        
        # Swing analiz parametreleri
        self.min_swing_range_pct = 1.0  # Swing'ler arası minimum %1 fark
        self.max_swing_age_candles = 50 # Swing'lerin maksimum yaşı
        
        # Confluence parametreleri
        self.min_confluence_score = 70  # Minimum %70 confluence skoru
        self.trend_strength_threshold = 0.6  # Minimum trend gücü
    
    def analyze(self, symbol: str, all_symbol_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Likidite avı + zayıf/güçlü swing konfluansı için tam analiz yapar.
        
        Args:
            symbol: Sembol adı
            all_symbol_data: Tüm analiz verilerini içeren dict
            
        Returns:
            Dict: Likidite avı + swing confluence analiz sonuçları
        """
        logger.info(f"[{symbol}] Likidite Avı + Zayıf/Güçlü Swing analizi başlatılıyor...")
        
        candles = all_symbol_data.get('candles')
        if candles is None or candles.empty:
            logger.error(f"[{symbol}] Mum verileri bulunamadı")
            return {'signals': [], 'error': 'candles_missing'}
        
        current_price = float(candles.iloc[-1]['close'])
        
        # 1. Mevcut piyasa yapısını analiz et
        market_structure = self._analyze_market_structure(all_symbol_data)
        if not market_structure['has_clear_trend']:
            logger.info(f"[{symbol}] Net trend bulunamadı, analiz durduruluyor")
            return {'signals': [], 'reason': 'no_clear_trend'}
        
        # 2. Zayıf/Güçlü swing'leri tespit et
        swing_analysis = self._analyze_weak_strong_swings(all_symbol_data, market_structure)
        if not swing_analysis['valid_setup']:
            logger.info(f"[{symbol}] Geçerli zayıf/güçlü swing kurulumu bulunamadı")
            return {'signals': [], 'reason': 'no_valid_swing_setup'}
        
        # 3. Likidite avı tespiti
        liquidity_hunts = self._detect_liquidity_hunts(all_symbol_data, swing_analysis)
        if not liquidity_hunts:
            logger.info(f"[{symbol}] Yakın zamanda likidite avı tespit edilemedi")
            return {'signals': [], 'reason': 'no_recent_liquidity_hunt'}
        
        # 4. Confluence sinyallerini oluştur
        confluence_signals = self._generate_confluence_signals(
            liquidity_hunts, swing_analysis, market_structure, current_price
        )
        
        # 5. Sinyalleri skorla ve filtrele
        final_signals = self._score_and_filter_signals(confluence_signals, current_price)
        
        logger.success(f"[{symbol}] Likidite avı + swing analizi tamamlandı: {len(final_signals)} sinyal")
        
        return {
            'signals': final_signals,
            'market_structure': market_structure,
            'swing_analysis': swing_analysis,
            'liquidity_hunts': liquidity_hunts,
            'analysis_timestamp': pd.Timestamp.now(),
            'confluence_signals_raw': confluence_signals
        }
    
    def _analyze_market_structure(self, all_symbol_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Piyasa yapısını analiz ederek trend yönü ve gücünü belirler.
        """
        structure_analysis = all_symbol_data.get('main_tf_structure', {})
        current_trend = structure_analysis.get('current_trend', 'sideways')
        
        # Trend gücünü hesapla (major pivotlar arası momentum)
        major_pivots = structure_analysis.get('major_pivots', [])
        trend_strength = self._calculate_trend_strength(major_pivots, current_trend)
        
        has_clear_trend = (
            current_trend in ['bullish', 'bearish'] and 
            trend_strength >= self.trend_strength_threshold
        )
        
        return {
            'current_trend': current_trend,
            'trend_strength': trend_strength,
            'has_clear_trend': has_clear_trend,
            'major_pivots_count': len(major_pivots),
            'trend_direction': current_trend.upper() if current_trend != 'sideways' else 'NEUTRAL'
        }
    
    def _calculate_trend_strength(self, major_pivots: List[Dict[str, Any]], 
                                 current_trend: str) -> float:
        """
        Major pivotlara göre trend gücünü hesaplar (0.0-1.0).
        """
        if len(major_pivots) < 4:
            return 0.5  # Yetersiz veri
        
        try:
            # Son 4 major pivot'ı al
            recent_pivots = major_pivots[-4:]
            
            # Trend yönünde kaç pivot var?
            consistent_moves = 0
            total_moves = len(recent_pivots) - 1
            
            for i in range(1, len(recent_pivots)):
                prev_pivot = recent_pivots[i-1]
                curr_pivot = recent_pivots[i]
                
                prev_price = prev_pivot.get('price', 0)
                curr_price = curr_pivot.get('price', 0)
                
                if current_trend == 'bullish':
                    # Bullish trendde higher highs ve higher lows arayalım
                    if curr_price > prev_price:
                        consistent_moves += 1
                elif current_trend == 'bearish':
                    # Bearish trendde lower highs ve lower lows arayalım
                    if curr_price < prev_price:
                        consistent_moves += 1
            
            trend_strength = consistent_moves / total_moves if total_moves > 0 else 0.5
            return min(1.0, max(0.0, trend_strength))
            
        except Exception as e:
            logger.error(f"Trend gücü hesaplama hatası: {e}")
            return 0.5
    
    def _analyze_weak_strong_swings(self, all_symbol_data: Dict[str, Any], 
                                   market_structure: Dict[str, Any]) -> Dict[str, Any]:
        """
        Zayıf ve güçlü swing'leri analiz ederek geçerli kurulumları tespit eder.
        """
        ws_analysis = all_symbol_data.get('weak_strong_swings', {})
        labeled_swings = ws_analysis.get('labeled_swings', [])
        
        if len(labeled_swings) < 2:
            return {'valid_setup': False, 'reason': 'insufficient_swings'}
        
        current_trend = market_structure['current_trend']
        
        # Trend yönüne göre aranacak pattern'i belirle
        if current_trend == 'bullish':
            # Bullish trend: Strong Low + Weak High kombinasyonu ara
            target_pattern = self._find_strong_low_weak_high_pattern(labeled_swings)
        elif current_trend == 'bearish':
            # Bearish trend: Strong High + Weak Low kombinasyonu ara
            target_pattern = self._find_strong_high_weak_low_pattern(labeled_swings)
        else:
            return {'valid_setup': False, 'reason': 'no_trend'}
        
        if not target_pattern['found']:
            return {'valid_setup': False, 'reason': 'no_valid_pattern'}
        
        return {
            'valid_setup': True,
            'pattern_type': target_pattern['pattern_type'],
            'target_swing': target_pattern['target_swing'],    # Zayıf swing (hedef)
            'reference_swing': target_pattern['reference_swing'],  # Güçlü swing (referans)
            'swing_sequence': target_pattern['swing_sequence'],
            'pattern_strength': target_pattern['strength'],
            'next_weak_target': self._find_next_weak_target(labeled_swings, target_pattern)
        }
    
    def _find_strong_low_weak_high_pattern(self, labeled_swings: List[Dict[str, Any]]) -> Dict[str, Any]:
        """
        Bullish trend için Strong Low + Weak High pattern'ini arar.
        """
        strong_lows = [s for s in labeled_swings if s.get('type') == 'low' and s.get('strength_type') == 'STRONG']
        weak_highs = [s for s in labeled_swings if s.get('type') == 'high' and s.get('strength_type') == 'WEAK']
        
        if not strong_lows or not weak_highs:
            return {'found': False, 'reason': 'missing_required_swings'}
        
        # En yakın Strong Low ve Weak High'ı bul
        latest_strong_low = max(strong_lows, key=lambda x: x.get('index', 0))
        latest_weak_high = max(weak_highs, key=lambda x: x.get('index', 0))
        
        # Sequence kontrolü: Strong Low, Weak High sırası olmalı
        if latest_strong_low.get('index', 0) >= latest_weak_high.get('index', 0):
            return {'found': False, 'reason': 'incorrect_sequence'}
        
        # Fiyat aralığı kontrolü
        low_price = latest_strong_low.get('price', 0)
        high_price = latest_weak_high.get('price', 0)
        
        if high_price <= low_price:
            return {'found': False, 'reason': 'invalid_price_range'}
        
        range_pct = ((high_price - low_price) / low_price) * 100
        if range_pct < self.min_swing_range_pct:
            return {'found': False, 'reason': 'insufficient_range'}
        
        pattern_strength = self._calculate_pattern_strength(latest_strong_low, latest_weak_high)
        
        return {
            'found': True,
            'pattern_type': 'STRONG_LOW_WEAK_HIGH',
            'target_swing': latest_weak_high,      # Hedef (avlanacak likidite)
            'reference_swing': latest_strong_low,  # Referans (SL için)
            'swing_sequence': [latest_strong_low, latest_weak_high],
            'strength': pattern_strength,
            'range_pct': range_pct
        }
    
    def _find_strong_high_weak_low_pattern(self, labeled_swings: List[Dict[str, Any]]) -> Dict[str, Any]:
        """
        Bearish trend için Strong High + Weak Low pattern'ini arar.
        """
        strong_highs = [s for s in labeled_swings if s.get('type') == 'high' and s.get('strength_type') == 'STRONG']
        weak_lows = [s for s in labeled_swings if s.get('type') == 'low' and s.get('strength_type') == 'WEAK']
        
        if not strong_highs or not weak_lows:
            return {'found': False, 'reason': 'missing_required_swings'}
        
        # En yakın Strong High ve Weak Low'u bul
        latest_strong_high = max(strong_highs, key=lambda x: x.get('index', 0))
        latest_weak_low = max(weak_lows, key=lambda x: x.get('index', 0))
        
        # Sequence kontrolü: Strong High, Weak Low sırası olmalı
        if latest_strong_high.get('index', 0) >= latest_weak_low.get('index', 0):
            return {'found': False, 'reason': 'incorrect_sequence'}
        
        # Fiyat aralığı kontrolü
        high_price = latest_strong_high.get('price', 0)
        low_price = latest_weak_low.get('price', 0)
        
        if low_price >= high_price:
            return {'found': False, 'reason': 'invalid_price_range'}
        
        range_pct = ((high_price - low_price) / high_price) * 100
        if range_pct < self.min_swing_range_pct:
            return {'found': False, 'reason': 'insufficient_range'}
        
        pattern_strength = self._calculate_pattern_strength(latest_strong_high, latest_weak_low)
        
        return {
            'found': True,
            'pattern_type': 'STRONG_HIGH_WEAK_LOW',
            'target_swing': latest_weak_low,       # Hedef (avlanacak likidite)
            'reference_swing': latest_strong_high, # Referans (SL için)
            'swing_sequence': [latest_strong_high, latest_weak_low],
            'strength': pattern_strength,
            'range_pct': range_pct
        }
    
    def _calculate_pattern_strength(self, reference_swing: Dict[str, Any], 
                                   target_swing: Dict[str, Any]) -> float:
        """
        Pattern gücünü hesaplar (0.0-1.0).
        """
        strength = 0.5  # Base strength
        
        # Reference swing gücü
        ref_strength_score = reference_swing.get('strength_score', 5)  # 1-10
        strength += (ref_strength_score / 10) * 0.3
        
        # Target swing zayıflığı (zayıf olması +puan)
        target_weakness_score = 10 - target_swing.get('strength_score', 5)  # Reverse
        strength += (target_weakness_score / 10) * 0.2
        
        return min(1.0, max(0.0, strength))
    
    def _find_next_weak_target(self, labeled_swings: List[Dict[str, Any]], 
                              current_pattern: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """
        Sonraki zayıf swing hedefini bulur (TP için).
        """
        pattern_type = current_pattern['pattern_type']
        target_swing = current_pattern['target_swing']
        target_index = target_swing.get('index', 0)
        
        # Hedef swing'den önceki zayıf swing'leri ara
        if pattern_type == 'STRONG_LOW_WEAK_HIGH':
            # Bullish: Önceki weak high'ları ara
            earlier_weak_highs = [
                s for s in labeled_swings 
                if (s.get('type') == 'high' and 
                    s.get('strength_type') == 'WEAK' and 
                    s.get('index', 999) < target_index and
                    s.get('price', 0) > target_swing.get('price', 0))
            ]
            if earlier_weak_highs:
                return max(earlier_weak_highs, key=lambda x: x.get('price', 0))
        
        else:  # STRONG_HIGH_WEAK_LOW
            # Bearish: Önceki weak low'ları ara
            earlier_weak_lows = [
                s for s in labeled_swings 
                if (s.get('type') == 'low' and 
                    s.get('strength_type') == 'WEAK' and 
                    s.get('index', 999) < target_index and
                    s.get('price', 0) < target_swing.get('price', 0))
            ]
            if earlier_weak_lows:
                return min(earlier_weak_lows, key=lambda x: x.get('price', 0))
        
        return None
    
    def _detect_liquidity_hunts(self, all_symbol_data: Dict[str, Any], 
                               swing_analysis: Dict[str, Any]) -> List[Dict[str, Any]]:
        """
        Zayıf swing'lerdeki likidite avlarını tespit eder.
        """
        liquidity_analysis = all_symbol_data.get('liquidity_analysis', {})
        
        # Geleneksel LIQSFP analizi varsa kullan
        liqsfp_signals = liquidity_analysis.get('liqsfp_signals', [])
        
        # External liquidity'den BSL/SSL sweep'lerini al
        external_liquidity = liquidity_analysis.get('external_liquidity', {})
        bsl_sweeps = external_liquidity.get('bsl_sweeps', [])
        ssl_sweeps = external_liquidity.get('ssl_sweeps', [])
        
        all_sweeps = bsl_sweeps + ssl_sweeps
        
        # Swing pattern'ine uygun hunt'ları filtrele
        target_swing = swing_analysis['target_swing']
        pattern_type = swing_analysis['pattern_type']
        
        relevant_hunts = []
        
        for sweep in all_sweeps:
            hunt_info = self._analyze_sweep_relevance(sweep, target_swing, pattern_type)
            if hunt_info['is_relevant']:
                relevant_hunts.append(hunt_info)
        
        # LIQSFP sinyallerini de kontrol et
        for liqsfp in liqsfp_signals:
            hunt_info = self._analyze_liqsfp_relevance(liqsfp, target_swing, pattern_type)
            if hunt_info['is_relevant']:
                relevant_hunts.append(hunt_info)
        
        # Son 5 mum içindeki hunt'ları filtrele
        recent_hunts = [
            hunt for hunt in relevant_hunts 
            if hunt.get('candles_ago', 999) <= self.max_hunt_age_candles
        ]
        
        # Güçlü hunt'ları seç
        strong_hunts = [
            hunt for hunt in recent_hunts 
            if hunt.get('strength', 0) >= self.min_hunt_strength and
               hunt.get('completion_percentage', 0) >= self.min_hunt_completion
        ]
        
        return strong_hunts
    
    def _analyze_sweep_relevance(self, sweep: Dict[str, Any], target_swing: Dict[str, Any], 
                                pattern_type: str) -> Dict[str, Any]:
        """
        Sweep'in hedef swing ile ilgili olup olmadığını analiz eder.
        """
        sweep_price = sweep.get('swept_price', 0)
        sweep_type = sweep.get('type', '')  # 'BSL_SWEEP', 'SSL_SWEEP'
        target_price = target_swing.get('price', 0)
        
        # Fiyat yakınlığı kontrolü (%1 tolerance)
        price_diff_pct = abs(sweep_price - target_price) / target_price * 100
        is_price_close = price_diff_pct <= 1.0
        
        # Sweep direction kontrolü
        is_direction_match = False
        if pattern_type == 'STRONG_LOW_WEAK_HIGH':
            # Bullish pattern: BSL sweep bekliyoruz (yukarı hareket)
            is_direction_match = sweep_type == 'BSL_SWEEP'
        elif pattern_type == 'STRONG_HIGH_WEAK_LOW':
            # Bearish pattern: SSL sweep bekliyoruz (aşağı hareket)
            is_direction_match = sweep_type == 'SSL_SWEEP'
        
        is_relevant = is_price_close and is_direction_match
        
        return {
            'is_relevant': is_relevant,
            'hunt_type': sweep_type,
            'hunt_price': sweep_price,
            'strength': sweep.get('strength', 5),
            'completion_percentage': sweep.get('completion_percentage', 70),
            'candles_ago': sweep.get('candles_ago', 0),
            'price_diff_pct': price_diff_pct,
            'raw_data': sweep
        }
    
    def _analyze_liqsfp_relevance(self, liqsfp: Dict[str, Any], target_swing: Dict[str, Any], 
                                 pattern_type: str) -> Dict[str, Any]:
        """
        LIQSFP sinyalinin hedef swing ile ilgili olup olmadığını analiz eder.
        """
        liqsfp_price = liqsfp.get('stop_hunt_price', 0)
        liqsfp_direction = liqsfp.get('direction', '').upper()
        target_price = target_swing.get('price', 0)
        
        # Fiyat yakınlığı kontrolü
        price_diff_pct = abs(liqsfp_price - target_price) / target_price * 100
        is_price_close = price_diff_pct <= 1.5  # LIQSFP için biraz daha esnek
        
        # Direction kontrolü
        is_direction_match = False
        if pattern_type == 'STRONG_LOW_WEAK_HIGH':
            # Bullish pattern: Bullish LIQSFP bekliyoruz
            is_direction_match = liqsfp_direction == 'BULLISH'
        elif pattern_type == 'STRONG_HIGH_WEAK_LOW':
            # Bearish pattern: Bearish LIQSFP bekliyoruz
            is_direction_match = liqsfp_direction == 'BEARISH'
        
        is_relevant = is_price_close and is_direction_match
        
        hunt_type = 'BSL_SWEEP' if liqsfp_direction == 'BULLISH' else 'SSL_SWEEP'
        
        return {
            'is_relevant': is_relevant,
            'hunt_type': hunt_type,
            'hunt_price': liqsfp_price,
            'strength': liqsfp.get('strength', 7),  # LIQSFP genelde güçlü
            'completion_percentage': liqsfp.get('completion_percentage', 85),
            'candles_ago': liqsfp.get('candles_ago', 0),
            'price_diff_pct': price_diff_pct,
            'raw_data': liqsfp
        }
    
    def _generate_confluence_signals(self, liquidity_hunts: List[Dict[str, Any]],
                                    swing_analysis: Dict[str, Any], 
                                    market_structure: Dict[str, Any],
                                    current_price: float) -> List[Dict[str, Any]]:
        """
        Likidite avı + swing confluence sinyallerini oluşturur.
        """
        signals = []
        
        for hunt in liquidity_hunts:
            signal = {
                'type': 'LIQUIDITY_HUNT_WEAK_STRONG',
                'pattern': 'LIQUIDITY_HUNT_WEAK_STRONG',
                'direction': market_structure['trend_direction'],
                'liquidity_hunt_data': hunt,
                'weak_strong_data': swing_analysis,
                'market_structure_data': market_structure,
                'current_price': current_price,
                'confluence_score': 0.0,  # Hesaplanacak
                'entry_method': 'liquidity_hunt_weak_strong'
            }
            
            signals.append(signal)
        
        return signals
    
    def _score_and_filter_signals(self, confluence_signals: List[Dict[str, Any]], 
                                 current_price: float) -> List[Dict[str, Any]]:
        """
        Confluence sinyallerini skorlar ve filtreler.
        """
        if not confluence_signals:
            return []
        
        # Her sinyal için confluence score hesapla
        for signal in confluence_signals:
            confluence_score = self._calculate_signal_confluence_score(signal)
            signal['confluence_score'] = confluence_score
        
        # Minimum confluence score filtresi
        valid_signals = [
            signal for signal in confluence_signals 
            if signal.get('confluence_score', 0) >= self.min_confluence_score
        ]
        
        # Confluence score'a göre sırala
        valid_signals.sort(key=lambda x: x.get('confluence_score', 0), reverse=True)
        
        # En iyi 2 sinyali döndür
        return valid_signals[:2]
    
    def _calculate_signal_confluence_score(self, signal: Dict[str, Any]) -> float:
        """
        Tek bir sinyal için confluence skorunu hesaplar (0-100).
        """
        hunt_data = signal.get('liquidity_hunt_data', {})
        swing_data = signal.get('weak_strong_data', {})
        market_data = signal.get('market_structure_data', {})
        
        score = 0.0
        
        # Likidite avı kalitesi (35%)
        hunt_strength = hunt_data.get('strength', 5)
        hunt_completion = hunt_data.get('completion_percentage', 50)
        hunt_score = (hunt_strength / 10 * 70) + (hunt_completion / 100 * 30)
        score += (hunt_score / 100) * 35
        
        # Swing pattern kalitesi (35%)
        pattern_strength = swing_data.get('pattern_strength', 0.5)
        score += pattern_strength * 35
        
        # Trend gücü (20%)
        trend_strength = market_data.get('trend_strength', 0.5)
        score += trend_strength * 20
        
        # Timing bonus (10%)
        hunt_recency = hunt_data.get('candles_ago', 5)
        timing_bonus = max(0, 10 - hunt_recency * 2)
        score += timing_bonus
        
        return min(100.0, max(0.0, score))
