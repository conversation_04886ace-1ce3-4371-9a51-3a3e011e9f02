import os
import pandas as pd
from datetime import datetime, timedelta
from bybit_client import BybitClient
from pivot_analyzer import PivotAnalyzer
from fvrp_analyzer import FVRPAnalyzer
from loguru import logger
from typing import Dict, List, Optional, Tuple
from utils import format_price_standard

# Logs ve data klasörlerini oluştur
os.makedirs("logs", exist_ok=True)
os.makedirs("data", exist_ok=True)

# <PERSON><PERSON><PERSON> kullanıyoruz, ek yapılandırma gerekli değil

# FVRP analizci örneği oluştur
fvrp_analyzer = FVRPAnalyzer()

class NPOCAnalyzer:
    """
    Naked POC (nPOC) analizi için sınıf. 
    Bu sınıf, CSV dosyasından POC verilerini çeker,
    bu POC'ların "naked" olup olmadığını kontrol eder ve sonuçları
    npoc_summary.log dosyasına özetler.
    """
    
    def __init__(self, resolution: int = 30, lookback_periods: int = 5, client: Optional['BybitClient'] = None):
        """NPOCAnalyzer için yapılandırma"""
        self.resolution = resolution
        self.lookback_periods = lookback_periods
        # Analiz sonuçlarını saklamak için sözlük (symbol -> timeframe -> pocs listesi)
        self.latest_npoc_data: Dict[str, Dict[str, List[Dict]]] = {}
        logger.info("NPOC Analyzer başlatıldı.")
        # Naked POC'ları saklamak için sözlük
        self.naked_pocs = {}
        # Son analiz zamanı
        self.last_analysis_time = {}
        # Son tam analiz zamanı
        self.last_full_analysis_time = None

        # Dependency Injection: Mevcut client varsa kullan, yoksa yeni oluştur
        if client is not None:
            self.client = client
            logger.info("[NPOC Init] ✅ Mevcut BybitClient instance'ı kullanılıyor (Dependency Injection)")
        else:
            self.client = BybitClient()
            logger.warning("[NPOC Init] ⚠️  Yeni BybitClient oluşturuldu (Dependency Injection önerilir)")

        # Pivot analizci
        self.analyzer = PivotAnalyzer()
        # Maksimum incelenecek POC sayısı (her sembol için)
        self.max_pocs_per_symbol = 12
        # Özet dosyası için rotasyon sayacı
        self.summary_counter = 0
    
    def load_historical_pocs(self):
        """
        CSV dosyasından POC verilerini yükler.
        
        Returns:
            bool: Başarılı yükleme durumu
        """
        try:
            # CSV dosyasının varlığını kontrol et
            csv_path = "data/fvrp_data.csv"
            if not os.path.exists(csv_path):
                logger.error(f"❌ [NPOC-KRİTİK-HATA] FVRP veri dosyası bulunamadı: {csv_path}")
                logger.error("📋 Bu hata nedeniyle Naked POC analizi yapılamayacak!")
                logger.error("🔧 Çözüm: main.py'de analyze_historical_fvrp() fonksiyonunun başarıyla çalıştığından emin olun.")

                # Klasör içeriğini kontrol et
                data_dir = "data"
                if os.path.exists(data_dir):
                    files = os.listdir(data_dir)
                    logger.error(f"📁 'data' klasöründeki mevcut dosyalar: {files}")
                    if not files:
                        logger.error("📁 'data' klasörü boş! FVRP analizi hiç çalışmamış olabilir.")
                else:
                    logger.error(f"📁 'data' klasörü mevcut değil! Klasör oluşturma hatası olabilir.")

                logger.error("⚠️  Naked POC puanlaması devre dışı kalacak - bu önemli bir teyit mekanizmasını kaybetmenize neden olur!")
                return False
            
            # CSV dosyasını oku
            df = pd.read_csv(csv_path)
            
            # Veri varsa devam et
            if df.empty:
                logger.error("[NPOC-SİSTEM] FVRP veri dosyası boş!")
                return False
            
            # Tarih sütununu datetime formatına dönüştür
            df['date'] = pd.to_datetime(df['date']).dt.date
            
            # Veriyi sembol ve timeframe'e göre grupla
            for (symbol, timeframe), group in df.groupby(['symbol', 'timeframe']):
                key = f"{symbol}_{timeframe}"
                
                # Her kayıt için POC nesnesi oluştur
                poc_data = []
                for _, row in group.iterrows():
                    # POC değeri geçerliyse ekle
                    if not pd.isna(row['poc']):
                        poc_record = {
                            "date": row['date'],
                            "poc": row['poc'],
                            "is_naked": True,  # Başlangıçta tüm POC'lar naked kabul edilir
                            "last_checked": datetime.now()
                        }
                        poc_data.append(poc_record)
                
                # Tarihe göre sırala (en yeniden en eskiye)
                poc_data = sorted(poc_data, key=lambda x: x['date'], reverse=True)
                
                # Sadece en son 7 POC'u al (veya daha az varsa tümünü)
                poc_data = poc_data[:self.max_pocs_per_symbol]
                
                # Grubu naked_pocs sözlüğüne ekle
                if poc_data:
                    self.naked_pocs[key] = poc_data
            
            logger.info(f"[NPOC-SİSTEM] CSV dosyasından {len(df)} FVRP kaydı başarıyla yüklendi")
            return len(self.naked_pocs) > 0
        
        except Exception as e:
            logger.error(f"[NPOC-SİSTEM] Tarihsel POC'lar yüklenirken hata: {str(e)}")
            import traceback
            logger.error(f"[NPOC-SİSTEM] Hata detayları: {traceback.format_exc()}")
            return False
    
    def check_naked_pocs(self, symbol, timeframe):
        """
        Belirli bir sembol ve zaman dilimi için naked POC'ları günlük bazda kontrol eder
        
        Args:
            symbol (str): Sembol (örn. "BTCUSDT")
            timeframe (str): Zaman dilimi (örn. "240")
            
        Returns:
            list: Naked POC listesi
        """
        key = f"{symbol}_{timeframe}"
        
        # Naked POC'lar var mı kontrol et
        if key not in self.naked_pocs or not self.naked_pocs[key]:
            logger.info(f"[NPOC-KONTROL] {symbol}/{timeframe}: Naked POC bulunamadı")
            return []
        
        # Analiz yapmak için günlük verileri çek (D=1 gün)
        try:
            # Günlük verileri çek - son 30 gün
            daily_candles = self.client.fetch_klines(symbol, "D", limit=30)
            
            if daily_candles is None or len(daily_candles) < 5:
                logger.warning(f"[NPOC-UYARI] {symbol} için yeterli günlük veri bulunamadı")
                return []
            
            # Mevcut fiyat bilgilerini al
            current_price = daily_candles['close'].iloc[-1]
            
            # Fiyat bilgilerini logla
            logger.info(f"[NPOC-FİYAT] {symbol}/{timeframe}: Mevcut Fiyat={format_price_standard(current_price)}")
            
            # Naked POC'ları kontrol et
            still_naked_pocs = []
            
            # Arka arkaya POC'ları tek tek kontrol et
            for poc_record in self.naked_pocs[key]:
                # POC bilgilerini al
                poc_price = poc_record["poc"]
                poc_date = poc_record["date"]
                
                # POC'un oluştuğu günden sonraki günlük mumları bul
                post_poc_candles = daily_candles[daily_candles['timestamp'].dt.date > poc_date]
                
                # Eğer POC'tan sonra günlük mum yoksa, hala naked
                if len(post_poc_candles) == 0:
                    logger.info(f"[NPOC-DURUM] {symbol}/{timeframe}: POC={format_price_standard(poc_price)} ({poc_date}) - Sonraki gün yok, naked")
                    still_naked_pocs.append(poc_record)
                    continue
                
                # POC test edilmiş mi kontrol et
                is_tested = False
                
                # Her günlük mumun içinde POC seviyesi var mı kontrol et
                for _, daily_candle in post_poc_candles.iterrows():
                    # POC değeri günlük mumun aralığında mı? (tam değer karşılaştırması)
                    if daily_candle['low'] <= poc_price <= daily_candle['high']:
                        is_tested = True
                        test_date = daily_candle['timestamp'].date()
                        logger.info(f"[NPOC-TEST] {symbol}/{timeframe}: POC={format_price_standard(poc_price)} ({poc_date}) - {test_date} tarihinde test edildi")
                        break
                
                # Eğer test edilmediyse, hala naked
                if not is_tested:
                    logger.info(f"[NPOC-DURUM] {symbol}/{timeframe}: POC={format_price_standard(poc_price)} ({poc_date}) - Test edilmedi, naked")
                    still_naked_pocs.append(poc_record)
            
            # Son analiz zamanını güncelle
            self.last_analysis_time[key] = datetime.now()
            
            # Güncellenmiş naked POC'ları kaydet
            self.naked_pocs[key] = still_naked_pocs
            
            return still_naked_pocs
        
        except Exception as e:
            logger.error(f"[NPOC-HATA] {symbol}/{timeframe} için NPOC kontrolü sırasında hata: {str(e)}")
            import traceback
            logger.error(f"[NPOC-HATA] Hata detayları: {traceback.format_exc()}")
            return []
    
    def analyze_all_symbols(self):
        """
        Tüm semboller ve zaman dilimleri için naked POC analizi yapar
        
        Returns:
            dict: Sembol ve zaman dilimine göre naked POC'ları içeren sözlük
        """
        # Sembolleri ve zaman dilimlerini al
        symbols = os.environ.get("SYMBOLS", "BTCUSDT,ETHUSDT,SOLUSDT").split(",")
        timeframes = ["240"]  # Sadece 4s
        
        # Çok sık analiz yapmayı önle
        now = datetime.now()
        if self.last_full_analysis_time and (now - self.last_full_analysis_time).total_seconds() < 3600:  # 1 saat
            logger.info("[NPOC-SİSTEM] Son analiz 1 saat içinde yapıldı, atlanıyor")
            return
        
        # Tarihsel POC'ları yükle
        if not self.load_historical_pocs():
            logger.error("[NPOC-SİSTEM] Tarihsel POC'lar yüklenemedi")
            return
        
        logger.info("="*50)
        logger.info("[NPOC-BAŞLA] nPOC analizi başlatılıyor")
        
        # Tüm nPOC'ları kontrol et
        all_naked_pocs = {}
        
        # Her sembol ve zaman dilimi için analiz yap
        for symbol in symbols:
            for timeframe in timeframes:
                naked_pocs = self.check_naked_pocs(symbol, timeframe)
                all_naked_pocs[f"{symbol}_{timeframe}"] = naked_pocs
        
        # Son analiz zamanını güncelle
        self.last_full_analysis_time = now
        
        # Özet rapor oluştur
        self._create_summary_report(all_naked_pocs, symbols, timeframes)
        
        # Konsola da özet göster
        total_npocs = sum(len(pocs) for pocs in all_naked_pocs.values())
        logger.info(f"[NPOC-ÖZET] Toplam {total_npocs} naked POC bulundu")
        
        logger.info("--- Son Tespit Edilen Naked POC'lar (En fazla 5) ---")
        found_any_npoc_for_console = False
        for symbol in symbols:
            for timeframe in timeframes:
                key = f"{symbol}_{timeframe}"
                if key in all_naked_pocs and all_naked_pocs[key]:
                    found_any_npoc_for_console = True
                    tf_label = "4s" if timeframe == "240" else timeframe
                    # En yeniden eskiye sıralı (zaten öyle yüklendi)
                    pocs_to_log = all_naked_pocs[key][:5] # En son 5 taneyi al
                    logger.info(f"📌 {symbol:<10} | {tf_label:<3} | ({len(all_naked_pocs[key])} adet):")
                    for npoc in pocs_to_log:
                        date_str = npoc["date"].strftime("%Y-%m-%d")
                        logger.info(f"  → {date_str} → nPOC: {format_price_standard(npoc['poc'])}")
                        
        if not found_any_npoc_for_console:
            logger.info(">> Konsolda gösterilecek Naked POC bulunamadı.")
        logger.info("--------------------------------------------------")
        
        logger.info("[NPOC-BİTİŞ] nPOC analizi tamamlandı (Detaylar: logs/npoc_summary.log)")
        logger.info("="*50)
        
        # Sonuçları sakla
        if symbol not in self.latest_npoc_data:
            self.latest_npoc_data[symbol] = {}
        self.latest_npoc_data[symbol][timeframe] = all_naked_pocs
        
        return all_naked_pocs
    
    def _create_summary_report(self, all_naked_pocs, symbols, timeframes):
        """
        Naked POC analizinin sonuçlarını özet rapor olarak oluşturur
        
        Args:
            all_naked_pocs (dict): Sembol ve zaman dilimine göre naked POC'lar
            symbols (list): Sembol listesi
            timeframes (list): Zaman dilimi listesi
        """
        # Özet rapor dosyasını oluştur - Rotasyon için kontrol ekle
        summary_path = "logs/npoc_summary.log"
        
        # Önceki özet dosyasının boyutunu kontrol et
        try:
            if os.path.exists(summary_path) and os.path.getsize(summary_path) > 5*1024*1024:  # 5MB
                # Dosya çok büyükse, yedekle
                self.summary_counter += 1
                backup_path = f"logs/npoc_summary.{self.summary_counter}.log"
                
                # Eğer 5 yedekten fazla varsa, en eskiyi sil
                if self.summary_counter > 5:
                    old_backup = f"logs/npoc_summary.{self.summary_counter-5}.log"
                    if os.path.exists(old_backup):
                        os.remove(old_backup)
                
                # Mevcut dosyayı yedekle
                if os.path.exists(summary_path):
                    os.rename(summary_path, backup_path)
                    logger.info(f"[NPOC-SİSTEM] Özet dosyası yedeklendi: {backup_path}")
        except Exception as e:
            logger.error(f"[NPOC-SİSTEM] Özet dosyası rotasyonu sırasında hata: {str(e)}")
        
        # Yeni özet dosyasını oluştur
        with open(summary_path, 'w') as f:
            f.write(f"nPOC Analiz Özeti - {datetime.now()}\n")
            f.write("="*50 + "\n\n")
            
            # Toplam nPOC sayısını hesapla
            total_npocs = sum(len(pocs) for pocs in all_naked_pocs.values())
            f.write(f"Toplam Naked POC Sayısı: {total_npocs}\n\n")
            
            if total_npocs == 0:
                f.write("Hiç Naked POC bulunamadı.\n")
            else:
                f.write("Sembol / Zaman Dilimi / Tarih / Naked POC Seviyesi\n")
                f.write("-"*50 + "\n\n")
                
                # Her sembol ve zaman dilimi için nPOC'ları listele
                for symbol in symbols:
                    for timeframe in timeframes:
                        key = f"{symbol}_{timeframe}"
                        if key in all_naked_pocs and all_naked_pocs[key]:
                            # En yeniden en eskiye sırala
                            sorted_pocs = sorted(all_naked_pocs[key], key=lambda x: x["date"], reverse=True)
                            
                            # Sembol başlığı
                            tf_label = "4s" if timeframe == "240" else timeframe
                            f.write(f"{symbol} / {tf_label}\n")
                            
                            # Her nPOC için detay
                            for npoc in sorted_pocs:
                                date_str = npoc["date"].strftime("%Y-%m-%d")
                                f.write(f"  → {date_str} → nPOC: {format_price_standard(npoc['poc'])}\n")
                            
                            f.write("\n")
            
            f.write("="*50 + "\n")
            f.write(f"nPOC Analizi Tamamlandı - {datetime.now()}\n")

    def get_pocs_for_symbol(self, symbol: str) -> Dict[str, List[Dict]]:
        """
        Belirli bir sembol için tüm zaman dilimlerindeki POC listelerini döndürür.

        Returns:
            {'60': [poc1, poc2], '240': [poc3], ...} formatında bir sözlük.
        """
        return self.latest_npoc_data.get(symbol, {})

    def get_pocs_for_symbol_timeframe(self, symbol: str, timeframe: str) -> List[Dict]:
        """
        Belirli bir sembol ve zaman dilimi için POC listesini döndürür.
        (main.py'deki kullanım için daha uygun olabilir)
        """
        return self.latest_npoc_data.get(symbol, {}).get(timeframe, [])

# Doğrudan çalıştırma testi
if __name__ == "__main__":
    analyzer = NPOCAnalyzer()
    analyzer.analyze_all_symbols() 