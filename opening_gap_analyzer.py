# opening_gap_analyzer.py

import pandas as pd
from loguru import logger
from typing import Dict, Any, Optional
from datetime import datetime, timedelta

class OpeningGapAnalyzer:
    """
    ICT konseptlerine göre Ye<PERSON> (NDOG) ve Yeni <PERSON> (NWOG) açılış boşluklarını tespit eder.
    
    ICT Prensipleri:
    - Açılış boşlukları genellikle doldurulur (gap fill)
    - NDOG ve NWOG seviyelerinde önemli destek/direnç oluşur
    - Boşluklar likidite hedefleri olarak işlev görür
    """

    def __init__(self, min_gap_size_pct: float = 0.01):
        """
        OpeningGapAnalyzer'ı başlatır.
        
        Args:
            min_gap_size_pct (float): Minimum gap büyüklüğü yüzdesi (varsayılan %0.01)
        """
        self.min_gap_size_pct = min_gap_size_pct
        logger.info("ICT Opening Gap Analiz <PERSON> başlatıldı.")

    def analyze_gaps(self, daily_candles: pd.DataFrame, weekly_candles: pd.DataFrame = None) -> Dict[str, Any]:
        """
        Verilen günlük ve haftalık mum verilerine göre açılış boşluklarını analiz eder.

        Args:
            daily_candles (pd.DataFrame): Günlük mum verileri ('open', 'close', 'timestamp' sütunları gerekli).
            weekly_candles (pd.DataFrame, optional): Haftalık mum verileri.

        Returns:
            dict: Tespit edilen günlük ve haftalık boşluk bilgileri:
            {
                'daily_gap': {...} veya None,
                'weekly_gap': {...} veya None,
                'gap_analysis': {...}
            }
        """
        try:
            gaps = {
                'daily_gap': None,
                'weekly_gap': None,
                'gap_analysis': {
                    'has_daily_gap': False,
                    'has_weekly_gap': False,
                    'total_gaps': 0
                }
            }

            # Yeni Gün Açılış Boşluğu (NDOG) analizi
            if daily_candles is not None and len(daily_candles) >= 2:
                daily_gap = self._analyze_daily_gap(daily_candles)
                if daily_gap:
                    gaps['daily_gap'] = daily_gap
                    gaps['gap_analysis']['has_daily_gap'] = True
                    gaps['gap_analysis']['total_gaps'] += 1

            # Yeni Hafta Açılış Boşluğu (NWOG) analizi
            if weekly_candles is not None and len(weekly_candles) >= 2:
                weekly_gap = self._analyze_weekly_gap(weekly_candles)
                if weekly_gap:
                    gaps['weekly_gap'] = weekly_gap
                    gaps['gap_analysis']['has_weekly_gap'] = True
                    gaps['gap_analysis']['total_gaps'] += 1

            # Genel gap durumu logla
            total_gaps = gaps['gap_analysis']['total_gaps']
            if total_gaps > 0:
                logger.info(f"ICT Opening Gap Analizi: {total_gaps} adet gap tespit edildi")
            else:
                logger.debug("ICT Opening Gap Analizi: Hiçbir anlamlı gap bulunamadı")

            return gaps

        except Exception as e:
            logger.error(f"Opening gap analizi sırasında hata: {e}", exc_info=True)
            return {
                'daily_gap': None,
                'weekly_gap': None,
                'gap_analysis': {'error': str(e)}
            }

    def _analyze_daily_gap(self, daily_candles: pd.DataFrame) -> Optional[Dict[str, Any]]:
        """
        Günlük açılış boşluğunu (NDOG - New Day Opening Gap) analiz eder.
        
        Args:
            daily_candles: Günlük mum verileri
            
        Returns:
            NDOG bilgileri veya None
        """
        try:
            prev_day = daily_candles.iloc[-2]
            current_day = daily_candles.iloc[-1]
            
            prev_day_close = prev_day['close']
            current_day_open = current_day['open']
            
            # Gap büyüklüğünü hesapla
            gap_size = abs(current_day_open - prev_day_close)
            gap_size_pct = (gap_size / prev_day_close) * 100
            
            # Minimum gap büyüklüğü kontrolü
            if gap_size_pct < self.min_gap_size_pct:
                logger.debug(f"NDOG çok küçük: %{gap_size_pct:.3f} < %{self.min_gap_size_pct}")
                return None
            
            # Gap yönünü belirle
            gap_direction = 'up' if current_day_open > prev_day_close else 'down'
            
            gap_info = {
                'type': 'NDOG',
                'direction': gap_direction,
                'top': max(prev_day_close, current_day_open),
                'bottom': min(prev_day_close, current_day_open),
                'size': gap_size,
                'size_pct': gap_size_pct,
                'prev_close': prev_day_close,
                'current_open': current_day_open,
                'timestamp': current_day['timestamp'],
                'filled': False,  # Başlangıçta doldurulmamış
                'fill_percent': 0.0
            }
            
            # Boşluğun doldurulup doldurulmadığını kontrol et
            self._check_gap_fill(gap_info, current_day)
            
            logger.info(f"ICT NDOG tespit edildi: {gap_direction.upper()} gap "
                       f"({gap_info['bottom']:.4f} - {gap_info['top']:.4f}), "
                       f"Büyüklük: %{gap_size_pct:.2f}")
            
            return gap_info
            
        except Exception as e:
            logger.error(f"NDOG analizi sırasında hata: {e}")
            return None

    def _analyze_weekly_gap(self, weekly_candles: pd.DataFrame) -> Optional[Dict[str, Any]]:
        """
        Haftalık açılış boşluğunu (NWOG - New Week Opening Gap) analiz eder.
        
        Args:
            weekly_candles: Haftalık mum verileri
            
        Returns:
            NWOG bilgileri veya None
        """
        try:
            prev_week = weekly_candles.iloc[-2]
            current_week = weekly_candles.iloc[-1]
            
            prev_week_close = prev_week['close']
            current_week_open = current_week['open']
            
            # Gap büyüklüğünü hesapla
            gap_size = abs(current_week_open - prev_week_close)
            gap_size_pct = (gap_size / prev_week_close) * 100
            
            # Minimum gap büyüklüğü kontrolü (haftalık için daha yüksek tolerans)
            min_weekly_gap = self.min_gap_size_pct * 2  # Haftalık için 2x tolerans
            if gap_size_pct < min_weekly_gap:
                logger.debug(f"NWOG çok küçük: %{gap_size_pct:.3f} < %{min_weekly_gap}")
                return None
            
            # Gap yönünü belirle
            gap_direction = 'up' if current_week_open > prev_week_close else 'down'
            
            gap_info = {
                'type': 'NWOG',
                'direction': gap_direction,
                'top': max(prev_week_close, current_week_open),
                'bottom': min(prev_week_close, current_week_open),
                'size': gap_size,
                'size_pct': gap_size_pct,
                'prev_close': prev_week_close,
                'current_open': current_week_open,
                'timestamp': current_week['timestamp'],
                'filled': False,  # Başlangıçta doldurulmamış
                'fill_percent': 0.0
            }
            
            # Boşluğun doldurulup doldurulmadığını kontrol et
            self._check_gap_fill(gap_info, current_week)
            
            logger.info(f"ICT NWOG tespit edildi: {gap_direction.upper()} gap "
                       f"({gap_info['bottom']:.4f} - {gap_info['top']:.4f}), "
                       f"Büyüklük: %{gap_size_pct:.2f}")
            
            return gap_info
            
        except Exception as e:
            logger.error(f"NWOG analizi sırasında hata: {e}")
            return None

    def _check_gap_fill(self, gap_info: Dict[str, Any], candle: pd.Series) -> None:
        """
        Gap'ın mevcut mum tarafından doldurulup doldurulmadığını kontrol eder.
        
        Args:
            gap_info: Gap bilgilerini içeren dict
            candle: Kontrol edilecek mum verisi
        """
        try:
            gap_top = gap_info['top']
            gap_bottom = gap_info['bottom']
            gap_range = gap_top - gap_bottom
            
            # Mumun low ve high değerleri
            candle_low = candle['low']
            candle_high = candle['high']
            
            # Gap'ın ne kadarının doldurulduğunu hesapla
            if gap_info['direction'] == 'up':
                # Yukarı gap: Fiyat gap'ın altına inmişse doldurulmuş
                if candle_low <= gap_bottom:
                    gap_info['filled'] = True
                    gap_info['fill_percent'] = 100.0
                elif candle_low < gap_top:
                    # Kısmi doldurulma
                    filled_amount = gap_top - candle_low
                    gap_info['fill_percent'] = (filled_amount / gap_range) * 100
            else:
                # Aşağı gap: Fiyat gap'ın üstüne çıkmışsa doldurulmuş
                if candle_high >= gap_top:
                    gap_info['filled'] = True
                    gap_info['fill_percent'] = 100.0
                elif candle_high > gap_bottom:
                    # Kısmi doldurulma
                    filled_amount = candle_high - gap_bottom
                    gap_info['fill_percent'] = (filled_amount / gap_range) * 100
            
            # Doldurulma durumunu logla
            if gap_info['filled']:
                logger.debug(f"ICT {gap_info['type']} tam dolduruldu (%100)")
            elif gap_info['fill_percent'] > 0:
                logger.debug(f"ICT {gap_info['type']} kısmen dolduruldu (%{gap_info['fill_percent']:.1f})")
                
        except Exception as e:
            logger.error(f"Gap doldurulma kontrolünde hata: {e}")

    def get_unfilled_gaps(self, gaps: Dict[str, Any]) -> Dict[str, Any]:
        """
        Henüz doldurulmamış gap'ları döndürür.
        
        Args:
            gaps: analyze_gaps() fonksiyonundan dönen sonuçlar
            
        Returns:
            Doldurulmamış gap'lar
        """
        unfilled = {}
        
        if gaps.get('daily_gap') and not gaps['daily_gap'].get('filled', False):
            unfilled['daily_gap'] = gaps['daily_gap']
            
        if gaps.get('weekly_gap') and not gaps['weekly_gap'].get('filled', False):
            unfilled['weekly_gap'] = gaps['weekly_gap']
            
        return unfilled

    def get_gap_targets(self, gaps: Dict[str, Any]) -> Dict[str, float]:
        """
        Gap'ların hedef seviyelerini döndürür.
        
        Args:
            gaps: Gap analiz sonuçları
            
        Returns:
            Gap hedef seviyeleri
        """
        targets = {}
        
        # Günlük gap hedefleri
        if gaps.get('daily_gap'):
            daily_gap = gaps['daily_gap']
            if daily_gap['direction'] == 'up':
                targets['daily_gap_target'] = daily_gap['bottom']  # Aşağıya dönüş hedefi
            else:
                targets['daily_gap_target'] = daily_gap['top']     # Yukarıya dönüş hedefi
        
        # Haftalık gap hedefleri
        if gaps.get('weekly_gap'):
            weekly_gap = gaps['weekly_gap']
            if weekly_gap['direction'] == 'up':
                targets['weekly_gap_target'] = weekly_gap['bottom']  # Aşağıya dönüş hedefi
            else:
                targets['weekly_gap_target'] = weekly_gap['top']     # Yukarıya dönüş hedefi
        
        return targets

# Test fonksiyonu
if __name__ == '__main__':
    # Örnek test verisi
    sample_daily = pd.DataFrame({
        'timestamp': pd.to_datetime(['2025-01-01', '2025-01-02', '2025-01-03']),
        'open': [100.0, 102.5, 101.8],
        'high': [101.0, 103.0, 102.5],
        'low': [99.5, 102.0, 101.2],
        'close': [100.5, 102.8, 102.1]
    })
    
    sample_weekly = pd.DataFrame({
        'timestamp': pd.to_datetime(['2025-01-01', '2025-01-08']),
        'open': [100.0, 103.0],
        'high': [105.0, 104.0],
        'low': [98.0, 102.5],
        'close': [102.0, 103.5]
    })
    
    analyzer = OpeningGapAnalyzer(min_gap_size_pct=0.5)
    
    # Gap analizi
    gaps = analyzer.analyze_gaps(sample_daily, sample_weekly)
    print(f"Gap Analizi: {gaps}")
    
    # Doldurulmamış gap'lar
    unfilled = analyzer.get_unfilled_gaps(gaps)
    print(f"Doldurulmamış Gap'lar: {unfilled}")
    
    # Gap hedefleri
    targets = analyzer.get_gap_targets(gaps)
    print(f"Gap Hedefleri: {targets}")
