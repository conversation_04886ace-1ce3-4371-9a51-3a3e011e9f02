# ote_confluence_analyzer.py
"""
OTE Confluence Analyzer - OTE + Order Block Kesişimi Analizi
===========================================================

Bu modül, Optimal Trade Entry (OTE) seviyeleri ile Order Block'ların
kesişimini analiz ederek yüksek kaliteli confluence sinyalleri üretir.

ICT OTE Konsepti:
- OTE Zone: %61.8-79% Fibonacci retracement bölgesi
- En iyi giriş noktaları genellikle bu bölgede bulunur
- Order Block ile kesişim confluence kalitesini artırır

Single Responsibility İlkesi:
- Sadece OTE + OB confluence analizinden sorumlu
- fibonacci_analyzer ve order_block_analyzer çıktılarını kullanır
- Temiz, test edilebilir ve yeniden kullanılabilir kod
"""

import pandas as pd
import numpy as np
from typing import Dict, List, Any, Optional, Tuple
from loguru import logger
from datetime import datetime


class OTEConfluenceAnalyzer:
    def _find_ote_poi_confluence(self, ote_zone, order_blocks, fvgs, symbol, tolerance_pips=5):
        """
        OTE bölgesi ile Order Block ve FVG'lerin kesişimini analiz eder.
        Args:
            ote_zone: OTE bölgesi dict
            order_blocks: Order Block listesi
            fvgs: FVG listesi
            symbol: Sembol adı
            tolerance_pips: Yakınlık toleransı
        Returns:
            confluence_points: Kesişim noktaları listesi
        """
        confluence_points = []
        try:
            ote_top = ote_zone.get('top')
            ote_bottom = ote_zone.get('bottom')
            ote_mid = ote_zone.get('mid')
            direction = ote_zone.get('direction')
            # Order Block kesişimleri
            for ob in order_blocks:
                ob_high = ob.get('high')
                ob_low = ob.get('low')
                ob_type = ob.get('type', '').lower()
                # OTE ile OB arasında overlap var mı?
                overlap_top = min(ote_top, ob_high)
                overlap_bottom = max(ote_bottom, ob_low)
                if overlap_top > overlap_bottom:
                    overlap_mid = (overlap_top + overlap_bottom) / 2
                    score = 70
                    # Direction uyumu
                    if (direction == 'bullish' and 'bull' in ob_type) or (direction == 'bearish' and 'bear' in ob_type):
                        score += 10
                    # Fiyat yakınlığı
                    score += max(0, 10 - abs(overlap_mid - ote_mid) / tolerance_pips)
                    confluence_points.append({
                        'intersection_mid': overlap_mid,
                        'intersection_top': overlap_top,
                        'intersection_bottom': overlap_bottom,
                        'score': score,
                        'type': 'order_block',
                        'direction': direction,
                        'order_block': ob,
                        'ote_zone': ote_zone
                    })
            # FVG kesişimleri
            for fvg in fvgs:
                fvg_top = fvg.get('top')
                fvg_bottom = fvg.get('bottom')
                overlap_top = min(ote_top, fvg_top)
                overlap_bottom = max(ote_bottom, fvg_bottom)
                if overlap_top > overlap_bottom:
                    overlap_mid = (overlap_top + overlap_bottom) / 2
                    score = 60
                    score += max(0, 10 - abs(overlap_mid - ote_mid) / tolerance_pips)
                    confluence_points.append({
                        'intersection_mid': overlap_mid,
                        'intersection_top': overlap_top,
                        'intersection_bottom': overlap_bottom,
                        'score': score,
                        'type': 'fvg',
                        'direction': direction,
                        'fvg': fvg,
                        'ote_zone': ote_zone
                    })
            return confluence_points
        except Exception as e:
            logger.error(f"[{symbol}] OTE/POI confluence analizi hatası: {e}", exc_info=True)
            return []

    def analyze(self, symbol: str, fib_levels: dict, order_blocks: list, fvgs: list):
        """
        OTE bölgeleri ile Order Block ve FVG gibi POI'ler arasındaki kesişimleri analiz eder.
        main.py ile uyumluluk için eski interface korunmuştur.
        """
        # Eski interface'i yeni analyze metoduna yönlendir
        try:
            # fib_levels'dan fibonacci_data oluştur
            fibonacci_data = fib_levels

            # order_blocks'dan order_block_data oluştur - DÜZELTME: Doğru anahtar isimleri kullan
            order_block_data = {
                'bullish': [],
                'bearish': []
            }

            # order_blocks listesini uygun formata çevir
            if isinstance(order_blocks, list):
                for ob in order_blocks:
                    ob_type = ob.get('type', '').lower()
                    if 'bull' in ob_type:
                        order_block_data['bullish'].append(ob)
                    elif 'bear' in ob_type:
                        order_block_data['bearish'].append(ob)

            # Debug: Order Block verilerinin başarıyla alındığını kontrol et
            logger.debug(f"🔍 OTE Order Block verileri - Bullish: {len(order_block_data['bullish'])}, Bearish: {len(order_block_data['bearish'])}")

            # Güncel fiyatı tahmin et (son fiyat olarak)
            current_price = 0
            if 'range_high' in fibonacci_data and 'range_low' in fibonacci_data:
                current_price = (fibonacci_data['range_high'] + fibonacci_data['range_low']) / 2

            # Yeni analyze metodunu çağır
            return self.analyze_comprehensive(symbol, fibonacci_data, order_block_data, current_price)

        except Exception as e:
            logger.error(f"[{symbol}] Eski interface OTE analizi hatası: {e}")
            return {
                'confluences': [],
                'highest_rated_confluence': None,
                'error': True,
                'error_reason': str(e)
            }
    """
    OTE (Optimal Trade Entry) + Order Block Confluence Analizörü.
    
    Bu sınıf, Fibonacci OTE bölgeleri ile Order Block'ların kesişimini
    analiz ederek yüksek kaliteli giriş fırsatları tespit eder.
    """
    
    def __init__(self, 
                 ote_fib_min: float = 0.618,
                 ote_fib_max: float = 0.79,
                 min_confluence_score: float = 70.0,
                 proximity_tolerance_pct: float = 1.0):
        """
        OTE Confluence Analyzer'ı başlatır.
        
        Args:
            ote_fib_min: OTE bölgesinin alt sınırı (61.8%)
            ote_fib_max: OTE bölgesinin üst sınırı (79%)
            min_confluence_score: Minimum confluence skoru
            proximity_tolerance_pct: Yakınlık toleransı (%)
        """
        self.ote_fib_min = ote_fib_min
        self.ote_fib_max = ote_fib_max
        self.min_confluence_score = min_confluence_score
        self.proximity_tolerance_pct = proximity_tolerance_pct / 100
        
        logger.info(f"OTE Confluence Analyzer başlatıldı - "
                   f"OTE Zone: {ote_fib_min:.1%}-{ote_fib_max:.1%}, "
                   f"Min Score: {min_confluence_score}, "
                   f"Tolerance: {proximity_tolerance_pct}%")

    def analyze_comprehensive(self, symbol: str,
                fibonacci_data: Dict[str, Any],
                order_block_data: Dict[str, Any],
                current_price: float) -> Dict[str, Any]:
        """
        Ana OTE + OB confluence analizi.
        
        Args:
            symbol: Sembol adı
            fibonacci_data: Fibonacci analiz sonuçları
            order_block_data: Order Block analiz sonuçları
            current_price: Güncel fiyat
            
        Returns:
            OTE Confluence analiz sonuçları
        """
        logger.info(f"[{symbol}] OTE Confluence analizi başlıyor...")
        
        try:
            # 1. Fibonacci verilerini doğrula
            if not self._validate_fibonacci_data(fibonacci_data):
                return self._empty_result("Geçersiz Fibonacci verisi")
            
            # 2. Order Block verilerini doğrula
            if not self._validate_order_block_data(order_block_data):
                return self._empty_result("Geçersiz Order Block verisi")
            
            # 3. OTE bölgelerini hesapla
            ote_zones = self._calculate_ote_zones(fibonacci_data)
            if not ote_zones:
                return self._empty_result("OTE bölgeleri hesaplanamadı")
            
            # 4. OTE + OB kesişimlerini bul
            confluences = self._find_ote_ob_intersections(
                ote_zones, order_block_data, current_price
            )
            
            # 5. Confluence'ları skorla ve filtrele
            scored_confluences = self._score_confluences(confluences, current_price)
            
            # 6. En iyi confluence'ları seç
            high_quality_confluences = [
                c for c in scored_confluences 
                if c['confluence_score'] >= self.min_confluence_score
            ]
            
            # 7. Sonuçları formatla
            result = {
                'confluences': high_quality_confluences,
                'ote_zones': ote_zones,
                'total_intersections': len(confluences),
                'high_quality_count': len(high_quality_confluences),
                'analysis_timestamp': datetime.now().isoformat(),
                'summary': self._generate_summary(high_quality_confluences)
            }
            
            logger.success(f"[{symbol}] OTE Confluence analizi tamamlandı: "
                          f"{len(confluences)} kesişim, "
                          f"{len(high_quality_confluences)} yüksek kalite")
            
            return result
            
        except Exception as e:
            logger.error(f"[{symbol}] OTE Confluence analizi hatası: {e}", exc_info=True)
            return self._empty_result(f"Analiz hatası: {str(e)}")

    def _validate_fibonacci_data(self, fibonacci_data: Dict[str, Any]) -> bool:
        """Fibonacci verilerini doğrular."""
        if not fibonacci_data or fibonacci_data.get('error', True):
            return False
            
        required_fields = ['range_high', 'range_low', 'direction']
        return all(field in fibonacci_data for field in required_fields)

    def _validate_order_block_data(self, order_block_data: Dict[str, Any]) -> bool:
        """Order Block verilerini doğrular."""
        if not order_block_data:
            return False
            
        bullish_obs = order_block_data.get('bullish', [])
        bearish_obs = order_block_data.get('bearish', [])
        
        return isinstance(bullish_obs, list) or isinstance(bearish_obs, list)

    def _calculate_ote_zones(self, fibonacci_data: Dict[str, Any]) -> List[Dict[str, Any]]:
        """
        Fibonacci verilerinden OTE bölgelerini hesaplar.
        
        Args:
            fibonacci_data: Fibonacci analiz sonuçları
            
        Returns:
            OTE bölgeleri listesi
        """
        try:
            range_high = float(fibonacci_data['range_high'])
            range_low = float(fibonacci_data['range_low'])
            direction = fibonacci_data['direction']
            
            # Fibonacci retracement seviyeleri
            range_size = range_high - range_low
            
            if direction == 'bullish':
                # Bullish trend: High'dan Low'a retracement
                ote_top = range_high - (range_size * self.ote_fib_min)
                ote_bottom = range_high - (range_size * self.ote_fib_max)
                
                ote_zone = {
                    'type': 'BULLISH_OTE',
                    'direction': 'bullish',
                    'top': ote_top,
                    'bottom': ote_bottom,
                    'mid': (ote_top + ote_bottom) / 2,
                    'fib_range': f"{self.ote_fib_min:.1%}-{self.ote_fib_max:.1%}",
                    'impulse_high': range_high,
                    'impulse_low': range_low
                }
                
            else:  # bearish
                # Bearish trend: Low'dan High'a retracement  
                ote_bottom = range_low + (range_size * self.ote_fib_min)
                ote_top = range_low + (range_size * self.ote_fib_max)
                
                ote_zone = {
                    'type': 'BEARISH_OTE',
                    'direction': 'bearish',
                    'top': ote_top,
                    'bottom': ote_bottom,
                    'mid': (ote_top + ote_bottom) / 2,
                    'fib_range': f"{self.ote_fib_min:.1%}-{self.ote_fib_max:.1%}",
                    'impulse_high': range_high,
                    'impulse_low': range_low
                }
            
            logger.debug(f"OTE Zone hesaplandı: {ote_zone['type']} | "
                        f"Top: {ote_zone['top']:.6f} | "
                        f"Bottom: {ote_zone['bottom']:.6f}")
            
            return [ote_zone]
            
        except Exception as e:
            logger.error(f"OTE bölgesi hesaplama hatası: {e}")
            return []

    def _find_ote_ob_intersections(self, 
                                  ote_zones: List[Dict[str, Any]],
                                  order_block_data: Dict[str, Any],
                                  current_price: float) -> List[Dict[str, Any]]:
        """
        OTE bölgeleri ile Order Block'ların kesişimlerini bulur.
        
        Args:
            ote_zones: OTE bölgeleri
            order_block_data: Order Block verileri
            current_price: Güncel fiyat
            
        Returns:
            Kesişim listesi
        """
        intersections = []
        
        # Tüm order block'ları al
        all_obs = []
        
        bullish_obs = order_block_data.get('bullish', [])
        bearish_obs = order_block_data.get('bearish', [])
        
        if isinstance(bullish_obs, list):
            all_obs.extend(bullish_obs)
        if isinstance(bearish_obs, list):
            all_obs.extend(bearish_obs)
        
        logger.debug(f"Kesişim analizi: {len(ote_zones)} OTE zone, {len(all_obs)} OB")
        
        for ote_zone in ote_zones:
            for order_block in all_obs:
                intersection = self._check_ote_ob_intersection(ote_zone, order_block, current_price)
                if intersection:
                    intersections.append(intersection)
        
        return intersections

    def _check_ote_ob_intersection(self, 
                                  ote_zone: Dict[str, Any],
                                  order_block: Dict[str, Any],
                                  current_price: float) -> Optional[Dict[str, Any]]:
        """
        Tek bir OTE bölgesi ile Order Block arasındaki kesişimi kontrol eder.
        
        Args:
            ote_zone: OTE bölgesi
            order_block: Order Block
            current_price: Güncel fiyat
            
        Returns:
            Kesişim varsa interseksiyon bilgileri, yoksa None
        """
        try:
            # OTE zone bilgileri
            ote_top = ote_zone['top']
            ote_bottom = ote_zone['bottom']
            ote_direction = ote_zone['direction']
            
            # Order Block bilgileri
            ob_high = float(order_block.get('high', 0))
            ob_low = float(order_block.get('low', 0))
            ob_type = order_block.get('type', '').lower()
            
            # Kesişim kontrolü
            overlap_top = min(ote_top, ob_high)
            overlap_bottom = max(ote_bottom, ob_low)
            
            # Kesişim var mı?
            if overlap_top <= overlap_bottom:
                return None
            
            # Kesişim alanı hesapla
            overlap_size = overlap_top - overlap_bottom
            ote_size = ote_top - ote_bottom
            ob_size = ob_high - ob_low
            
            overlap_pct_ote = (overlap_size / ote_size) * 100
            overlap_pct_ob = (overlap_size / ob_size) * 100
            
            # Direction uyumu kontrol et
            direction_match = (
                (ote_direction == 'bullish' and 'bull' in ob_type) or
                (ote_direction == 'bearish' and 'bear' in ob_type)
            )
            
            # Kesişim bilgilerini oluştur
            intersection = {
                'ote_zone': ote_zone,
                'order_block': order_block,
                'intersection_top': overlap_top,
                'intersection_bottom': overlap_bottom,
                'intersection_mid': (overlap_top + overlap_bottom) / 2,
                'intersection_size': overlap_size,
                'overlap_pct_ote': overlap_pct_ote,
                'overlap_pct_ob': overlap_pct_ob,
                'direction_match': direction_match,
                'direction': ote_direction,
                'distance_from_price': abs(((overlap_top + overlap_bottom) / 2) - current_price),
                'distance_from_price_pct': abs(((((overlap_top + overlap_bottom) / 2) - current_price) / current_price) * 100)
            }
            
            logger.debug(f"Kesişim bulundu: {ote_direction.upper()} OTE + {ob_type.upper()} OB | "
                        f"Overlap: {overlap_pct_ote:.1f}% OTE, {overlap_pct_ob:.1f}% OB | "
                        f"Direction Match: {direction_match}")
            
            return intersection
            
        except Exception as e:
            logger.error(f"Kesişim kontrolü hatası: {e}")
            return None

    def _score_confluences(self, 
                          confluences: List[Dict[str, Any]],
                          current_price: float) -> List[Dict[str, Any]]:
        """
        Confluence'ları kalite skoruna göre puanlar.
        
        Args:
            confluences: Confluence listesi
            current_price: Güncel fiyat
            
        Returns:
            Skorlanmış confluence listesi
        """
        scored_confluences = []
        
        for confluence in confluences:
            score = self._calculate_confluence_score(confluence, current_price)
            confluence['confluence_score'] = score
            confluence['quality_rating'] = self._get_quality_rating(score)
            scored_confluences.append(confluence)
        
        # Skora göre sırala (yüksekten düşüğe)
        scored_confluences.sort(key=lambda x: x['confluence_score'], reverse=True)
        
        return scored_confluences

    def _calculate_confluence_score(self, 
                                   confluence: Dict[str, Any],
                                   current_price: float) -> float:
        """
        Tek bir confluence'ın kalite skorunu hesaplar (0-100).
        
        Args:
            confluence: Confluence verisi
            current_price: Güncel fiyat
            
        Returns:
            Confluence skoru (0-100)
        """
        score = 0.0
        
        try:
            # 1. Overlap kalitesi (40 puan)
            overlap_ote = confluence['overlap_pct_ote']
            overlap_ob = confluence['overlap_pct_ob']
            
            # İdeal overlap: %50+ için her iki alan
            overlap_score = min(40, (min(overlap_ote, overlap_ob) / 50) * 40)
            score += overlap_score
            
            # 2. Direction uyumu (25 puan)
            if confluence['direction_match']:
                score += 25
            
            # 3. Fiyat yakınlığı (20 puan)
            distance_pct = confluence['distance_from_price_pct']
            if distance_pct <= 1.0:  # %1 içinde
                proximity_score = 20
            elif distance_pct <= 3.0:  # %3 içinde
                proximity_score = 15
            elif distance_pct <= 5.0:  # %5 içinde
                proximity_score = 10
            else:
                proximity_score = max(0, 20 - (distance_pct * 2))
            
            score += proximity_score
            
            # 4. Order Block kalitesi (15 puan)
            order_block = confluence['order_block']
            ob_strength = order_block.get('strength', 'medium').lower()
            
            if ob_strength == 'strong':
                score += 15
            elif ob_strength == 'medium':
                score += 10
            else:  # weak
                score += 5
            
            # Bonus puanlar
            # 5. Büyük overlap (bonus +5)
            if min(overlap_ote, overlap_ob) >= 80:
                score += 5
            
            # 6. Çok yakın fiyat (bonus +5)
            if distance_pct <= 0.5:
                score += 5
            
            # Skor sınırlaması
            score = min(100, max(0, score))
            
            logger.debug(f"Confluence score hesaplandı: {score:.1f} | "
                        f"Overlap: {overlap_score:.1f}, Direction: {25 if confluence['direction_match'] else 0}, "
                        f"Proximity: {proximity_score:.1f}, OB Quality: {15 if ob_strength == 'strong' else 10 if ob_strength == 'medium' else 5}")
            
            return score
            
        except Exception as e:
            logger.error(f"Confluence score hesaplama hatası: {e}")
            return 0.0

    def _get_quality_rating(self, score: float) -> str:
        """Skora göre kalite derecelendirmesi."""
        if score >= 85:
            return "EXCELLENT"
        elif score >= 75:
            return "VERY_GOOD"
        elif score >= 65:
            return "GOOD"
        elif score >= 50:
            return "FAIR"
        else:
            return "POOR"

    def _generate_summary(self, confluences: List[Dict[str, Any]]) -> Dict[str, Any]:
        """Confluence analizi özeti oluşturur."""
        if not confluences:
            return {
                'best_confluence': None,
                'total_count': 0,
                'avg_score': 0.0,
                'quality_distribution': {}
            }
        
        scores = [c['confluence_score'] for c in confluences]
        best_confluence = confluences[0] if confluences else None
        
        # Kalite dağılımı
        quality_dist = {}
        for confluence in confluences:
            quality = confluence['quality_rating']
            quality_dist[quality] = quality_dist.get(quality, 0) + 1
        
        return {
            'best_confluence': best_confluence,
            'total_count': len(confluences),
            'avg_score': sum(scores) / len(scores),
            'max_score': max(scores),
            'min_score': min(scores),
            'quality_distribution': quality_dist
        }

    def _empty_result(self, reason: str = "Analiz yapılamadı") -> Dict[str, Any]:
        """Boş analiz sonucu döndürür."""
        return {
            'confluences': [],
            'ote_zones': [],
            'total_intersections': 0,
            'high_quality_count': 0,
            'analysis_timestamp': datetime.now().isoformat(),
            'error': True,
            'error_reason': reason,
            'summary': {
                'best_confluence': None,
                'total_count': 0,
                'avg_score': 0.0,
                'quality_distribution': {}
            }
        }

    def get_best_confluence_signal(self, 
                                  analysis_result: Dict[str, Any],
                                  symbol: str) -> Optional[Dict[str, Any]]:
        """
        En iyi confluence'dan ticaret sinyali oluşturur.
        
        Args:
            analysis_result: OTE Confluence analiz sonucu
            symbol: Sembol adı
            
        Returns:
            Ticaret sinyali veya None
        """
        confluences = analysis_result.get('confluences', [])
        if not confluences:
            return None
        
        best_confluence = confluences[0]  # En yüksek skorlu
        
        # Sinyal oluştur
        signal = {
            'type': 'OTE_OB_CONFLUENCE',
            'symbol': symbol,
            'direction': 'bull' if best_confluence['direction'] == 'bullish' else 'bear',
            'price': best_confluence['intersection_mid'],
            'confidence': best_confluence['confluence_score'] / 100,
            'reason': f"OTE Zone + Order Block Confluence (Score: {best_confluence['confluence_score']:.1f})",
            'confluence_data': best_confluence,
            'entry_zone_top': best_confluence['intersection_top'],
            'entry_zone_bottom': best_confluence['intersection_bottom'],
            'quality_rating': best_confluence['quality_rating'],
            'ote_zone_info': best_confluence['ote_zone'],
            'order_block_info': best_confluence['order_block']
        }
        
        logger.success(f"[{symbol}] En iyi OTE Confluence sinyali oluşturuldu: "
                      f"{signal['direction'].upper()} @ {signal['price']:.6f} "
                      f"(Score: {best_confluence['confluence_score']:.1f})")
        
        return signal

    def assess_ote_poi_quality(self, symbol: str, all_symbol_data: Dict[str, Any],
                              ote_zone: Dict[str, float], trade_direction: str) -> Dict[str, Any]:
        """
        OTE bölgesi içindeki POI kalitesini değerlendirir.
        scoring_system.py'den taşınan merkezi fonksiyon.

        Args:
            symbol: Sembol adı
            all_symbol_data: Tüm analiz verileri
            ote_zone: OTE bölgesi bilgileri
            trade_direction: İşlem yönü

        Returns:
            POI kalite analizi sonucu
        """
        try:
            ote_start = ote_zone.get('entry_start', ote_zone.get('bottom', 0))
            ote_end = ote_zone.get('entry_end', ote_zone.get('top', 0))

            if not ote_start or not ote_end:
                return {'score': 0, 'description': 'OTE zone verisi eksik'}

            quality_score = 0
            poi_types = []

            # Order Block kalitesi
            order_blocks = all_symbol_data.get('main_tf_order_blocks', {})
            if order_blocks:
                obs_to_check = []
                if trade_direction.lower() in ['bullish', 'bull', 'long']:
                    obs_to_check = order_blocks.get('bullish', [])
                else:
                    obs_to_check = order_blocks.get('bearish', [])

                for ob in obs_to_check:
                    ob_mid = (ob.get('high', 0) + ob.get('low', 0)) / 2
                    if min(ote_start, ote_end) <= ob_mid <= max(ote_start, ote_end):
                        ob_strength = ob.get('strength', 1)
                        quality_score += ob_strength * 5  # 5-25 puan arası
                        poi_types.append(f"OB (Güç: {ob_strength})")

            # FVG kalitesi
            fvg_analysis = all_symbol_data.get('fvg_analysis', [])
            if isinstance(fvg_analysis, list):
                for fvg in fvg_analysis:
                    if fvg.get('mitigated', True):  # Sadece aktif FVG'ler
                        continue

                    fvg_mid = (fvg.get('top', 0) + fvg.get('bottom', 0)) / 2
                    if min(ote_start, ote_end) <= fvg_mid <= max(ote_start, ote_end):
                        fvg_type = fvg.get('type', '').lower()
                        if (trade_direction.lower() in ['bullish', 'bull', 'long'] and 'bull' in fvg_type) or \
                           (trade_direction.lower() in ['bearish', 'bear', 'short'] and 'bear' in fvg_type):
                            quality_score += 8  # FVG için sabit puan
                            poi_types.append("FVG")

            # Liquidity Sweep kalitesi
            liquidity_analysis = all_symbol_data.get('liquidity_analysis', {})
            if liquidity_analysis:
                sweeps = liquidity_analysis.get('sweeps', [])
                for sweep in sweeps:
                    sweep_price = sweep.get('price', 0)
                    if min(ote_start, ote_end) <= sweep_price <= max(ote_start, ote_end):
                        quality_score += 6  # Liquidity sweep için puan
                        poi_types.append("Liquidity Sweep")

            # Kalite açıklaması
            if quality_score >= 20:
                description = "Yüksek kaliteli POI bölgesi"
            elif quality_score >= 10:
                description = "Orta kaliteli POI bölgesi"
            elif quality_score > 0:
                description = "Düşük kaliteli POI bölgesi"
            else:
                description = "POI bulunamadı"

            if poi_types:
                description += f" ({', '.join(poi_types)})"

            return {
                'score': min(quality_score, 30),  # Maximum 30 puan
                'description': description,
                'poi_types': poi_types
            }

        except Exception as e:
            logger.error(f"[{symbol}] OTE POI kalite değerlendirme hatası: {e}")
            return {'score': 0, 'description': 'Değerlendirme hatası'}

    def assess_fibonacci_level_poi_quality(self, symbol: str, all_symbol_data: Dict[str, Any],
                                          closest_level: Dict[str, Any], trade_direction: str) -> Dict[str, Any]:
        """
        Fibonacci seviyesi etrafındaki POI kalitesini değerlendirir.
        scoring_system.py'den taşınan merkezi fonksiyon.

        Args:
            symbol: Sembol adı
            all_symbol_data: Tüm analiz verileri
            closest_level: En yakın Fibonacci seviyesi
            trade_direction: İşlem yönü

        Returns:
            POI kalite analizi sonucu
        """
        try:
            level_price = closest_level.get('price', 0)
            tolerance = level_price * 0.01  # %1 tolerans

            quality_score = 0
            poi_types = []

            # Order Block kalitesi
            order_blocks = all_symbol_data.get('main_tf_order_blocks', {})
            if order_blocks:
                obs_to_check = []
                if trade_direction.lower() in ['bullish', 'bull', 'long']:
                    obs_to_check = order_blocks.get('bullish', [])
                else:
                    obs_to_check = order_blocks.get('bearish', [])

                for ob in obs_to_check:
                    ob_mid = (ob.get('high', 0) + ob.get('low', 0)) / 2
                    if abs(ob_mid - level_price) <= tolerance:
                        ob_strength = ob.get('strength', 1)
                        quality_score += ob_strength * 4  # 4-20 puan arası
                        poi_types.append(f"OB (Güç: {ob_strength})")

            # FVG kalitesi
            fvg_analysis = all_symbol_data.get('fvg_analysis', [])
            if isinstance(fvg_analysis, list):
                for fvg in fvg_analysis:
                    if fvg.get('mitigated', True):
                        continue

                    fvg_mid = (fvg.get('top', 0) + fvg.get('bottom', 0)) / 2
                    if abs(fvg_mid - level_price) <= tolerance:
                        fvg_type = fvg.get('type', '').lower()
                        if (trade_direction.lower() in ['bullish', 'bull', 'long'] and 'bull' in fvg_type) or \
                           (trade_direction.lower() in ['bearish', 'bear', 'short'] and 'bear' in fvg_type):
                            quality_score += 6  # FVG için puan
                            poi_types.append("FVG")

            # Kalite açıklaması
            if quality_score >= 15:
                description = "Yüksek kaliteli Fibonacci POI"
            elif quality_score >= 8:
                description = "Orta kaliteli Fibonacci POI"
            elif quality_score > 0:
                description = "Düşük kaliteli Fibonacci POI"
            else:
                description = "Fibonacci seviyesinde POI bulunamadı"

            if poi_types:
                description += f" ({', '.join(poi_types)})"

            return {
                'score': min(quality_score, 25),  # Maximum 25 puan
                'description': description,
                'poi_types': poi_types
            }

        except Exception as e:
            logger.error(f"[{symbol}] Fibonacci POI kalite değerlendirme hatası: {e}")
            return {'score': 0, 'description': 'Değerlendirme hatası'}

    def calculate_ote_zone_from_swing_points(self, swing_points: List[Dict[str, Any]],
                                           trade_direction: str) -> Optional[Dict[str, Any]]:
        """
        Swing noktalarından OTE bölgesi hesaplar.
        scoring_system.py'den taşınan merkezi fonksiyon.

        Args:
            swing_points: Swing noktaları listesi
            trade_direction: İşlem yönü

        Returns:
            OTE bölgesi bilgileri veya None
        """
        try:
            if len(swing_points) < 2:
                return None

            # Son iki swing noktasını al
            last_swing = swing_points[-1]
            prev_swing = swing_points[-2]

            # Leg başlangıç ve bitiş noktalarını belirle
            if trade_direction.lower() in ['bullish', 'bull', 'long']:
                # Bullish için: Low'dan High'a leg
                if last_swing.get('type') == 'high' and prev_swing.get('type') == 'low':
                    leg_start = prev_swing.get('price', 0)
                    leg_end = last_swing.get('price', 0)
                else:
                    return None
            else:
                # Bearish için: High'dan Low'a leg
                if last_swing.get('type') == 'low' and prev_swing.get('type') == 'high':
                    leg_start = prev_swing.get('price', 0)
                    leg_end = last_swing.get('price', 0)
                else:
                    return None

            if not leg_start or not leg_end or leg_start == leg_end:
                return None

            # OTE bölgesi hesapla (61.8% - 79% retracement)
            leg_size = abs(leg_end - leg_start)

            if trade_direction.lower() in ['bullish', 'bull', 'long']:
                # Bullish: High'dan aşağıya retracement
                ote_start = leg_end - (leg_size * self.ote_fib_min)  # 61.8%
                ote_end = leg_end - (leg_size * self.ote_fib_max)    # 79%
                sweet_spot = leg_end - (leg_size * 0.705)           # 70.5% OTE sweet spot
            else:
                # Bearish: Low'dan yukarıya retracement
                ote_start = leg_end + (leg_size * self.ote_fib_min)  # 61.8%
                ote_end = leg_end + (leg_size * self.ote_fib_max)    # 79%
                sweet_spot = leg_end + (leg_size * 0.705)           # 70.5% OTE sweet spot

            return {
                'type': f"{trade_direction.upper()}_OTE",
                'direction': trade_direction.lower(),
                'entry_start': min(ote_start, ote_end),
                'entry_end': max(ote_start, ote_end),
                'sweet_spot': sweet_spot,
                'top': max(ote_start, ote_end),
                'bottom': min(ote_start, ote_end),
                'mid': (ote_start + ote_end) / 2,
                'leg_start': leg_start,
                'leg_end': leg_end,
                'leg_size': leg_size,
                'fib_range': f"{self.ote_fib_min:.1%}-{self.ote_fib_max:.1%}",
                'last_swing': last_swing,
                'prev_swing': prev_swing
            }

        except Exception as e:
            logger.error(f"OTE bölgesi hesaplama hatası: {e}")
            return None

    def check_ote_confluence_signal(self, symbol: str, all_symbol_data: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """
        OTE + Order Block Confluence sinyalini kontrol eder.
        scoring_system.py'den taşınan merkezi fonksiyon.

        Args:
            symbol: Sembol adı
            all_symbol_data: Tüm analiz verileri

        Returns:
            OTE+OB confluence sinyali veya None
        """
        try:
            # Önceden hesaplanmış OTE confluence analiz sonuçlarını al
            ote_analysis = all_symbol_data.get('ote_confluence_analysis', {})

            # Analiz hatası varsa veya sonuç yoksa None döndür
            if ote_analysis.get('error') or not ote_analysis:
                logger.debug(f"[{symbol}] OTE confluence analizi mevcut değil veya hatalı")
                return None

            # En iyi confluence sinyali al
            confluences = ote_analysis.get('confluences', [])
            if confluences:
                best_signal = self.get_best_confluence_signal(ote_analysis, symbol)
                if best_signal:
                    logger.success(f"[{symbol}] 🏆 OTE+OB Confluence sinyali: {best_signal['quality_rating']} kalite, "
                                  f"Skor: {best_signal['confidence']*100:.1f}")

                    # Scoring system formatına uygun sinyal döndür
                    return {
                        'type': 'OTE_OB_CONFLUENCE',
                        'symbol': symbol,
                        'direction': best_signal['direction'],
                        'price': best_signal['price'],
                        'confidence': best_signal['confidence'],
                        'reason': best_signal['reason'],
                        'confluence_score': best_signal['confidence'] * 100,
                        'quality_rating': best_signal['quality_rating'],
                        'details': best_signal
                    }

            return None

        except Exception as e:
            logger.error(f"[{symbol}] OTE confluence sinyal kontrolü hatası: {e}")
            return None
