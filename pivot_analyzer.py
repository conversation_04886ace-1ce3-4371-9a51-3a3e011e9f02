"""
ICT (Inner Circle Trader) prensiplerine uygun Pivot analizi modülü.
Piyasa yapısının anlamlı dönüş noktalarını (swing high/low) tespit eder.
Bu modül, market_structure_analyzer gibi diğer analiz modüllerinin temelini oluşturur.
"""

import pandas as pd
from loguru import logger
from typing import List, Dict, Any

class PivotAnalyzer:
    """
    ICT (Inner Circle Trader) konseptlerine uygun olarak piyasanın anlamlı dönüş 
    noktalarını (swing high/low) tespit eder. Bu modül, piyasa yapısı analizinin
    temelini oluşturur ve iç (internal) yapıların gürültüsünü filtreler.
    """
    
    def __init__(self, length: int = 5):
        """
        ICT pivot analizörünü başlatır.
        
        Args:
            length (int): Bir pivotun ne kadar önemli olduğunu belirler.
                         <PERSON><PERSON> yüksek değerler sadece majör dönüşleri yakalar.
                         ICT'de genellikle 5-15 arası değerler kullanılır.
        """
        if length < 1:
            raise ValueError("Pivot 'length' değeri 1'den küçük olamaz.")
        self.length = length
        logger.info(f"PivotAnalyzer başlatıldı - Swing periyodu: {self.length}")

    def find_pivots(self, candles: pd.DataFrame) -> List[Dict[str, Any]]:
        """
        ICT prensiplerine uygun olarak anlamlı swing high ve swing low noktalarını tespit eder.
        
        ICT Mantığı:
        - Bir swing high, sol ve sağ tarafındaki 'length' kadar mumdan daha yüksek olmalıdır.
        - Bir swing low, sol ve sağ tarafındaki 'length' kadar mumdan daha düşük olmalıdır.
        - Bu yaklaşım, piyasanın gerçek yapısal değişimlerini yakalar.
        
        Args:
            candles (pd.DataFrame): 'high', 'low', 'timestamp' sütunlarını içeren mum verileri.
        
        Returns:
            Her biri bir swing noktasını temsil eden sözlüklerin listesi.
        """
        swing_points = []
        if candles is None or len(candles) < (self.length * 2 + 1):
            logger.warning(f"Pivot analizi için yetersiz veri. Mevcut: {len(candles) if candles is not None else 0}, Gerekli: {self.length * 2 + 1}")
            return swing_points

        logger.debug(f"Pivot analizi başlatılıyor: {len(candles)} mum, length={self.length}")
        
        highs = candles['high'].values
        lows = candles['low'].values
        timestamps = candles['timestamp'].values
        
        for i in range(self.length, len(candles) - self.length):
            is_swing_high = all(highs[i] > h for h in highs[i-self.length:i]) and \
                            all(highs[i] > h for h in highs[i+1:i+self.length+1])
            
            if is_swing_high:
                swing_points.append({
                    'timestamp': timestamps[i],
                    'price': highs[i],
                    'type': 'high',
                    'candle_index': i
                })
                logger.debug(f"Swing High tespit edildi @ {timestamps[i]}: {highs[i]}")
                continue

            is_swing_low = all(lows[i] < l for l in lows[i-self.length:i]) and \
                           all(lows[i] < l for l in lows[i+1:i+self.length+1])

            if is_swing_low:
                swing_points.append({
                    'timestamp': timestamps[i],
                    'price': lows[i],
                    'type': 'low',
                    'candle_index': i
                })
                logger.debug(f"Swing Low tespit edildi @ {timestamps[i]}: {lows[i]}")

        swing_points.sort(key=lambda x: x['timestamp'])
        for i, swing in enumerate(swing_points):
            swing['index'] = i
        
        logger.info(f"Pivot analizi tamamlandı: {len(swing_points)} toplam swing noktası bulundu (length={self.length}).")
        return swing_points
