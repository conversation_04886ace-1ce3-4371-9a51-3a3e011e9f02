"""
Premium/Discount analizi için modül.
Günlük zaman diliminde pivot noktalarına dayalı Premium/Discount bölgelerini hesaplar.
"""

from typing import Dict, List, Any, Tuple, Optional
from loguru import logger

# Logger formatını değiştir - Modül/fonksiyon/satır bilgisini gizle
logger = logger.bind(format="{time:YYYY-MM-DD HH:mm:ss} | {level} | {message}")

class PremiumDiscountAnalyzer:
    """
    Premium/Discount bölgelerini hesaplayan ve bu bölgelere göre yapıları (FVG, OB) puanlayan sınıf.
    """

    def __init__(self):
        """
        PremiumDiscountAnalyzer sınıfını başlatır.
        """
        logger.info("Premium/Discount analiz modülü başlatıldı")

    def calculate_daily_premium_discount(self, symbol: str, daily_stats: Optional[Dict[str, Any]], swing_points: Optional[list] = None) -> Dict[str, Any]:
        """
        Günlük zaman diliminde son iki swing/pivot noktasını kullanarak Premium/Discount bölgelerini hesaplar.

        Args:
            symbol: Kripto para sembolü
            daily_stats: Günlük zaman dilimi için stats verisi (sadece current_price için kullanılır)
            swing_points: Günlük swing/pivot noktaları listesi (zorunlu)

        Returns:
            Dict: Premium/Discount bölge bilgisi
        """
        # Swing points kontrolü
        if not swing_points or not isinstance(swing_points, list) or len(swing_points) < 2:
            return {
                'status': 'error',
                'reason': "Günlük swing/pivot noktaları bulunamadı veya yetersiz (en az 2 pivot gerekli)",
                'is_premium': False,
                'is_discount': False,
                'eq_level': 0.0
            }

        # Son iki pivot noktasını al
        last_pivot = swing_points[-1]
        prev_pivot = swing_points[-2]

        # Pivot fiyatlarını kontrol et
        if 'price' not in last_pivot or 'price' not in prev_pivot:
            return {
                'status': 'error',
                'reason': "Pivot noktalarında fiyat bilgisi eksik",
                'is_premium': False,
                'is_discount': False,
                'eq_level': 0.0
            }

        # High ve Low değerlerini belirle
        high = max(last_pivot['price'], prev_pivot['price'])
        low = min(last_pivot['price'], prev_pivot['price'])

        # EQ seviyesini hesapla (Fibonacci 0.5 seviyesi)
        eq_level = low + (high - low) * 0.5

        # Güncel fiyatı al
        current_price = daily_stats.get('last_price') if daily_stats else None
        if not current_price:
            return {
                'status': 'error',
                'reason': "Günlük fiyat verisi yok",
                'is_premium': False,
                'is_discount': False,
                'eq_level': eq_level,  # EQ seviyesini yine de döndür
                'high': high,
                'low': low
            }

        # Premium/Discount durumunu belirle
        is_premium = current_price >= eq_level
        is_discount = current_price < eq_level

        # Son pivot tiplerini log için al
        last_pivot_type = last_pivot.get('type', 'Bilinmiyor')
        prev_pivot_type = prev_pivot.get('type', 'Bilinmiyor')

        # Sadece 0, EQ (0.5) ve 1 seviyelerini hesapla
        fib_levels = {
            "0": low,  # 0 seviyesi
            "0.5": eq_level,  # EQ seviyesi
            "1": high  # 1 seviyesi
        }

        # Detaylı log mesajı
        logger.info(f"[{symbol}] Premium/Discount hesaplandı: EQ={eq_level:.4f}, Current={current_price:.4f}, Zone={'Premium' if is_premium else 'Discount'}")

        # Sadece 0, EQ ve 1 seviyelerini logla
        logger.info(f"[{symbol}] Fibonacci Seviyeleri: 0={low:.4f}, EQ={eq_level:.4f}, 1={high:.4f}")
        logger.debug(f"[{symbol}] Son pivotlar: {prev_pivot_type} -> {last_pivot_type}, High={high:.4f}, Low={low:.4f}")

        return {
            'eq_level': eq_level,
            'is_premium': is_premium,
            'is_discount': is_discount,
            'high': high,
            'low': low,
            'current_price': current_price,
            'last_pivot_type': last_pivot_type,
            'prev_pivot_type': prev_pivot_type,
            'fib_levels': fib_levels,
            'status': 'success'
        }

    def is_price_in_structure(self, price: float, structure_top: float, structure_bottom: float, tolerance: float = 0.01) -> bool:
        """
        Fiyatın bir yapı içinde veya yakınında olup olmadığını kontrol eder

        Args:
            price: Kontrol edilecek fiyat
            structure_top: Yapının üst sınırı
            structure_bottom: Yapının alt sınırı
            tolerance: Tolerans yüzdesi (varsayılan %1)

        Returns:
            bool: Fiyat yapı içindeyse veya tolerans içinde kesişiyorsa True
        """
        # Tolerans değerini hesapla
        tolerance_value = (structure_top - structure_bottom) * tolerance

        # İçinde veya tolerans dahilinde kesişiyor mu?
        return (structure_bottom - tolerance_value <= price <= structure_top + tolerance_value)

    def is_in_zone(self, price: float, zone_type: str, pd_data: Dict[str, Any]) -> bool:
        """
        Verilen bir fiyatın, hesaplanan Premium/Discount bölgesinde olup olmadığını kontrol eder.

        Args:
            price (float): Kontrol edilecek fiyat.
            zone_type (str): Kontrol edilecek bölge ('premium' veya 'discount').
            pd_data (Dict[str, Any]): calculate_daily_premium_discount'dan dönen veri.

        Returns:
            bool: Fiyat belirtilen bölgedeyse True, değilse False.
        """
        if not pd_data or pd_data.get('status') != 'success':
            logger.warning("is_in_zone: Geçersiz veya başarısız Premium/Discount verisi.")
            return False

        eq_level = pd_data.get('eq_level')
        if eq_level is None:
            logger.warning("is_in_zone: Premium/Discount verisinde 'eq_level' bulunamadı.")
            return False

        if zone_type.lower() == 'premium':
            return price >= eq_level
        elif zone_type.lower() == 'discount':
            return price < eq_level
        else:
            logger.error(f"is_in_zone: Geçersiz zone_type: {zone_type}. 'premium' veya 'discount' olmalı.")
            return False

    def score_structure_premium_discount(self, symbol: str, timeframe: str, structure_type: str, structure: Dict[str, Any], pd_zones: Dict[str, Any], trade_direction: str, htf_trend: str, get_timeframe_label_func=None) -> List[Tuple[float, str]]:
        """
        Premium/Discount bölgelerine göre yapıları (FVG, OB) puanlar

        Args:
            symbol: Kripto para sembolü
            timeframe: Zaman dilimi (örn. '240')
            structure_type: Yapı tipi ('fvg' veya 'ob')
            structure: Yapı bilgileri
            pd_zones: Premium/Discount bölge bilgileri
            trade_direction: İşlem yönü ('bull' veya 'bear')
            get_timeframe_label_func: Zaman dilimi etiketini döndüren fonksiyon

        Returns:
            List[Tuple[float, str]]: Puan ve açıklama listesi
        """
        scores = []

        # 4H zaman dilimi kontrolü (Sadece 4H yapılarını puanla)
        if timeframe != '240':
            return scores

        # Premium/Discount bilgisi yoksa veya hesaplama başarısız olmuşsa puanlama yapılamaz
        if not pd_zones or pd_zones.get('status') != 'success' or 'is_premium' not in pd_zones or 'is_discount' not in pd_zones:
            logger.debug(f"[{symbol}] {structure_type.upper()} puanlaması yapılamadı: Premium/Discount bilgisi yok veya hesaplama başarısız")
            return scores

        # pd_zones içinde is_premium ve is_discount anahtarları var mı kontrol et
        if 'is_premium' not in pd_zones or 'is_discount' not in pd_zones:
            logger.debug(f"[{symbol}] {structure_type.upper()} puanlaması yapılamadı: pd_zones içinde is_premium veya is_discount anahtarları yok")
            return scores

        # Yapı tipine göre etiket belirle
        structure_label = "OB" if structure_type == 'ob' else "FVG"

        # Yapının tipini belirle (bullish/bearish)
        if structure_type == 'ob':
            pattern_type = None
            if 'type' in structure:
                pattern_type = structure['type']
            elif isinstance(structure, dict) and ('bullish' in structure or 'bearish' in structure):
                if 'bullish' in structure and structure['bullish']:
                    pattern_type = 'bullish'
                elif 'bearish' in structure and structure['bearish']:
                    pattern_type = 'bearish'
        else:  # FVG
            pattern_type = structure.get('type')

        if not pattern_type:
            logger.debug(f"[{symbol}] {structure_type.upper()} puanlaması yapılamadı: Yapı tipi belirlenemedi")
            return scores

        # Yapının sınırlarını belirle
        structure_top = structure.get('top', structure.get('high'))
        structure_bottom = structure.get('bottom', structure.get('low'))

        if not structure_top or not structure_bottom:
            logger.debug(f"[{symbol}] {structure_type.upper()} puanlaması yapılamadı: Yapı sınırları belirlenemedi")
            return scores

        # HTF Trend ve P/D Bölge Uyumu Kontrolü
        is_bullish_scenario = htf_trend == 'bullish' and pd_zones['is_discount'] and pattern_type == 'bullish'
        is_bearish_scenario = htf_trend == 'bearish' and pd_zones['is_premium'] and pattern_type == 'bearish'

        if not (is_bullish_scenario or is_bearish_scenario):
            logger.debug(f"[{symbol}] {structure_type.upper()} puanlaması atlandı: HTF trendi ({htf_trend}) ve P/D bölgesi uyumsuz.")
            return scores

        # Puanlama için değişkenler
        score_value = 2.0 if structure_type == 'ob' else 1.0  # OB: 2 puan, FVG: 1 puan

        # Mevcut fiyatı kontrol et
        if 'current_price' not in pd_zones or pd_zones['current_price'] == 0:
            logger.debug(f"[{symbol}] {structure_type.upper()} puanlaması yapılamadı: pd_zones içinde geçerli current_price anahtarı yok")
            return scores

        # Fiyatın yapı içinde olup olmadığını kontrol et (%1 toleransla)
        is_in_structure = self.is_price_in_structure(
            pd_zones['current_price'],
            structure_top,
            structure_bottom,
            tolerance=0.01
        )

        # Yapı içinde değilse puanlama yapma
        if not is_in_structure:
            return scores

        # Zaman dilimi etiketini al
        if get_timeframe_label_func:
            tf_label = get_timeframe_label_func(timeframe)
        else:
            # Varsayılan etiket
            tf_label = "4H" if timeframe == "240" else timeframe

        structure_name = f"{tf_label} {pattern_type.capitalize()} {structure_label}"
        zone_name = "Premium" if pd_zones['is_premium'] else "Discount"

        # Premium bölgede SHORT için bearish yapılar (pahalı bölgede satış), veya
        # Discount bölgede LONG için bullish yapılar (ucuz bölgede alış)
        if (pd_zones['is_premium'] and pattern_type == 'bearish' and trade_direction == 'bear') or \
           (pd_zones['is_discount'] and pattern_type == 'bullish' and trade_direction == 'bull'):
            scores.append((score_value, f"✅ {structure_name} in {zone_name}"))
            logger.debug(f"[{symbol}] {structure_name} yapısı {zone_name} bölgesinde, +{score_value} puan")

        # YENİ: Discount bölgede SHORT için bearish pattern: -0.5 puan (ucuz bölgede satış)
        # YENİ: Premium bölgede LONG için bullish pattern: -0.5 puan (pahalı bölgede alış)
        elif (pd_zones['is_discount'] and pattern_type == 'bearish' and trade_direction == 'bear'):
            scores.append((-0.5, f"🔴 {structure_label} Bearish in {zone_name}"))
            logger.debug(f"[{symbol}] {structure_name} yapısı {zone_name} bölgesinde, -0.5 puan (Yön uyumsuzluğu)")
        elif (pd_zones['is_premium'] and pattern_type == 'bullish' and trade_direction == 'bull'):
            scores.append((-0.5, f"🔴 {structure_label} Bullish in {zone_name}"))
            logger.debug(f"[{symbol}] {structure_name} yapısı {zone_name} bölgesinde, -0.5 puan (Yön uyumsuzluğu)")

        return scores
