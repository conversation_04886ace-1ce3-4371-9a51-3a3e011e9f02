# rejection_block_analyzer.py
# ICT Mitigation & Rejection Blocks Analyzer
# Fiyatın bir Order Block'u ihlal ettikten sonra güçlü reddetme bölgelerini tespit eder

import pandas as pd
import numpy as np
from loguru import logger
from typing import List, Dict, Any, Optional, Tuple
from datetime import datetime, timedelta

class RejectionBlockAnalyzer:
    """
    ICT Mitigation & Rejection Block Analyzer
    
    Bu modül, fiyatın bir Order Block veya önemli seviyeyi ihlal ettikten sonra
    güçlü bir şekilde reddedildiği bölgeleri tespit eder. Bu bölgeler gelecekte
    güçlü destek/direnç seviyeleri haline gelir.
    
    ICT Konseptleri:
    - Mitigation Block: OB'nin kısmi olarak test edilip reddedilmesi
    - Rejection Block: OB'nin tamamen ihlal edilip ardından güçlü reddetme
    - Failed Mitigation: Mitigation'ın başarısız olup Rejection'a dönüşmesi
    """
    
    def __init__(self,
                 rejection_strength_threshold: float = 1.5,
                 mitigation_test_pips: float = 10.0,
                 min_rejection_body_pct: float = 70.0,
                 rejection_confirmation_candles: int = 2):
        """
        Rejection Block Analyzer'ı başlatır.
        
        Args:
            rejection_strength_threshold: Rejection için minimum geri çekilme oranı (%)
            mitigation_test_pips: Mitigation testi için pip toleransı
            min_rejection_body_pct: Rejection mumu için minimum body yüzdesi
            rejection_confirmation_candles: Rejection onayı için gereken mum sayısı
        """
        self.rejection_strength_threshold = rejection_strength_threshold
        self.mitigation_test_pips = mitigation_test_pips
        self.min_rejection_body_pct = min_rejection_body_pct
        self.rejection_confirmation_candles = rejection_confirmation_candles
        
        logger.info(f"🚫 ICT Rejection Block Analyzer başlatıldı - "
                   f"Rejection Threshold: {rejection_strength_threshold}%, "
                   f"Test Pips: {mitigation_test_pips}, "
                   f"Min Body: {min_rejection_body_pct}%")

    def analyze(self, candles: pd.DataFrame, 
                order_blocks: List[Dict[str, Any]],
                breaker_blocks: List[Dict[str, Any]] = None) -> Dict[str, Any]:
        """
        Ana rejection block analizi.
        
        Args:
            candles: Mum verileri
            order_blocks: Order Block analiz sonuçları
            breaker_blocks: Breaker Block analiz sonuçları (opsiyonel)
            
        Returns:
            Rejection block analiz sonuçları
        """
        if candles is None or len(candles) < 20:
            logger.warning("Rejection block analizi için yetersiz mum verisi")
            return self._empty_result()
        
        if not order_blocks:
            logger.debug("Rejection block analizi için Order Block verisi yok, analiz atlanıyor")
            return self._empty_result()
        
        logger.info(f"🚫 Rejection Block analizi başlıyor - "
                   f"{len(candles)} mum, {len(order_blocks)} OB")
        
        # 1. Mitigation Block Detection
        mitigation_blocks = self._detect_mitigation_blocks(candles, order_blocks)
        
        # 2. Rejection Block Detection  
        rejection_blocks = self._detect_rejection_blocks(candles, order_blocks)
        
        # 3. Failed Mitigation Analysis
        failed_mitigations = self._analyze_failed_mitigations(
            candles, mitigation_blocks, rejection_blocks
        )
        
        # 4. Active Rejection Zones
        active_rejections = self._identify_active_rejection_zones(
            candles, rejection_blocks + failed_mitigations
        )
        
        # 5. Confluence with Breaker Blocks
        confluence_zones = []
        if breaker_blocks:
            confluence_zones = self._find_breaker_rejection_confluence(
                rejection_blocks, breaker_blocks
            )
        
        result = {
            'mitigation_blocks': mitigation_blocks,
            'rejection_blocks': rejection_blocks,
            'failed_mitigations': failed_mitigations,
            'active_rejection_zones': active_rejections,
            'breaker_confluence': confluence_zones,
            'summary': {
                'total_mitigations': len(mitigation_blocks),
                'total_rejections': len(rejection_blocks),
                'failed_mitigations': len(failed_mitigations),
                'active_zones': len(active_rejections),
                'high_quality_rejections': len([r for r in rejection_blocks if r.get('quality') == 'HIGH'])
            },
            'analysis_timestamp': datetime.now().isoformat()
        }
        
        logger.success(f"✅ Rejection Block analizi tamamlandı: "
                      f"Mitigation={len(mitigation_blocks)}, "
                      f"Rejection={len(rejection_blocks)}, "
                      f"Active={len(active_rejections)}")
        
        return result

    def _detect_mitigation_blocks(self, candles: pd.DataFrame,
                                 order_blocks: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """
        Mitigation block'ları tespit eder.
        
        Mitigation: OB'nin kısmi olarak test edilip reddedilmesi
        """
        mitigation_blocks = []
        
        for ob in order_blocks:
            ob_high = float(ob.get('high', 0))
            ob_low = float(ob.get('low', 0))
            ob_type = ob.get('type', '')
            ob_index = ob.get('candle_index', 0)
            
            # OB sonrası mumları analiz et
            post_ob_candles = candles.iloc[ob_index + 1:ob_index + 20]
            
            for i, (idx, candle) in enumerate(post_ob_candles.iterrows()):
                mitigation_result = self._check_mitigation_at_candle(
                    candle, ob_high, ob_low, ob_type
                )
                
                if mitigation_result:
                    mitigation_block = {
                        'type': f"{ob_type}_MITIGATION",
                        'direction': ob_type,
                        'original_ob': ob,
                        'mitigation_candle_index': ob_index + 1 + i,
                        'mitigation_candle': candle.to_dict(),
                        'test_level': mitigation_result['test_level'],
                        'rejection_strength': mitigation_result['rejection_strength'],
                        'quality': self._calculate_mitigation_quality(mitigation_result, candle),
                        'timestamp': candle.get('timestamp', datetime.now()),
                        'status': 'ACTIVE'
                    }
                    mitigation_blocks.append(mitigation_block)
                    
                    logger.debug(f"🔄 {ob_type} Mitigation tespit edildi: "
                               f"Level={mitigation_result['test_level']:.6f}")
                    break  # Bir OB için sadece bir mitigation
        
        return mitigation_blocks

    def _detect_rejection_blocks(self, candles: pd.DataFrame,
                                order_blocks: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """
        Rejection block'ları tespit eder.
        
        Rejection: OB'nin tamamen ihlal edilip ardından güçlü reddetme
        """
        rejection_blocks = []
        
        for ob in order_blocks:
            ob_high = float(ob.get('high', 0))
            ob_low = float(ob.get('low', 0))
            ob_type = ob.get('type', '')
            ob_index = ob.get('candle_index', 0)
            
            # OB sonrası mumları analiz et
            post_ob_candles = candles.iloc[ob_index + 1:ob_index + 30]
            
            violation_detected = False
            violation_candle = None
            violation_index = None
            
            # 1. OB ihlalini tespit et
            for i, (idx, candle) in enumerate(post_ob_candles.iterrows()):
                if self._check_ob_violation(candle, ob_high, ob_low, ob_type):
                    violation_detected = True
                    violation_candle = candle
                    violation_index = ob_index + 1 + i
                    break
            
            if not violation_detected:
                continue
            
            # 2. İhlal sonrası rejection'ı kontrol et
            post_violation_candles = candles.iloc[violation_index + 1:violation_index + 10]
            
            for j, (idx, candle) in enumerate(post_violation_candles.iterrows()):
                rejection_result = self._check_rejection_after_violation(
                    candle, violation_candle, ob_high, ob_low, ob_type
                )
                
                if rejection_result:
                    rejection_block = {
                        'type': f"{ob_type}_REJECTION",
                        'direction': 'BULLISH' if ob_type == 'BEARISH' else 'BEARISH',  # Ters yön
                        'original_ob': ob,
                        'violation_candle_index': violation_index,
                        'violation_candle': violation_candle.to_dict(),
                        'rejection_candle_index': violation_index + 1 + j,
                        'rejection_candle': candle.to_dict(),
                        'rejection_level': rejection_result['rejection_level'],
                        'rejection_strength': rejection_result['rejection_strength'],
                        'quality': self._calculate_rejection_quality(rejection_result, candle),
                        'timestamp': candle.get('timestamp', datetime.now()),
                        'status': 'ACTIVE'
                    }
                    rejection_blocks.append(rejection_block)
                    
                    logger.info(f"🚫 {ob_type} -> {rejection_block['direction']} Rejection tespit edildi: "
                               f"Level={rejection_result['rejection_level']:.6f}, "
                               f"Strength={rejection_result['rejection_strength']:.2f}%")
                    break  # Bir violation için sadece bir rejection
        
        return rejection_blocks

    def _check_mitigation_at_candle(self, candle: pd.Series, 
                                   ob_high: float, ob_low: float, 
                                   ob_type: str) -> Optional[Dict[str, Any]]:
        """
        Belirli bir mumda mitigation olup olmadığını kontrol eder.
        """
        if ob_type == 'BULLISH':
            # Bullish OB: Fiyat OB'nin alt seviyesini kısmen test edip geri dönmeli
            test_level = ob_low
            if candle['low'] <= test_level + (self.mitigation_test_pips * 0.00001):  # Kısmi test
                if candle['close'] > test_level:  # Geri dönüş
                    rejection_strength = (candle['close'] - candle['low']) / candle['low'] * 100
                    if rejection_strength >= self.rejection_strength_threshold:
                        return {
                            'test_level': test_level,
                            'rejection_strength': rejection_strength,
                            'mitigation_type': 'BULLISH_MITIGATION'
                        }
        
        elif ob_type == 'BEARISH':
            # Bearish OB: Fiyat OB'nin üst seviyesini kısmen test edip geri dönmeli
            test_level = ob_high
            if candle['high'] >= test_level - (self.mitigation_test_pips * 0.00001):  # Kısmi test
                if candle['close'] < test_level:  # Geri dönüş
                    rejection_strength = (candle['high'] - candle['close']) / candle['high'] * 100
                    if rejection_strength >= self.rejection_strength_threshold:
                        return {
                            'test_level': test_level,
                            'rejection_strength': rejection_strength,
                            'mitigation_type': 'BEARISH_MITIGATION'
                        }
        
        return None

    def _check_ob_violation(self, candle: pd.Series, 
                           ob_high: float, ob_low: float, ob_type: str) -> bool:
        """
        Order Block'ın ihlal edilip edilmediğini kontrol eder.
        """
        if ob_type == 'BULLISH':
            # Bullish OB ihlali: Kapanış OB'nin altında
            return candle['close'] < ob_low
        elif ob_type == 'BEARISH':
            # Bearish OB ihlali: Kapanış OB'nin üstünde
            return candle['close'] > ob_high
        
        return False

    def _check_rejection_after_violation(self, candle: pd.Series, 
                                       violation_candle: pd.Series,
                                       ob_high: float, ob_low: float, 
                                       ob_type: str) -> Optional[Dict[str, Any]]:
        """
        İhlal sonrası rejection olup olmadığını kontrol eder.
        """
        # Güçlü body oranı kontrolü
        body_size = abs(candle['close'] - candle['open'])
        total_range = candle['high'] - candle['low']
        
        if total_range == 0:
            return None
        
        body_pct = (body_size / total_range) * 100
        if body_pct < self.min_rejection_body_pct:
            return None
        
        if ob_type == 'BULLISH':
            # Bullish OB ihlal sonrası: Güçlü bullish rejection mumu beklenir
            if candle['close'] > candle['open']:  # Bullish mum
                rejection_level = candle['low']
                rejection_strength = (candle['close'] - candle['low']) / candle['low'] * 100
                
                if rejection_strength >= self.rejection_strength_threshold:
                    return {
                        'rejection_level': rejection_level,
                        'rejection_strength': rejection_strength,
                        'rejection_type': 'BULLISH_REJECTION'
                    }
        
        elif ob_type == 'BEARISH':
            # Bearish OB ihlal sonrası: Güçlü bearish rejection mumu beklenir
            if candle['close'] < candle['open']:  # Bearish mum
                rejection_level = candle['high']
                rejection_strength = (candle['high'] - candle['close']) / candle['high'] * 100
                
                if rejection_strength >= self.rejection_strength_threshold:
                    return {
                        'rejection_level': rejection_level,
                        'rejection_strength': rejection_strength,
                        'rejection_type': 'BEARISH_REJECTION'
                    }
        
        return None

    def _calculate_mitigation_quality(self, mitigation_result: Dict[str, Any], 
                                     candle: pd.Series) -> str:
        """
        Mitigation kalitesini hesaplar.
        """
        rejection_strength = mitigation_result.get('rejection_strength', 0)
        volume = candle.get('volume', 1)
        
        if rejection_strength >= 3.0 and volume > 500000:
            return 'HIGH'
        elif rejection_strength >= 2.0:
            return 'MEDIUM'
        else:
            return 'LOW'

    def _calculate_rejection_quality(self, rejection_result: Dict[str, Any], 
                                    candle: pd.Series) -> str:
        """
        Rejection kalitesini hesaplar.
        """
        rejection_strength = rejection_result.get('rejection_strength', 0)
        volume = candle.get('volume', 1)
        
        # Body size kontrolü
        body_size = abs(candle['close'] - candle['open'])
        total_range = candle['high'] - candle['low']
        body_pct = (body_size / total_range) * 100 if total_range > 0 else 0
        
        if rejection_strength >= 4.0 and body_pct >= 80 and volume > 1000000:
            return 'HIGH'
        elif rejection_strength >= 2.5 and body_pct >= 70:
            return 'MEDIUM'
        else:
            return 'LOW'

    def _analyze_failed_mitigations(self, candles: pd.DataFrame,
                                   mitigation_blocks: List[Dict[str, Any]],
                                   rejection_blocks: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """
        Başarısız mitigation'ları analiz eder.
        
        Failed Mitigation: Mitigation'ın test edilip kırılması
        """
        failed_mitigations = []
        
        for mitigation in mitigation_blocks:
            mitigation_index = mitigation.get('mitigation_candle_index', 0)
            mitigation_level = mitigation.get('test_level', 0)
            mitigation_type = mitigation.get('direction', '')
            
            # Mitigation sonrası mumları kontrol et
            post_mitigation_candles = candles.iloc[mitigation_index + 1:mitigation_index + 15]
            
            for i, (idx, candle) in enumerate(post_mitigation_candles.iterrows()):
                if self._check_mitigation_failure(candle, mitigation_level, mitigation_type):
                    failed_mitigation = {
                        'type': f"FAILED_{mitigation_type}_MITIGATION",
                        'original_mitigation': mitigation,
                        'failure_candle_index': mitigation_index + 1 + i,
                        'failure_candle': candle.to_dict(),
                        'failure_level': mitigation_level,
                        'new_direction': 'BEARISH' if mitigation_type == 'BULLISH' else 'BULLISH',
                        'quality': 'HIGH',  # Failed mitigation genellikle güçlü sinyal
                        'timestamp': candle.get('timestamp', datetime.now()),
                        'status': 'ACTIVE'
                    }
                    failed_mitigations.append(failed_mitigation)
                    
                    logger.warning(f"⚠️ Failed {mitigation_type} Mitigation tespit edildi: "
                                 f"Level={mitigation_level:.6f}")
                    break
        
        return failed_mitigations

    def _check_mitigation_failure(self, candle: pd.Series, 
                                 mitigation_level: float, mitigation_type: str) -> bool:
        """
        Mitigation'ın başarısız olup olmadığını kontrol eder.
        """
        if mitigation_type == 'BULLISH':
            # Bullish mitigation başarısızlığı: Kapanış mitigation seviyesinin altında
            return candle['close'] < mitigation_level
        elif mitigation_type == 'BEARISH':
            # Bearish mitigation başarısızlığı: Kapanış mitigation seviyesinin üstünde
            return candle['close'] > mitigation_level
        
        return False

    def _identify_active_rejection_zones(self, candles: pd.DataFrame,
                                        all_rejections: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """
        Aktif rejection zone'ları belirler.
        """
        active_zones = []
        current_price = float(candles.iloc[-1]['close'])
        
        for rejection in all_rejections:
            rejection_level = rejection.get('rejection_level', rejection.get('test_level', 0))
            rejection_type = rejection.get('direction', rejection.get('new_direction', ''))
            
            # Fiyata yakınlık kontrolü (%5 içinde)
            distance_pct = abs(current_price - rejection_level) / current_price * 100
            
            if distance_pct <= 5.0:  # %5 yakınlık
                active_zone = rejection.copy()
                active_zone['distance_from_current'] = distance_pct
                active_zone['proximity_score'] = max(0, 10 - distance_pct * 2)  # 0-10 skor
                active_zones.append(active_zone)
        
        # Yakınlığa göre sırala
        active_zones.sort(key=lambda x: x['distance_from_current'])
        
        return active_zones

    def _find_breaker_rejection_confluence(self, rejection_blocks: List[Dict[str, Any]],
                                          breaker_blocks: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """
        Rejection block'lar ile breaker block'lar arasında confluence bulur.
        """
        confluence_zones = []
        
        for rejection in rejection_blocks:
            rejection_level = rejection.get('rejection_level', 0)
            
            for breaker in breaker_blocks:
                breaker_high = float(breaker.get('high', 0))
                breaker_low = float(breaker.get('low', 0))
                
                # Level proximity kontrolü (%0.1 tolerance)
                tolerance = rejection_level * 0.001
                
                if (breaker_low - tolerance <= rejection_level <= breaker_high + tolerance):
                    confluence = {
                        'type': 'REJECTION_BREAKER_CONFLUENCE',
                        'rejection_data': rejection,
                        'breaker_data': breaker,
                        'confluence_level': rejection_level,
                        'quality': 'HIGH',  # Confluence her zaman yüksek kalite
                        'timestamp': datetime.now().isoformat()
                    }
                    confluence_zones.append(confluence)
                    
                    logger.info(f"🎯 Rejection-Breaker Confluence tespit edildi: "
                               f"Level={rejection_level:.6f}")
        
        return confluence_zones

    def _empty_result(self) -> Dict[str, Any]:
        """
        Boş analiz sonucu döner.
        """
        return {
            'mitigation_blocks': [],
            'rejection_blocks': [],
            'failed_mitigations': [],
            'active_rejection_zones': [],
            'breaker_confluence': [],
            'summary': {
                'total_mitigations': 0,
                'total_rejections': 0,
                'failed_mitigations': 0,
                'active_zones': 0,
                'high_quality_rejections': 0
            },
            'analysis_timestamp': datetime.now().isoformat()
        }
