# risk_manager.py

from loguru import logger
from typing import Dict, Any, Optional

class RiskManager:
    """
    Ticaret stratejileri için risk yönetimi ve pozisyon boyutlandırma hesaplamalarını yönetir.
    <PERSON><PERSON> sın<PERSON><PERSON>, serma<PERSON>yi korumak ve her işlemde riski kontrol altında tutmak için
    gerekli araçları sağlar.
    """

    def __init__(self, capital: float, default_risk_pct: float, leverage: float,
                 maker_fee_pct: float = 0.02, taker_fee_pct: float = 0.05):
        """
        RiskManager'ı başlatır.

        Args:
            capital (float): Başlangıç ticaret sermayesi.
            default_risk_pct (float): Her işlem için varsayılan risk yüzdesi (örn. 1.0 için %1).
            leverage (float): Kullanılacak maksimum kaldıraç.
            maker_fee_pct (float): Piyasa yapıcı işlem ücreti yüzdesi.
            taker_fee_pct (float): <PERSON>yasa alıcı işlem ücreti yüzdesi.
        """
        if capital <= 0:
            raise ValueError("Sermaye pozitif bir değer olmalıdır.")
        if not (0 < default_risk_pct <= 100):
            raise ValueError("Varsayılan risk yüzdesi 0 ile 100 arasında olmalıdır.")

        self.capital = capital
        self.default_risk_pct = default_risk_pct
        self.leverage = leverage
        self.maker_fee_pct = maker_fee_pct
        self.taker_fee_pct = taker_fee_pct
        logger.info(f"RiskManager başlatıldı. Sermaye: ${capital:,.2f}, Varsayılan Risk: {default_risk_pct}%, Kaldıraç: {leverage}x")

    def calculate_position_size(self, entry_price: float, sl_price: float, risk_pct: Optional[float] = None) -> Optional[float]:
        """
        Verilen giriş ve stop-loss seviyelerine göre pozisyon büyüklüğünü (coin miktarı) hesaplar.

        Args:
            entry_price (float): İşleme giriş fiyatı.
            sl_price (float): Stop-loss fiyatı.
            risk_pct (Optional[float]): Bu işlem için özel risk yüzdesi. Belirtilmezse varsayılan kullanılır.

        Returns:
            Optional[float]: Hesaplanan pozisyon büyüklüğü (coin miktarı) veya hata durumunda None.
        """
        # ✅ DÜZELTME: None değer kontrolü eklendi
        if entry_price is None or sl_price is None:
            logger.error("Giriş ve stop-loss fiyatları None olamaz.")
            return None

        if entry_price <= 0 or sl_price <= 0:
            logger.error("Giriş ve stop-loss fiyatları pozitif olmalıdır.")
            return None
        if entry_price == sl_price:
            logger.error("Giriş ve stop-loss fiyatları aynı olamaz.")
            return None

        active_risk_pct = risk_pct if risk_pct is not None else self.default_risk_pct

        # 1. Riske atılacak Dolar miktarını hesapla
        risk_amount_per_trade_usd = self.capital * (active_risk_pct / 100.0)

        # 2. Coin başına riski (stop mesafesi) hesapla
        risk_per_coin_usd = abs(entry_price - sl_price)
        if risk_per_coin_usd == 0:
            return None

        # 3. Temel pozisyon büyüklüğünü (coin miktarı) hesapla
        position_size_coin = risk_amount_per_trade_usd / risk_per_coin_usd

        logger.info(f"Pozisyon Boyutu Hesaplaması: Risk Edilecek Miktar=${risk_amount_per_trade_usd:.2f}, "
                    f"Coin Başına Risk=${risk_per_coin_usd:.4f}, Pozisyon Büyüklüğü={position_size_coin:.4f} coin")

        return position_size_coin

    def calculate_sl_tp_levels(self, entry_price: float, risk_per_coin: float, direction: str, rr_ratios: Dict[str, float]) -> Dict[str, float]:
        """
        Giriş fiyatı ve risk/ödül oranlarına göre stop-loss ve take-profit seviyelerini hesaplar.
        Bu fonksiyon, SL/TP hesaplamasını merkezileştirir.

        Args:
            entry_price (float): İşleme giriş fiyatı.
            risk_per_coin (float): Coin başına risk (stop mesafesi).
            direction (str): İşlem yönü ('bullish' veya 'bearish').
            rr_ratios (Dict[str, float]): Risk/Ödül oranlarını içeren sözlük (örn. {'tp1': 1.5, 'tp2': 2.0}).

        Returns:
            Dict[str, float]: Hesaplanan SL ve TP seviyelerini içeren sözlük.
        """
        levels = {}
        if direction.lower() == 'bullish':
            levels['stop_loss'] = entry_price - risk_per_coin
            for tp_name, ratio in rr_ratios.items():
                levels[tp_name] = entry_price + (risk_per_coin * ratio)
        elif direction.lower() == 'bearish':
            levels['stop_loss'] = entry_price + risk_per_coin
            for tp_name, ratio in rr_ratios.items():
                levels[tp_name] = entry_price - (risk_per_coin * ratio)
        else:
            logger.error(f"Geçersiz işlem yönü: {direction}")
            return {}

        logger.debug(f"Hesaplanan Seviyeler ({direction}): Giriş={entry_price:.4f}, SL={levels['stop_loss']:.4f}, TPs={ {k: f'{v:.4f}' for k, v in levels.items() if 'tp' in k} }")
        return levels

    def set_stoploss_to_entry(self, trade: Dict[str, Any], current_price: float, rr_target: float = 1.0) -> Optional[Dict[str, Any]]:
        """
        Belirli bir R:R hedefine ulaşıldığında stop-loss'u giriş seviyesine taşır.
        Rapordaki "Set Stoploss to Entry at R:R" özelliğini karşılar.

        Args:
            trade (Dict[str, Any]): Aktif işlem bilgileri. 'entry_price', 'stop_loss', 'direction' içermelidir.
            current_price (float): Mevcut piyasa fiyatı.
            rr_target (float): SL'nin girişe taşınacağı R:R hedefi.

        Returns:
            Optional[Dict[str, Any]]: Güncellenmiş işlem bilgileri veya bir değişiklik yoksa None.
        """
        entry_price = trade.get('entry_price')
        initial_sl = trade.get('initial_stop_loss') # Orijinal SL'i kullanmalıyız
        direction = trade.get('direction')

        if not all([entry_price, initial_sl, direction]):
            logger.warning("İşlemde eksik bilgi, SL yönetimi yapılamıyor.")
            return None

        initial_risk_per_coin = abs(entry_price - initial_sl)
        target_profit = initial_risk_per_coin * rr_target

        if direction.lower() == 'bullish':
            if current_price >= entry_price + target_profit:
                if trade['stop_loss'] != entry_price:
                    trade['stop_loss'] = entry_price
                    logger.info(f"Risk ÜCRETSİZ! {trade['symbol']} için SL girişe ({entry_price}) taşındı ({rr_target}R hedefine ulaşıldı).")
                    return trade
        elif direction.lower() == 'bearish':
            if current_price <= entry_price - target_profit:
                if trade['stop_loss'] != entry_price:
                    trade['stop_loss'] = entry_price
                    logger.info(f"Risk ÜCRETSİZ! {trade['symbol']} için SL girişe ({entry_price}) taşındı ({rr_target}R hedefine ulaşıldı).")
                    return trade

        return None

    def update_capital(self, pnl: float):
        """
        Bir işlem kapandıktan sonra sermayeyi günceller.

        Args:
            pnl (float): İşlemin kar veya zararı (pozitif veya negatif).
        """
        old_capital = self.capital
        self.capital += pnl
        logger.info(f"Sermaye güncellendi. Eski: ${old_capital:,.2f}, PNL: ${pnl:,.2f}, Yeni: ${self.capital:,.2f}")

    def get_adjusted_risk_for_signal_type(self, signal_type: str, base_risk_pct: Optional[float] = None) -> float:
        """
        Sinyal türüne göre risk yüzdesini ayarlar.
        Yeni gelişmiş ICT stratejileri için optimized risk seviyeleri kullanır.

        Args:
            signal_type (str): Sinyal türü (örn. 'HTF_POI_LTF_MSS', 'LIQUIDITY_HUNT_WEAK_STRONG')
            base_risk_pct (Optional[float]): Temel risk yüzdesi. Belirtilmezse varsayılan kullanılır.

        Returns:
            float: Ayarlanmış risk yüzdesi
        """
        if base_risk_pct is None:
            base_risk_pct = self.default_risk_pct

        # Yeni gelişmiş ICT stratejileri için özel risk profilleri
        if signal_type == 'KILLZONE_SESSION_MANIPULATION':
            # Killzone + Session Manipulation: En yüksek confluence, maksimum agresif
            adjusted_risk = base_risk_pct * 1.3
            logger.info(f"Killzone + Session Manipulation sinyali için risk %{base_risk_pct:.1f} -> %{adjusted_risk:.1f} artırıldı")
            return min(adjusted_risk, base_risk_pct * 1.6)  # Maksimum %60 artış
            
        elif signal_type == 'HTF_POI_LTF_MSS':
            # HTF POI + LTF MSS: Yüksek confluence, biraz daha agresif
            adjusted_risk = base_risk_pct * 1.2
            logger.info(f"HTF POI + LTF MSS sinyali için risk %{base_risk_pct:.1f} -> %{adjusted_risk:.1f} artırıldı")
            return min(adjusted_risk, base_risk_pct * 1.5)  # Maksimum %50 artış
            
        elif signal_type == 'LIQUIDITY_HUNT_WEAK_STRONG':
            # Likidite Avı + Swing: Güvenilir Smart Money tracking
            adjusted_risk = base_risk_pct * 1.1
            logger.info(f"Likidite Avı + Swing sinyali için risk %{base_risk_pct:.1f} -> %{adjusted_risk:.1f} artırıldı")
            return min(adjusted_risk, base_risk_pct * 1.3)  # Maksimum %30 artış
            
        elif signal_type in ['LIQSFP_REV', 'MONDAY_RANGE_REVERSAL']:
            # Yüksek kaliteli geleneksel sinyaller
            adjusted_risk = base_risk_pct * 1.05
            logger.info(f"{signal_type} sinyali için risk %{base_risk_pct:.1f} -> %{adjusted_risk:.1f} hafifçe artırıldı")
            return adjusted_risk
            
        elif signal_type in ['BRKR12_BOS1', 'OB12_BOS1']:
            # Confluence stratejileri - standart risk
            logger.info(f"{signal_type} sinyali için standart risk %{base_risk_pct:.1f} kullanılıyor")
            return base_risk_pct
            
        else:
            # Diğer sinyaller için konservatif yaklaşım
            adjusted_risk = base_risk_pct * 0.8
            logger.info(f"Genel sinyal türü için risk %{base_risk_pct:.1f} -> %{adjusted_risk:.1f} azaltıldı")
            return adjusted_risk
