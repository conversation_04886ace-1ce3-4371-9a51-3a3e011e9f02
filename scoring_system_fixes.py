# scoring_system.py için SSoT düzeltmeleri

# ❌ YANLIŞ: Ana<PERSON>z yapan metodlar
def _check_volume_imbalance_signal(self, symbol: str, all_symbol_data: Dict[str, Any]) -> Optional[Dict[str, Any]]:
    # Bu metod volume imbalance analizi yapıyor - volume_imbalance_analyzer.py'de olmalı
    pass

def _check_rejection_block_signal(self, symbol: str, all_symbol_data: Dict[str, Any]) -> Optional[Dict[str, Any]]:
    # Bu metod rejection block analizi yapıyor - rejection_block_analyzer.py'de olmalı
    pass

def _check_turtle_soup_ifvg_signal(self, symbol: str, all_symbol_data: Dict[str, Any]) -> Optional[Dict[str, Any]]:
    # Bu metod turtle soup analizi yapıyor - turtle_soup_ifvg_analyzer.py'de olmalı
    pass

def _check_breaker_retest_signal(self, symbol: str, all_symbol_data: Dict[str, Any]) -> Optional[Dict[str, Any]]:
    # Bu metod breaker retest analizi yapıyor - breaker_block_analyzer.py'de olmalı
    pass

# ✅ DOĞRU: Sadece önceden analiz edilmiş veriyi tüketen metodlar
def _consume_volume_imbalance_signals(self, symbol: str, all_symbol_data: Dict[str, Any]) -> Optional[Dict[str, Any]]:
    """Volume Imbalance Analyzer'dan gelen hazır sinyalleri tüketir"""
    vi_analysis = all_symbol_data.get('volume_imbalance_analysis', {})
    signals = vi_analysis.get('signals', [])
    high_quality_signals = [s for s in signals if s.get('quality_score', 0) >= 8]
    
    if high_quality_signals:
        return max(high_quality_signals, key=lambda x: x.get('quality_score', 0))
    return None

def _consume_rejection_block_signals(self, symbol: str, all_symbol_data: Dict[str, Any]) -> Optional[Dict[str, Any]]:
    """Rejection Block Analyzer'dan gelen hazır sinyalleri tüketir"""
    rb_analysis = all_symbol_data.get('rejection_block_analysis', {})
    signals = rb_analysis.get('signals', [])
    high_quality_signals = [s for s in signals if s.get('strength', 0) >= 7]
    
    if high_quality_signals:
        return max(high_quality_signals, key=lambda x: x.get('strength', 0))
    return None