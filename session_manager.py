# session_manager.py

import pytz
from datetime import datetime, time
from typing import Dict, Optional, Tuple, Any
from loguru import logger

class SessionManager:
    """
    Forex oturum yöneticisi - ICT killzone'ları ve session manipulation tespiti
    Yaz saati (DST) uyumlu session saatleri
    """
    
    def __init__(self, config: Optional[Dict] = None):
        """
        SessionManager'ı başlatır.
        
        Args:
            config (Optional[Dict]): Eski uyumluluk için config parametresi.
                                    Yeni sistemde yaz saati saatleri sabit kodlanmıştır.
        """
        self.utc_tz = pytz.UTC
        
        # Eski config sistemi ile uyumluluk için
        if config:
            logger.info("Config parametresi alındı ancak yeni sistem sabit yaz saati saatlerini kullanıyor.")
        
        # YAZ SAATİ (DST) SESSION SAATLERİ - UTC bazında
        self.sessions = {
            'sydney': {
                'open': time(21, 0),    # 21:00 UTC (<PERSON><PERSON> saati)
                'close': time(6, 0),    # 06:00 UTC (Ya<PERSON> saati)
                'name': '<PERSON>',
                'importance': 'low'
            },
            'tokyo': {
                'open': time(0, 0),     # 00:00 UTC (DST yok)
                'close': time(9, 0),    # 09:00 UTC (DST yok)
                'name': 'Tokyo (Asya)',
                'importance': 'medium'
            },
            'london': {
                'open': time(7, 0),     # 07:00 UTC (Yaz saati)
                'close': time(16, 0),   # 16:00 UTC (Yaz saati)
                'name': 'London (Avrupa)',
                'importance': 'high'
            },
            'new_york': {
                'open': time(12, 0),    # 12:00 UTC (Yaz saati)
                'close': time(21, 0),   # 21:00 UTC (Yaz saati)
                'name': 'New York (ABD)',
                'importance': 'high'
            }
        }
        
        # ICT KILLZONE SAATLERİ (UTC bazında - yaz saati)
        self.killzones = {
            'london_killzone': {
                'start': time(6, 0),    # 06:00-09:00 UTC (London açılış öncesi)
                'end': time(9, 0),
                'name': 'London Killzone',
                'importance': 'very_high',
                'description': 'London açılış likidite avı'
            },
            'new_york_am_killzone': {
                'start': time(11, 30),  # 11:30-12:30 UTC (NY açılış öncesi)
                'end': time(12, 30),
                'name': 'New York AM Killzone',
                'importance': 'very_high',
                'description': 'New York açılış likidite avı'
            },
            'new_york_lunch_killzone': {
                'start': time(15, 30),  # 15:30-16:30 UTC (NY öğle arası)
                'end': time(16, 30),
                'name': 'New York Lunch Killzone',
                'importance': 'high',
                'description': 'New York öğle manipulation'
            },
            'london_close_killzone': {
                'start': time(15, 0),   # 15:00-17:00 UTC (London kapanış)
                'end': time(17, 0),
                'name': 'London Close Killzone',
                'importance': 'high',
                'description': 'London kapanış manipulation'
            }
        }
        
        # SESSION OVERLAP'LERİ (En yüksek likidite)
        self.overlaps = {
            'london_new_york': {
                'start': time(12, 0),   # 12:00-16:00 UTC (Yaz saati)
                'end': time(16, 0),
                'name': 'London-New York Overlap',
                'importance': 'very_high',
                'description': 'En yüksek likidite dönemi'
            },
            'tokyo_london': {
                'start': time(7, 0),    # 07:00-09:00 UTC
                'end': time(9, 0),
                'name': 'Tokyo-London Overlap',
                'importance': 'medium',
                'description': 'Orta düzey likidite'
            },
            'sydney_tokyo': {
                'start': time(0, 0),    # 00:00-06:00 UTC
                'end': time(6, 0),
                'name': 'Sydney-Tokyo Overlap',
                'importance': 'low',
                'description': 'Düşük volatilite dönemi'
            }
        }
        
        logger.info("SessionManager başlatıldı - YAZ SAATİ (DST) modunda")
        self._log_session_times()
    
    def _log_session_times(self):
        """Session saatlerini logla"""
        logger.info("=== YAZ SAATİ (DST) SESSION SAATLERİ ===")
        for session_name, session_data in self.sessions.items():
            logger.info(f"{session_data['name']}: {session_data['open'].strftime('%H:%M')} - {session_data['close'].strftime('%H:%M')} UTC")
        
        logger.info("=== ICT KILLZONE SAATLERİ ===")
        for kz_name, kz_data in self.killzones.items():
            logger.info(f"{kz_data['name']}: {kz_data['start'].strftime('%H:%M')} - {kz_data['end'].strftime('%H:%M')} UTC")
    
    def _get_current_time(self, current_time: Optional[datetime] = None) -> Tuple[datetime, time]:
        """Mevcut zamanı normalize eder - DRY prensibi için ortak fonksiyon"""
        if current_time is None:
            current_time = datetime.now(self.utc_tz)
        return current_time, current_time.time()
    
    def _create_time_period_dict(self, name: str, data: Dict) -> Dict:
        """Time period dictionary oluşturur - DRY prensibi için ortak fonksiyon"""
        return {
            'name': name,
            'display_name': data['name'],
            'importance': data['importance'],
            'description': data['description'],
            'start_time': data['start'].strftime('%H:%M'),
            'end_time': data['end'].strftime('%H:%M')
        }
    
    def get_current_session(self, current_time: Optional[datetime] = None) -> Dict:
        """Mevcut aktif session'ı döndürür"""
        current_time, current_time_only = self._get_current_time(current_time)
        active_sessions = []
        
        for session_name, session_data in self.sessions.items():
            if self._is_time_in_range(current_time_only, session_data['open'], session_data['close']):
                active_sessions.append({
                    'name': session_name,
                    'display_name': session_data['name'],
                    'importance': session_data['importance']
                })
        
        return {
            'active_sessions': active_sessions,
            'primary_session': active_sessions[0] if active_sessions else None,
            'current_time_utc': current_time_only.strftime('%H:%M')
        }
    
    def get_current_killzone(self, current_time: Optional[datetime] = None) -> Optional[Dict]:
        """Mevcut aktif killzone'u döndürür"""
        current_time, current_time_only = self._get_current_time(current_time)
        
        for kz_name, kz_data in self.killzones.items():
            if self._is_time_in_range(current_time_only, kz_data['start'], kz_data['end']):
                return self._create_time_period_dict(kz_name, kz_data)
        
        return None
    
    def get_current_overlap(self, current_time: Optional[datetime] = None) -> Optional[Dict]:
        """Mevcut aktif overlap'i döndürür"""
        current_time, current_time_only = self._get_current_time(current_time)
        
        for overlap_name, overlap_data in self.overlaps.items():
            if self._is_time_in_range(current_time_only, overlap_data['start'], overlap_data['end']):
                return self._create_time_period_dict(overlap_name, overlap_data)
        
        return None
    
    def _is_time_in_range(self, current_time: time, start_time: time, end_time: time) -> bool:
        """Zamanın belirli aralıkta olup olmadığını kontrol eder (gece geçiş dahil)"""
        if start_time <= end_time:
            # Normal aralık (08:00-17:00 gibi)
            return start_time <= current_time <= end_time
        else:
            # Gece geçen aralık (22:00-07:00 gibi)
            return current_time >= start_time or current_time <= end_time
    
    def is_high_impact_time(self, current_time: Optional[datetime] = None) -> Dict:
        """Mevcut zamanın yüksek etkili olup olmadığını kontrol eder"""
        current_session = self.get_current_session(current_time)
        current_killzone = self.get_current_killzone(current_time)
        current_overlap = self.get_current_overlap(current_time)
        
        # Impact seviyesi hesapla
        impact_score = 0
        impact_reasons = []
        
        if current_killzone:
            if current_killzone['importance'] == 'very_high':
                impact_score += 40
                impact_reasons.append(f"🔥 {current_killzone['display_name']} Aktif")
            elif current_killzone['importance'] == 'high':
                impact_score += 25
                impact_reasons.append(f"⚡ {current_killzone['display_name']} Aktif")
        
        if current_overlap:
            if current_overlap['importance'] == 'very_high':
                impact_score += 35
                impact_reasons.append(f"🌟 {current_overlap['display_name']} Aktif")
            elif current_overlap['importance'] == 'medium':
                impact_score += 20
                impact_reasons.append(f"📊 {current_overlap['display_name']} Aktif")
        
        # Session önem derecesi
        if current_session['active_sessions']:
            primary = current_session['primary_session']
            if primary and primary['importance'] == 'high':
                impact_score += 15
                impact_reasons.append(f"📈 {primary['name'].title()} Session Aktif")
        
        return {
            'is_high_impact': impact_score >= 50,
            'impact_score': impact_score,
            'impact_level': self._get_impact_level(impact_score),
            'reasons': impact_reasons,
            'current_session': current_session,
            'current_killzone': current_killzone,
            'current_overlap': current_overlap
        }
    
    def _get_impact_level(self, score: int) -> str:
        """Impact skoruna göre seviye döndürür"""
        if score >= 70:
            return 'ÇOK YÜKSEK'
        elif score >= 50:
            return 'YÜKSEK'
        elif score >= 30:
            return 'ORTA'
        elif score >= 15:
            return 'DÜŞÜK'
        else:
            return 'ÇOK DÜŞÜK'
    
    def get_next_killzone(self, current_time: Optional[datetime] = None) -> Optional[Dict]:
        """Bir sonraki killzone'u döndürür"""
        current_time, current_time_only = self._get_current_time(current_time)
        next_kz = None
        min_time_diff = None
        
        for kz_name, kz_data in self.killzones.items():
            start_time = kz_data['start']
            
            # Zaman farkını hesapla (gece geçişi dahil)
            if start_time > current_time_only:
                # Aynı gün içinde
                time_diff = (datetime.combine(datetime.today(), start_time) - 
                           datetime.combine(datetime.today(), current_time_only)).total_seconds()
            else:
                # Ertesi gün
                time_diff = (datetime.combine(datetime.today(), start_time) - 
                           datetime.combine(datetime.today(), current_time_only)).total_seconds() + 86400
            
            if min_time_diff is None or time_diff < min_time_diff:
                min_time_diff = time_diff
                next_kz = {
                    'name': kz_name,
                    'display_name': kz_data['name'],
                    'start_time': start_time.strftime('%H:%M'),
                    'end_time': kz_data['end'].strftime('%H:%M'),
                    'hours_until': time_diff / 3600
                }
        
        return next_kz
    
    def is_trade_allowed(self, check_time_utc: Optional[datetime] = None) -> bool:
        """
        Ticaretin izin verilen bir zaman diliminde olup olmadığını kontrol eder.
        Yüksek impact zamanları tercih eder.
        """
        impact_analysis = self.is_high_impact_time(check_time_utc)
        
        # En az ORTA seviye impact gerekli
        if impact_analysis['impact_score'] >= 30:
            return True
        
        logger.debug(f"Düşük impact zamanı: {impact_analysis['impact_level']} ({impact_analysis['impact_score']}/100)")
        return False
    
    def _is_session_active(self, session_name: str, check_time_utc: Optional[datetime] = None) -> bool:
        """Belirtilen session'ın aktif olup olmadığını kontrol eder - DRY prensibi için ortak fonksiyon"""
        _, current_time_only = self._get_current_time(check_time_utc)
        session = self.sessions[session_name]
        return self._is_time_in_range(current_time_only, session['open'], session['close'])
    
    def is_london_session(self, check_time_utc: Optional[datetime] = None) -> bool:
        """Mevcut zamanın Londra seansında olup olmadığını kontrol eder"""
        return self._is_session_active('london', check_time_utc)
    
    def is_ny_session(self, check_time_utc: Optional[datetime] = None) -> bool:
        """Mevcut zamanın New York seansında olup olmadığını kontrol eder"""
        return self._is_session_active('new_york', check_time_utc)
    
    def is_asia_session(self, check_time_utc: Optional[datetime] = None) -> bool:
        """Mevcut zamanın Tokyo/Asya seansında olup olmadığını kontrol eder"""
        return self._is_session_active('tokyo', check_time_utc)
