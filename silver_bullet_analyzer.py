# silver_bullet_analyzer.py

from datetime import datetime
from datetime import time
from typing import Dict, List, Optional, Any
import pandas as pd
from loguru import logger

from session_manager import SessionManager


class SilverBulletAnalyzer:
    """
    ICT Silver Bullet ticaret modelini analiz eder.

    Bu model, beli<PERSON><PERSON> zaman pencerele<PERSON> (Killzone'lar) gerçekleşen
    likidite avlarını ve ardından oluşan FVG'leri hedefler.
    """

    def __init__(self, session_manager: SessionManager):
        """
        SilverBulletAnalyzer'ı başlatır.

        Args:
            session_manager: Aktif seans ve killzone'ları yöneten SessionManager örneği.
        """
        self.session_manager = session_manager
        # Silver Bullet zaman pencereleri (UTC)
        self.silver_bullet_windows = [
            {'name': 'London Open', 'start': time(7, 0), 'end': time(9, 0)},
            {'name': 'New York AM', 'start': time(12, 0), 'end': time(14, 0)},
            {'name': 'New York PM', 'start': time(17, 0), 'end': time(18, 0)},
        ]
        logger.info("SilverBulletAnalyzer başlatıldı.")

    def analyze(self, candles: pd.DataFrame, pivots: List[Dict[str, Any]], fvgs: List[Dict[str, Any]], displacements: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """
        Verilen mum verilerinde Silver Bullet kurulumlarını arar.

        Args:
            candles: Analiz edilecek mum verileri (daha düşük zaman dilimi, örn. 15m, 5m).
            pivots: Önceden hesaplanmış pivot noktaları.
            fvgs: Önceden hesaplanmış FVG'ler.
            displacements: Önceden hesaplanmış Displacement mumları.

        Returns:
            Tespit edilen Silver Bullet sinyallerinin listesi.
        """
        signals = []
        if candles.empty or len(candles) < 20 or not pivots or not fvgs:
            return signals

        candles['timestamp'] = pd.to_datetime(candles['timestamp'])
        last_candle_time = candles.iloc[-1]['timestamp'].time()

        # 1. Zaman Penceresi Kontrolü
        active_window = None
        for window in self.silver_bullet_windows:
            if self.session_manager._is_time_in_range(last_candle_time, window['start'], window['end']):
                active_window = window
                break

        if not active_window:
            return signals

        logger.debug(f"Aktif Silver Bullet penceresi: {active_window['name']}")

        # 2. Yakın Zamandaki Swing Noktalarını Bul
        last_high = next((p for p in reversed(pivots) if p['type'] == 'high'), None)
        last_low = next((p for p in reversed(pivots) if p['type'] == 'low'), None)

        # 3. Likidite Avı ve MSS/Displacement Kontrolü
        # Son 5 mum içinde bir likidite avı olup olmadığını kontrol et
        recent_candles = candles.tail(5)
        last_candle = candles.iloc[-1]

        # Bullish Setup (Sell-side likidite avı)
        if last_low and recent_candles['low'].min() < last_low['price']:
            # Avdan sonra güçlü bir yükseliş (displacement) var mı?
            if last_candle['close'] > last_low['price']:
                # Bu hareket bir FVG oluşturdu mu?
                # FVG'nin, likidite avından SONRA oluştuğunu teyit et
                bullish_fvgs = [
                    fvg for fvg in fvgs 
                    if fvg['type'] == 'bullish' and not fvg.get('filled', False)
                    and pd.to_datetime(fvg['timestamp']) > pd.to_datetime(last_low['timestamp'])
                ]
                if bullish_fvgs:
                    # FVG'yi oluşturan mumun bir displacement olup olmadığını kontrol et
                    fvg_candle_timestamp = bullish_fvgs[-1]['timestamp']
                    is_displacement_candle = any(
                        disp['timestamp'] == fvg_candle_timestamp for disp in displacements if disp.get('direction') == 'bullish'
                    )
                    if is_displacement_candle:
                        signal = self._create_signal('bull', bullish_fvgs[-1], last_low, active_window)
                        signals.append(signal)
                        logger.success(f"BULLISH Silver Bullet sinyali bulundu: {signal}")

        # Bearish Setup (Buy-side likidite avı)
        if last_high and recent_candles['high'].max() > last_high['price']:
            if last_candle['close'] < last_high['price']:
                bearish_fvgs = [
                    fvg for fvg in fvgs 
                    if fvg['type'] == 'bearish' and not fvg.get('filled', False)
                    and pd.to_datetime(fvg['timestamp']) > pd.to_datetime(last_high['timestamp'])
                ]
                if bearish_fvgs:
                    fvg_candle_timestamp = bearish_fvgs[-1]['timestamp']
                    is_displacement_candle = any(
                        disp['timestamp'] == fvg_candle_timestamp for disp in displacements if disp.get('direction') == 'bearish'
                    )
                    if is_displacement_candle:
                        signal = self._create_signal('bear', bearish_fvgs[-1], last_high, active_window)
                        signals.append(signal)
                        logger.success(f"BEARISH Silver Bullet sinyali bulundu: {signal}")

        return signals

    def _create_signal(self, direction: str, fvg: Dict, swept_pivot: Dict, window: Dict) -> Dict:
        return {
            'type': 'SILVER_BULLET',
            'direction': direction,
            'price': (fvg['top'] + fvg['bottom']) / 2, # FVG'nin ortası
            'confidence': 0.90, # Yüksek güvenilirlikli model
            'reason': f"{window['name']} penceresinde likidite avı sonrası FVG oluşumu.",
            'details': {'fvg': fvg, 'swept_pivot': swept_pivot},
            'timestamp': datetime.now().isoformat()
        }