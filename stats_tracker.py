"""
İstatistik Takip <PERSON> (StatsTracker)
-------------------------------------
<PERSON><PERSON>, Automaton tarafından üretilen sinyallerin takibi,
i<PERSON><PERSON> sonuçlarının kaydedilmesi ve performans istatistiklerinin tutulmasından sorumludur.
"""

# (<PERSON>)

import os
import csv
import json
import shutil
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional
from loguru import logger
from utils import format_price_standard
from invalidation_manager import InvalidationManager

# CSV dosyasındaki tüm sütun başlıkları - YENİ TRAILING STOP ALANLARI VE PATTERN INVALIDATION EKLENDİ
SIGNAL_FIELDS = [
    "signal_id", "symbol", "timeframe", "entry_time", "entry_price", "direction",
    "score", "sl_price", "tp_price", "tp1_price", "tp1_5_price", "tp2_price", "tp3_price", 
    "status", "confirmations", "result_time", "result_price", "profit_percentage", 
    "volatility_level", "fib_level", "tp_strategy_used", "sl_type", 
    "sl_percentage", "tp_percentage", "pattern_name", "regime",
    # --- YENİ PATTERN INVALIDATION ALANI ---
    "initial_pivots",
    # --- YENİ TRAILING STOP ALANLARI ---
    "trailing_activation_price", "highest_price_in_trail", "partial_profit_price"
]

class StatsTracker:
    """
    İşlem sinyallerini takip eder, kaydeder ve performans metrikleri üretir.
    """

    def __init__(self, stats_dir: str = "stats", main_timeframe: str = "240", alert_manager: Optional[Any] = None):
        self.stats_dir = stats_dir
        self.main_timeframe = main_timeframe
        os.makedirs(self.stats_dir, exist_ok=True)
        self.backup_dir = os.path.join(self.stats_dir, "backups")
        os.makedirs(self.backup_dir, exist_ok=True)

        self.signals_file = os.path.join(self.stats_dir, "trade_signals.csv")
        self.results_file = os.path.join(self.stats_dir, "trade_results.csv")
        self.metrics_file = os.path.join(self.stats_dir, "performance_metrics.json")
        self.locks_file = os.path.join(self.stats_dir, "structure_locks.json")  # YENİ SATIR
        self.trit_locks_file = os.path.join(self.stats_dir, "trit_locks.json")
        self.pending_setups_file = os.path.join(self.stats_dir, "pending_setups.json")
        
        # InvalidationManager ve AlertManager'ı DI ile al
        self.invalidation_manager = InvalidationManager()
        self.alert_manager = alert_manager
        
        self._initialize_files()
        self.active_signals: Dict[str, Dict[str, Any]] = {}
        self.last_pivot_points: Dict[str, List[Dict[str, Any]]] = {}
        self.signal_cooldown: Dict[str, datetime] = {}

        # self.structure_lockout'ı dosyadan yükleyerek başlat
        self.structure_lockout: Dict[str, List[Dict[str, Any]]] = self._load_structure_locks()  # YENİ SATIR
        # YENİ: TRIT/TRIB için özel kilit sistemi
        self.trit_lockout: Dict[str, Dict[str, Any]] = self._load_trit_locks()
        self.pending_setups = self._load_pending_setups()
        
        self.entry_timeout_hours: int = 20  # Sinyal zaman aşımı için varsayılan süre (saat)

        # Aktif sinyal değişikliklerini takip etmek için
        self.last_signal_count = 0
        self.last_signal_symbols = set()
        self.notification_service = None  # Bildirim servisi referansı

        self._load_active_signals()
        self._update_signal_tracking()  # İlk yüklemede tracking'i güncelle
        logger.info(f"StatsTracker başlatıldı: {len(self.active_signals)} aktif sinyal, {len(self.structure_lockout)} yapısal kilit, {len(self.trit_lockout)} TRIT kilit, {len(self.pending_setups)} bekleyen kurulum yüklendi.")

    def set_notification_service(self, notification_service):
        """Bildirim servisi referansını ayarlar."""
        self.notification_service = notification_service

    def _load_structure_locks(self) -> Dict[str, Any]:
        """Yapısal kilitleri JSON dosyasından yükler."""
        try:
            if os.path.exists(self.locks_file):
                with open(self.locks_file, 'r', encoding='utf-8') as f:
                    # JSON'dan yüklenen pivotlardaki timestamp'leri datetime nesnesine çevir
                    locks_from_file = json.load(f)
                    for key, pivots in locks_from_file.items():
                        for pivot in pivots:
                            if 'timestamp' in pivot:
                                pivot['timestamp'] = datetime.fromisoformat(pivot['timestamp'])
                    logger.info(f"✅ {len(locks_from_file)} yapısal kilit dosyadan yüklendi.")
                    return locks_from_file
        except (IOError, json.JSONDecodeError) as e:
            logger.error(f"❌ Yapısal kilitler dosyası okunamadı: {e}. Boş başlatılıyor.")
        return {}

    def _save_structure_locks(self):
        """Mevcut yapısal kilitleri JSON dosyasına kaydeder."""
        try:
            # Timestamp nesnelerini JSON uyumlu ISO format string'e çevir
            locks_to_save = {}
            for key, pivots in self.structure_lockout.items():
                savable_pivots = []
                for pivot in pivots:
                    savable_pivot = pivot.copy()
                    if 'timestamp' in savable_pivot:
                        savable_pivot['timestamp'] = savable_pivot['timestamp'].isoformat()
                    savable_pivots.append(savable_pivot)
                locks_to_save[key] = savable_pivots

            with open(self.locks_file, 'w', encoding='utf-8') as f:
                json.dump(locks_to_save, f, indent=4)
            logger.debug(f"Yapısal kilitler dosyaya kaydedildi: {self.locks_file}")
        except IOError as e:
            logger.error(f"❌ Yapısal kilitler dosyaya kaydedilemedi: {e}")

    def _load_trit_locks(self) -> Dict[str, Dict[str, Any]]:
        """TRIT/TRIB özel kilitlerini JSON dosyasından yükler."""
        try:
            if os.path.exists(self.trit_locks_file):
                with open(self.trit_locks_file, 'r', encoding='utf-8') as f:
                    locks_from_file = json.load(f)
                    # Timestamp'leri datetime nesnesine çevir
                    for lock_key, lock_data in locks_from_file.items():
                        if 'lock_time' in lock_data and lock_data['lock_time']:
                            lock_data['lock_time'] = datetime.fromisoformat(lock_data['lock_time'])
                        if 'last_swing_time' in lock_data and lock_data['last_swing_time']:
                            lock_data['last_swing_time'] = datetime.fromisoformat(lock_data['last_swing_time'])
                    logger.info(f"✅ {len(locks_from_file)} TRIT kilit dosyadan yüklendi.")
                    return locks_from_file
        except (IOError, json.JSONDecodeError) as e:
            logger.error(f"❌ TRIT kilitler dosyası okunamadı: {e}. Boş başlatılıyor.")
        return {}

    def _save_trit_locks(self):
        """TRIT/TRIB özel kilitlerini JSON dosyasına kaydeder."""
        try:
            locks_to_save = {}
            for lock_key, lock_data in self.trit_lockout.items():
                savable_lock = lock_data.copy()
                if 'lock_time' in savable_lock and isinstance(savable_lock['lock_time'], datetime):
                    savable_lock['lock_time'] = savable_lock['lock_time'].isoformat()
                if 'last_swing_time' in savable_lock and isinstance(savable_lock['last_swing_time'], datetime):
                    savable_lock['last_swing_time'] = savable_lock['last_swing_time'].isoformat()
                locks_to_save[lock_key] = savable_lock

            with open(self.trit_locks_file, 'w', encoding='utf-8') as f:
                json.dump(locks_to_save, f, indent=4, ensure_ascii=False)
            logger.debug(f"TRIT kilitler dosyaya kaydedildi: {self.trit_locks_file}")
        except IOError as e:
            logger.error(f"❌ TRIT kilitler dosyaya kaydedilemedi: {e}")

    def _initialize_files(self):
        """Gerekli CSV ve JSON dosyalarını başlıklarıyla birlikte oluşturur (eğer yoksa)."""
        # CSV dosyalarını oluştur
        for file_path, fields in [(self.signals_file, SIGNAL_FIELDS), (self.results_file, SIGNAL_FIELDS)]:
            try:
                if not os.path.exists(file_path):
                    with open(file_path, 'w', newline='', encoding='utf-8') as f:
                        writer = csv.DictWriter(f, fieldnames=fields)
                        writer.writeheader()
                    logger.info(f"✅ CSV dosyası oluşturuldu: {file_path}")
                else:
                    # Mevcut dosyanın başlıklarını kontrol et
                    self._validate_csv_headers(file_path, fields)
            except IOError as e:
                logger.error(f"❌ {file_path} dosyası oluşturulurken hata: {e}")

        # JSON dosyalarını oluştur
        json_files = [
            (self.metrics_file, {
                "total_signals": 0, "completed_trades": 0, "successful_trades": 0, "failed_trades": 0,
                "success_rate": 0.0, "avg_profit_percentage": 0.0, "total_profit_percentage": 0.0,
                "tp1_hits": 0, "tp1_5_hits": 0, "tp2_hits": 0, "tp3_hits": 0, "pivot_success_hits": 0, "cancelled_trades": 0,
                "best_trade": None, "worst_trade": None, "last_updated": datetime.now().isoformat()
            }),
            (self.locks_file, {}),  # YENİ: Yapısal kilitler dosyası - boş sözlük ile başlat
            (self.trit_locks_file, {}),      # TRIT/TRIB özel kilitler dosyası
            (self.pending_setups_file, {})   # Beklemedeki kurulumlar dosyası
        ]

        for file_path, initial_data in json_files:
            try:
                if not os.path.exists(file_path):
                    with open(file_path, 'w', encoding='utf-8') as f:
                        json.dump(initial_data, f, indent=4)
                    logger.info(f"✅ JSON dosyası oluşturuldu: {file_path}")
            except IOError as e:
                logger.error(f"❌ {file_path} dosyası oluşturulurken hata: {e}")

    def _validate_csv_headers(self, file_path: str, expected_fields: List[str]):
        """CSV dosyasının başlıklarını doğrular ve gerekirse onarır."""
        try:
            if os.path.getsize(file_path) < 10:  # Çok küçük dosya
                logger.warning(f"⚠️  CSV dosyası çok küçük, yeniden oluşturuluyor: {file_path}")
                self._recreate_csv_file(file_path, expected_fields)
                return

            # İlk satırı oku
            with open(file_path, 'r', encoding='utf-8') as f:
                first_line = f.readline().strip()

            # Başlıkları kontrol et
            actual_headers = [h.strip() for h in first_line.split(',')]

            if actual_headers != expected_fields:
                logger.warning(f"⚠️  CSV başlıkları uyumsuz: {file_path}")
                logger.warning(f"📋 Beklenen: {expected_fields}")
                logger.warning(f"📋 Mevcut: {actual_headers}")
                logger.warning("🔧 Dosya yeniden oluşturuluyor...")
                self._recreate_csv_file(file_path, expected_fields)
            else:
                logger.debug(f"✅ CSV başlıkları doğru: {file_path}")

        except Exception as e:
            logger.error(f"❌ CSV başlık kontrolü hatası: {e}")
            self._recreate_csv_file(file_path, expected_fields)

    def _recreate_csv_file(self, file_path: str, fields: List[str]):
        """CSV dosyasını yeniden oluşturur."""
        try:
            # Yedek oluştur
            if os.path.exists(file_path):
                backup_path = f"{file_path}.backup_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
                os.rename(file_path, backup_path)
                logger.info(f"📁 Dosya yedeklendi: {backup_path}")

            # Yeni dosya oluştur
            with open(file_path, 'w', newline='', encoding='utf-8') as f:
                writer = csv.DictWriter(f, fieldnames=fields)
                writer.writeheader()

            logger.info(f"✅ CSV dosyası yeniden oluşturuldu: {file_path}")

        except Exception as e:
            logger.error(f"❌ CSV dosyası yeniden oluşturulurken hata: {e}")

    def _validate_signal_data(self, signal_data: Dict[str, Any]) -> bool:
        """Kaydedilecek sinyal verisinin temel alanlarını doğrular."""
        required_fields = ["symbol", "entry_price", "direction", "timeframe", "sl_price"]
        for field in required_fields:
            if signal_data.get(field) is None:
                logger.warning(f"Sinyal validasyon hatası: Zorunlu alan eksik veya None -> '{field}'")
                return False
        return True

    def generate_signal_id(self, symbol: str, direction: str) -> str:
        """Benzersiz sinyal ID'si oluşturur."""
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        return f"{symbol}_{timestamp}_{direction}"

    def record_signal(self, score_data: Dict[str, Any]) -> Optional[str]:
        """Yeni bir sinyali tüm detaylarıyla CSV dosyasına kaydeder."""
        try:
            symbol = score_data.get("symbol")
            direction = score_data.get("direction", "UNKNOWN").upper()
            
            logger.info(f"🔍 record_signal çağrıldı: symbol={symbol}, direction={direction}")
            
            # BU SEMBOL İÇİN ZATEN AKTİF SİNYAL VAR MI KONTROL ET
            if self.has_active_signal_for_symbol(symbol):
                logger.warning(f"⚠️  {symbol} için zaten aktif sinyal var, yeni sinyal kaydı atlanıyor.")
                return None

            # --- DÜZELTME BAŞLANGICI ---
            # entry_levels sözlüğünü beklemek yerine, değerleri doğrudan score_data'dan al.
            # STANDARTLAŞTIRMA: major_pivots ismini kullan (swing_points yerine)
            serializable_pivots = []
            initial_pivots = score_data.get("major_pivots", [])
            for p in initial_pivots:
                pivot_copy = p.copy()
                if 'timestamp' in pivot_copy and pivot_copy['timestamp'] is not None:
                    try:
                        # Timestamp'i standart Python datetime objesine çevirip ISO formatına dönüştür
                        pivot_copy['timestamp'] = pd.to_datetime(pivot_copy['timestamp']).isoformat()
                    except Exception as e:
                        logger.warning(f"Pivot timestamp ({pivot_copy['timestamp']}) ISO formatına çevrilemedi: {e}")
                        # Hata durumunda string olarak kaydet
                        pivot_copy['timestamp'] = str(pivot_copy['timestamp'])
                serializable_pivots.append(pivot_copy)
            
            signal_data = {
                "signal_id": self.generate_signal_id(symbol, direction),
                "symbol": symbol,
                "timeframe": score_data.get("timeframe", score_data.get("base_timeframe", "240")),
                "entry_time": datetime.now().isoformat(),
                "status": "PENDING_ENTRY",
                "score": score_data.get("net_score") if score_data.get("net_score") is not None else 0.0,
                "direction": direction,
                "confirmations": str(score_data.get("confirmation_details", [])),
                # Değerleri doğrudan score_data'dan oku
                "entry_price": score_data.get("primary_entry"),
                "sl_price": score_data.get("stop_loss"),
                "tp_price": score_data.get("tp1"),
                "tp1_price": score_data.get("tp1"),
                "tp1_5_price": score_data.get("tp1_5"),
                "tp2_price": score_data.get("tp2"),
                "tp3_price": score_data.get("tp3"),
                "volatility_level": score_data.get("volatility_level", "unknown"),
                "fib_level": score_data.get("fib_level", "unknown"),
                "tp_strategy_used": score_data.get("strategy_used", "unknown"),
                "sl_type": score_data.get("sl_type", "unknown"),
                "sl_percentage": score_data.get("sl_percentage", 0.0),
                "tp_percentage": score_data.get("tp_percentage", 0.0),
                "regime": score_data.get("regime", "UNCLEAR"),
                "initial_pivots": json.dumps(serializable_pivots),
                "result_time": None,
                "result_price": None,
                "profit_percentage": None,
                "trailing_activation_price": None,
                "highest_price_in_trail": None,
                "partial_profit_price": None,
            }
            # --- DÜZELTME SONU ---
            
            # Pattern name'i score_data'dan al
            pattern_name = score_data.get("pattern_name", score_data.get("entry_levels", {}).get("pattern_name", "N/A"))
            
            # Risk yüzdesini doğrudan hesapla
            entry_price = float(signal_data['entry_price']) if signal_data['entry_price'] else 0.0
            sl_price = float(signal_data['sl_price']) if signal_data['sl_price'] else 0.0
            risk_percentage = 0.0
            
            if entry_price > 0 and sl_price > 0:
                if direction in ["BULL", "BULLISH"]:
                    risk_percentage = abs((entry_price - sl_price) / entry_price) * 100
                elif direction in ["BEAR", "BEARISH"]:
                    risk_percentage = abs((sl_price - entry_price) / entry_price) * 100
            
            logger.info(f"🔍 Signal data hazırlandı: {symbol} {direction} | Pattern={pattern_name} | "
                       f"Giriş={format_price_standard(signal_data['entry_price'])} | "
                       f"SL={format_price_standard(signal_data['sl_price'])} | "
                       f"Risk=%{risk_percentage:.2f} | "
                       f"Rejim={signal_data.get('regime')} | Pivotlar={len(serializable_pivots)} | Durum=PENDING_ENTRY")

            if not self._validate_signal_data(signal_data):
                logger.error(f"❌ Sinyal kaydedilemedi, validasyon başarısız: {symbol}. Veri: {signal_data}")
                return None

            # CSV dosyasına yazma işlemini try-catch ile koru
            try:
                file_is_empty = not os.path.exists(self.signals_file) or os.path.getsize(self.signals_file) == 0

                with open(self.signals_file, 'a', newline='', encoding='utf-8') as f:
                    writer = csv.DictWriter(f, fieldnames=SIGNAL_FIELDS, extrasaction='ignore')
                    if file_is_empty:
                        writer.writeheader()
                    writer.writerow(signal_data)

                logger.debug(f"✅ Sinyal CSV dosyasına başarıyla yazıldı: {signal_data['signal_id']}")

            except Exception as csv_error:
                logger.error(f"❌ CSV dosyasına yazma hatası: {csv_error}")
                return None

            # CSV'ye başarıyla yazıldıktan sonra hafızaya ekle
            self.active_signals[signal_data["signal_id"]] = signal_data
            logger.info(f"✅ Yeni sinyal BAŞARIYLA kaydedildi ve hafızaya eklendi: {signal_data['signal_id']}")
            logger.info(f"📊 Mevcut aktif sinyal sayısı: {len(self.active_signals)}")

            # Pattern name'i signal_data'dan al
            pattern_name = score_data.get("entry_levels", {}).get("pattern_name")
            signal_data_with_pattern = signal_data.copy()
            signal_data_with_pattern["pattern_name"] = pattern_name

            self._update_metrics(new_signal=True, signal_data=signal_data_with_pattern)
            
            # _check_and_notify_signal_changes çağrısını kaldırdık - bildirim artık main.py'den gönderilecek
            
            return signal_data["signal_id"]
        except Exception as e:
            logger.error(f"❌ Sinyal kaydı sırasında kritik hata: {e}", exc_info=True)
            return None

    def _load_active_signals(self):
        """CSV'den aktif sinyalleri hafızaya yükler."""
        try:
            if not os.path.exists(self.signals_file) or os.path.getsize(self.signals_file) < 50:
                logger.info("CSV dosyası mevcut değil veya çok küçük, boş aktif sinyal listesi ile başlatılıyor.")
                self.active_signals = {}
                return

            # CSV dosyasını güvenli şekilde oku
            signals_df = pd.read_csv(self.signals_file).fillna('')

            # Başlıklardaki boşlukları ve özel karakterleri temizle
            signals_df.columns = signals_df.columns.str.strip().str.replace('\r', '').str.replace('\n', '')

            # Sütun başlıklarını kontrol et ve logla
            logger.debug(f"CSV sütun başlıkları: {list(signals_df.columns)}")

            # Status sütununun varlığını kontrol et
            if 'status' not in signals_df.columns:
                logger.error(f"❌ KRİTİK HATA: CSV dosyasında 'status' sütunu bulunamadı!")
                logger.error(f"📋 Mevcut sütunlar: {list(signals_df.columns)}")
                logger.error(f"🔧 Beklenen sütunlar: {SIGNAL_FIELDS}")

                # CSV dosyasını yeniden oluşturmayı öner
                logger.error("🔧 Çözüm: CSV dosyası bozulmuş olabilir. Yeniden oluşturuluyor...")
                self._repair_csv_file()
                return

            # Aktif sinyalleri filtrele (PENDING_ENTRY, ACTIVE, HIT durumları)
            active_statuses = ['PENDING_ENTRY', 'ACTIVE', 'HIT_TP1', 'HIT_TP1_5', 'HIT_TP2', 'HIT_TP3']
            active_signals_df = signals_df[signals_df['status'].isin(active_statuses)]

            # Aktif sinyalleri dictionary'e çevir ve score değerini güvenli hale getir
            self.active_signals = {}
            for _, row in active_signals_df.iterrows():
                signal_dict = row.to_dict()
                # Score değerini güvenli hale getir
                score_value = signal_dict.get('score')
                if score_value is None or score_value == '' or pd.isna(score_value):
                    signal_dict['score'] = 0.0
                else:
                    try:
                        signal_dict['score'] = float(score_value)
                    except (ValueError, TypeError):
                        signal_dict['score'] = 0.0
                
                self.active_signals[row['signal_id']] = signal_dict

            logger.info(f"✅ {len(self.active_signals)} aktif sinyal başarıyla yüklendi.")

        except Exception as e:
            logger.error(f"❌ Aktif sinyaller yüklenirken hata: {e}", exc_info=True)
            logger.error("🔧 CSV dosyası bozulmuş olabilir. Yeniden oluşturuluyor...")
            self._repair_csv_file()

    def _repair_csv_file(self):
        """Bozulmuş CSV dosyasını onarır."""
        try:
            # Mevcut dosyayı yedekle
            if os.path.exists(self.signals_file):
                backup_path = f"{self.signals_file}.backup_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
                os.rename(self.signals_file, backup_path)
                logger.info(f"📁 Bozuk CSV dosyası yedeklendi: {backup_path}")

            # Yeni dosyayı doğru başlıklarla oluştur
            with open(self.signals_file, 'w', newline='', encoding='utf-8') as f:
                writer = csv.DictWriter(f, fieldnames=SIGNAL_FIELDS)
                writer.writeheader()

            logger.info("✅ CSV dosyası başarıyla onarıldı ve yeniden oluşturuldu.")
            self.active_signals = {}

        except Exception as e:
            logger.error(f"❌ CSV dosyası onarılırken hata: {e}")

    def apply_structure_lock(self, symbol: str, direction: str):
        """
        Başarısız bir işlem sonrası sembol/yön çiftini, pivot yapısı değişene kadar kilitler.
        """
        lock_key = f"{symbol}_{direction}"
        # Kilit anındaki pivotları, o an hafızada olan son pivotlardan alır
        pivots_at_lock = self.last_pivot_points.get(symbol, [])
        if pivots_at_lock:
            self.structure_lockout[lock_key] = pivots_at_lock
            logger.warning(f"🔒 YAPI KİLİDİ AKTİF: {lock_key} kombinasyonu, pivot yapısı değişene kadar kilitlendi.")
            self._save_structure_locks()  # <<< YENİ: Değişikliği dosyaya kaydet
        else:
            logger.warning(f"Yapısal kilit uygulanamadı: {symbol} için geçmiş pivot verisi bulunamadı.")

    def is_under_structure_lock(self, symbol: str, direction: str, current_swing_points: List[Dict[str, Any]]) -> bool:
        """
        Bir sembol/yön çiftinin yapısal kilit altında olup olmadığını kontrol eder.
        """
        lock_key = f"{symbol}_{direction}"
        if lock_key not in self.structure_lockout:
            return False  # Kilitli değil

        # Kilit anındaki pivotlar ile şimdikileri karşılaştır
        pivots_at_lock = self.structure_lockout[lock_key]
        if not pivots_at_lock or not current_swing_points:
            # Karşılaştırma yapılamıyorsa, güvenlik için kilidi devam ettir
            return True

        # Timestamp'leri datetime nesnesine dönüştür (JSON'dan okuma sonrası için garanti)
        try:
            last_locked_pivot_time = max(pd.to_datetime(p.get('timestamp')) for p in pivots_at_lock)
            last_current_pivot_time = max(pd.to_datetime(p.get('timestamp')) for p in current_swing_points)
        except (TypeError, ValueError) as e:
            logger.error(f"Pivot zaman damgası karşılaştırma hatası: {e}. Kilit devam ediyor.")
            return True

        # Eğer güncel pivotların en sonuncusu, kilit anındakinden daha yeniyse, yapı değişmiştir.
        if last_current_pivot_time > last_locked_pivot_time:
            logger.success(f"🔓 YAPI KİLİDİ KALDIRILDI: {lock_key} için yeni pivot yapısı tespit edildi.")
            del self.structure_lockout[lock_key]
            self._save_structure_locks()  # <<< YENİ: Değişikliği dosyaya kaydet
            return False  # Kilit kaldırıldı

        return True # Yapı değişmemiş, kilit devam ediyor

    def apply_trit_lock(self, symbol: str, direction: str, pattern_name: str, current_swing_points: List[Dict[str, Any]]):
        """TRIT/TRIB stratejileri için özel kilit uygular."""
        if not pattern_name or not any(keyword in pattern_name.upper() for keyword in ['TRIT', 'TRIB']):
            return

        lock_key = f"{symbol}_{direction}_{pattern_name}"
        current_time = datetime.now()
        
        last_swing_time = None
        last_swing_type = None
        if current_swing_points:
            latest_swing = max(current_swing_points, key=lambda x: x.get('timestamp', datetime.min))
            last_swing_time = latest_swing.get('timestamp')
            last_swing_type = latest_swing.get('type')
        
        lock_data = {
            'lock_time': current_time,
            'symbol': symbol,
            'direction': direction,
            'pattern_name': pattern_name,
            'last_swing_time': last_swing_time,
            'last_swing_type': last_swing_type,
            'reason': 'SL_HIT_TRIT'
        }
        
        self.trit_lockout[lock_key] = lock_data
        self._save_trit_locks()
        logger.warning(f"🔒 TRIT KİLİDİ AKTİF: {lock_key}")

    def is_under_trit_lock(self, symbol: str, direction: str, pattern_name: str, current_swing_points: List[Dict[str, Any]]) -> bool:
        """TRIT/TRIB stratejileri için özel kilit kontrolü yapar."""
        if not pattern_name or not any(keyword in pattern_name.upper() for keyword in ['TRIT', 'TRIB']):
            return False

        lock_key = f"{symbol}_{direction}_{pattern_name}"
        
        if lock_key not in self.trit_lockout:
            return False
        
        lock_data = self.trit_lockout[lock_key]
        lock_time = lock_data.get('lock_time')
        
        if not lock_time:
            logger.warning(f"TRIT kilit verisi bozuk: {lock_key}")
            return False
        
        lock_age_minutes = (datetime.now() - lock_time).total_seconds() / 60
        
        if lock_age_minutes < 60:
            logger.debug(f"🔒 TRIT KİLİDİ AKTİF: {lock_key} - Minimum süre dolmadı ({lock_age_minutes:.1f}/60 dk)")
            return True
        
        if not current_swing_points:
            logger.debug(f"🔒 TRIT KİLİDİ AKTİF: {lock_key} - Mevcut swing verisi yok, kilit devam ediyor.")
            return True
        
        latest_swing = max(current_swing_points, key=lambda x: x.get('timestamp', datetime.min))
        current_swing_time = latest_swing.get('timestamp')
        current_swing_type = latest_swing.get('type')
        locked_swing_time = lock_data.get('last_swing_time')
        
        if not locked_swing_time or current_swing_time <= locked_swing_time:
            logger.debug(f"🔒 TRIT KİLİDİ AKTİF: {lock_key} - Yeni swing oluşmadı.")
            return True
        
        if self._is_critical_swing_for_trit(direction, current_swing_type):
            logger.success(f"🔓 TRIT KİLİDİ KALDIRILDI: {lock_key} - Kritik swing ({current_swing_type}) tespit edildi.")
            del self.trit_lockout[lock_key]
            self._save_trit_locks()
            return False
        else:
            logger.debug(f"🔒 TRIT KİLİDİ AKTİF: {lock_key} - Kritik olmayan swing: {current_swing_type}")
            return True

    def _is_critical_swing_for_trit(self, direction: str, swing_type: str) -> bool:
        """TRIT/TRIB için kritik swing kontrolü yapar."""
        if not swing_type:
            return False
        
        direction = direction.upper()
        swing_type = swing_type.upper()
        
        if direction in ['BULL', 'BULLISH']: # TRIB Sinyali için
            return swing_type in ['LH', 'HH']
        elif direction in ['BEAR', 'BEARISH']: # TRIT Sinyali için
            return swing_type in ['HL', 'LL']
        return False

    def _update_signal_tracking(self):
        """Aktif sinyal takibini günceller."""
        current_count = len(self.active_signals)
        current_symbols = set(signal.get('symbol') for signal in self.active_signals.values())
        
        self.last_signal_count = current_count
        self.last_signal_symbols = current_symbols

    def check_active_signals(self, current_prices: Dict[str, float], all_timeframe_data: Dict[str, Dict[str, Dict[str, Any]]]) -> List[Dict[str, Any]]:
        """
        Aktif sinyalleri kontrol eder ve işlem yönetimi uygular.
        YENİ: Kârdaki işlemler için yapı bozulduğunda akıllı trailing stop mekanizmasını devreye alır.
        
        Args:
            current_prices: Sembol -> fiyat eşlemesi
            all_timeframe_data: Sembol -> zaman dilimi -> veri yapısı (swing_points, fib_levels, vb.)
        """
        if not self.active_signals:
            return []

        completed_trades = []
        signals_to_remove = []
        callback_rate = 0.30  # Geri çekilme oranı: %30
        partial_profit_take_pct = 0.50  # Kısmi kâr alma oranı: %50

        for signal_id, signal_data in list(self.active_signals.items()):
            symbol = signal_data.get("symbol")
            if symbol not in current_prices:
                continue

            current_status = signal_data.get("status")
            direction = signal_data.get("direction")
            current_price = current_prices[symbol]
            entry_price = float(signal_data.get("entry_price", 0))
            is_trade_terminal = False
            final_result = current_status

            # --- YENİ: TRAILING STOP DURUMUNU YÖNET ---
            if current_status == 'TRAILING_PROFIT':
                highest_price = float(signal_data.get('highest_price_in_trail', entry_price))
                
                # Yeni zirve/dip yaparak kârı artırdı mı kontrol et
                if (direction in ['BULL', 'BULLISH'] and current_price > highest_price) or \
                   (direction in ['BEAR', 'BEARISH'] and current_price < highest_price):
                    self.active_signals[signal_id]['highest_price_in_trail'] = current_price
                    highest_price = current_price
                    logger.info(f"[{symbol}] Trailing: Yeni zirve/dip kaydedildi: {format_price_standard(highest_price)}")

                # Trailing stop seviyesini hesapla
                profit_range = abs(highest_price - entry_price)
                allowed_pullback = profit_range * (1.0 - partial_profit_take_pct) * callback_rate
                
                trailing_stop_price = 0
                if direction in ['BULL', 'BULLISH']:
                    trailing_stop_price = highest_price - allowed_pullback
                    if current_price < trailing_stop_price:
                        final_result = "TRAILING_STOP_HIT"
                        is_trade_terminal = True
                elif direction in ['BEAR', 'BEARISH']:
                    trailing_stop_price = highest_price + allowed_pullback
                    if current_price > trailing_stop_price:
                        final_result = "TRAILING_STOP_HIT"
                        is_trade_terminal = True
                
                if not is_trade_terminal:
                    logger.debug(f"[{symbol}] Trailing aktif. Hedef: {format_price_standard(highest_price)}, Stop: {format_price_standard(trailing_stop_price)}")

            # --- YENİ BLOK: PENDING SİNYALLER İÇİN PROAKTİF GEÇERSİZ KILMA KONTROLÜ ---
            if current_status == "PENDING_ENTRY":
                # Gerekli verilerin olup olmadığını kontrol et
                if symbol in all_timeframe_data and self.main_timeframe in all_timeframe_data[symbol]:
                    symbol_data = all_timeframe_data[symbol][self.main_timeframe]
                    
                    invalidation_reason = self.invalidation_manager.check_invalidation_conditions(
                        pending_trade=signal_data,
                        all_symbol_data=symbol_data,
                        current_price=current_price
                    )
                    
                    if invalidation_reason:
                        logger.warning(f"[{symbol}] 🚫 PENDING sinyal proaktif olarak İPTAL EDİLDİ: {invalidation_reason}")
                        final_result = "CANCELLED_PRE_ENTRY"
                        is_trade_terminal = True
                else:
                    logger.debug(f"[{symbol}] PENDING sinyal için geçersiz kılma kontrolü atlanıyor (veri eksik).")
            # --- PROAKTİF KONTROL SONU ---

            # --- MEVCUT MANTIĞIN GÜNCELLENMİŞ HALİ ---
            if not is_trade_terminal and current_status not in ['TRAILING_PROFIT']:
                
                # --- 1. Adım: PENDING_ENTRY (Giriş Bekleyen) Sinyallerin Yönetimi ---
                if current_status == "PENDING_ENTRY":
                    # Fiyat giriş seviyesine ulaştı mı?
                    if self._has_price_reached_entry(direction, current_price, entry_price):
                        
                        # --- YENİ LOGLAMA MANTIĞI ---
                        # Artık rejim bilgisini direkt signal_data'dan alabiliyoruz
                        regime = signal_data.get('regime', 'UNCLEAR')
                        strategy_info = signal_data.get('tp_strategy_used', 'unknown').lower()
                        fibo_tf = "Günlük" if 'daily' in strategy_info else "4s" if '4h' in strategy_info else ""
                        
                        log_message = (
                            f"✅ GİRİŞ GERÇEKLEŞTİ: {symbol} sinyali {format_price_standard(current_price)} seviyesinden aktif oldu. "
                            f"(Rejim: {regime}, Giriş: {fibo_tf} Fibo)"
                        )
                        logger.info(log_message)
                        # --- YENİ LOGLAMA MANTIĞI SONU ---
                        
                        self.active_signals[signal_id]['status'] = 'ACTIVE'
                        # Durumu anında güncelleyerek bu döngüde SL/TP kontrolüne de girmesini sağla
                        current_status = 'ACTIVE'
                        
                        if self.notification_service:
                            self.notification_service.notify_status_update(self.active_signals[signal_id], 'ACTIVE')
                    else:
                        # Fiyat henüz girişe gelmediyse, zaman aşımı kontrolü yap
                        try:
                            entry_time_iso = signal_data.get("entry_time")
                            entry_time_dt = datetime.fromisoformat(entry_time_iso)
                            signal_age = datetime.now() - entry_time_dt

                            # Sadece PENDING durumundaki sinyaller için zaman aşımı
                            if signal_age > timedelta(hours=self.entry_timeout_hours):
                                logger.info(f"⏳ ZAMAN AŞIMI (PENDING): {symbol} sinyali girişe ulaşamadan zaman aşımına uğradı.")
                                final_result = "TIMEOUT"
                                is_trade_terminal = True
                        except (ValueError, TypeError) as e:
                            logger.warning(f"Sinyal {signal_id} için zaman aşımı kontrolü yapılamadı: {e}")
                        
                        # Giriş gerçekleşmediyse ve zaman aşımı olmadıysa, bu sinyal için başka kontrol yapma.
                        if not is_trade_terminal:
                            continue

                # --- 2. Adım: ACTIVE (ve TP almış) Sinyallerin Yönetimi ---
                if current_status != "PENDING_ENTRY" and not is_trade_terminal:
                    
                    # a) SL/TP Kontrolü
                    try:
                        sl_price = float(signal_data.get("sl_price"))
                        tp1_price = float(signal_data.get("tp1_price")) if signal_data.get("tp1_price") else None
                        tp1_5_price = float(signal_data.get("tp1_5_price")) if signal_data.get("tp1_5_price") else None
                        tp2_price = float(signal_data.get("tp2_price")) if signal_data.get("tp2_price") else None
                        tp3_price = float(signal_data.get("tp3_price")) if signal_data.get("tp3_price") else None

                        sl_tp_result = self._check_signal_result(
                            direction, current_price, entry_price, sl_price,
                            tp1_price, tp1_5_price, tp2_price, tp3_price
                        )

                        # Eğer SL veya nihai TP (TP3) vurulmuşsa, işlem kesin olarak biter.
                        if sl_tp_result in ["SL", "TP3"]:
                            final_result = sl_tp_result
                            is_trade_terminal = True
                        
                        # Eğer ara TP'ler vurulmuşsa ve durum daha önce güncellenmemişse, sadece durumu güncelle.
                        elif sl_tp_result == "TP1" and current_status == "ACTIVE":
                            logger.info(f"✅ KMS: {signal_id} TP1'e ulaştı. Stop girişe çekiliyor.")
                            self.active_signals[signal_id]['status'] = 'TP1_HIT'
                            self.active_signals[signal_id]['sl_price'] = entry_price
                            # YENİ: TP1 durumu bildirimini gönder
                            if self.notification_service:
                                self.notification_service.notify_status_update(self.active_signals[signal_id], 'TP1_HIT')
                        elif sl_tp_result == "TP1.5" and current_status in ["ACTIVE", "TP1_HIT"]:
                            logger.info(f"✅ KÂR KORUMA: {signal_id} TP1.5'e ulaştı. Stop TP1'e çekiliyor.")
                            self.active_signals[signal_id]['status'] = 'TP1.5_HIT'
                            self.active_signals[signal_id]['sl_price'] = tp1_price
                            # YENİ: TP1.5 durumu bildirimini gönder
                            if self.notification_service:
                                self.notification_service.notify_status_update(self.active_signals[signal_id], 'TP1.5_HIT')
                        elif sl_tp_result == "TP2" and current_status in ["ACTIVE", "TP1_HIT", "TP1.5_HIT"]:
                            logger.info(f"✅ KÂR KORUMA: {signal_id} TP2'ye ulaştı. Stop TP1.5'e çekiliyor.")
                            self.active_signals[signal_id]['status'] = 'TP2_HIT'
                            self.active_signals[signal_id]['sl_price'] = tp1_5_price if tp1_5_price else tp1_price
                            # YENİ: TP2 durumu bildirimini gönder
                            if self.notification_service:
                                self.notification_service.notify_status_update(self.active_signals[signal_id], 'TP2_HIT')

                    except (ValueError, TypeError) as e:
                        logger.warning(f"Sinyal {signal_id} için geçersiz fiyat verisi ({e}), SL/TP kontrolü atlanıyor.")

                    # b) Piyasa Yapısı Kırılımı Kontrolü - İYİLEŞTİRİLMİŞ AKILLI MANTIK
                    if not is_trade_terminal and symbol in all_timeframe_data and self.main_timeframe in all_timeframe_data[symbol]:
                        # Güncel swing noktalarını kontrol et
                        current_swing_points = all_timeframe_data[symbol][self.main_timeframe].get('swing_points', [])
                        
                        # Eğer yeni bir pivot noktası oluşmuşsa kontrol et
                        if current_swing_points and len(current_swing_points) > 0:
                            # Trade'den SONRA oluşan pivot'ları filtrele
                            signal_timestamp = signal_data.get('timestamp')
                            post_trade_pivots = []
                            
                            if signal_timestamp:
                                for pivot in current_swing_points:
                                    pivot_time = pivot.get('timestamp')
                                    if pivot_time and pivot_time > signal_timestamp:
                                        post_trade_pivots.append(pivot)
                            
                            # En az 1 yeni pivot oluşmuşsa yapı kırılımı kontrol et
                            if post_trade_pivots:
                                last_pivot = post_trade_pivots[-1]
                                pivot_type = last_pivot.get('type', '')
                                
                                # PATTERN TİPİNE GÖRE YAPI KIRILIMI KONTROLÜ
                                pattern_name = signal_data.get('pattern_name', '')
                                is_structure_broken = False
                                
                                # BOS ve LIQSFP_REV pattern'leri için özel kontrol
                                if 'BOS' in pattern_name or 'LIQSFP_REV' in pattern_name:
                                    # Bu pattern'ler güçlü yapısal sinyaller, daha toleranslı yaklaşım
                                    is_structure_broken = (direction == 'BULL' and pivot_type == 'LL') or \
                                                          (direction == 'BEAR' and pivot_type == 'HH')
                                # TRIB/TRIT ve diğer pattern'ler için normal kontrol
                                else:
                                    is_structure_broken = (direction == 'BULL' and pivot_type in ['LL', 'LH']) or \
                                                          (direction == 'BEAR' and pivot_type in ['HH', 'HL'])

                                if is_structure_broken:
                                    profit_pct = self._calculate_profit(direction, entry_price, current_price)
                                    
                                    # KURAL: Sadece kârda ise (%0.5'ten fazla) trailing'i başlat
                                    if profit_pct > 0.5:
                                        logger.success(f"[{symbol}] YAPI BOZULDU ({pivot_type}) AMA İŞLEM KÂRDA (%{profit_pct:.2f}). Takip Eden Stop Aktif Ediliyor.")
                                        
                                        # Kısmi kâr al ve yeni durumu ayarla
                                        self.active_signals[signal_id]['status'] = 'TRAILING_PROFIT'
                                        self.active_signals[signal_id]['trailing_activation_price'] = current_price
                                        self.active_signals[signal_id]['highest_price_in_trail'] = current_price
                                        self.active_signals[signal_id]['partial_profit_price'] = current_price
                                        
                                        if self.notification_service:
                                            # YENİ: Trailing stop aktivasyon bildirimini gönder
                                            self.notification_service.notify_trailing_stop_activated(
                                                self.active_signals[signal_id], profit_pct, partial_profit_take_pct
                                            )
                                        else:
                                            logger.info(f"📈 {symbol} Trailing Stop Aktif: Mevcut kâr %{profit_pct:.2f}, Kısmi alım %{partial_profit_take_pct*100:.0f}%")
                                    else:
                                        # Kârda değilse, eskisi gibi kapat
                                        logger.warning(f"[{symbol}] YAPI BOZULDU ({pivot_type}) ve işlem kârda değil. Pattern: {pattern_name}")
                                        final_result = "INVALIDATED_STRUCTURE"
                                        is_trade_terminal = True
                                else:
                                    logger.debug(f"[{symbol}] Yeni pivot ({pivot_type}) oluştu ama yapısal kırılım yok. Pattern: {pattern_name}")
                            else:
                                logger.debug(f"[{symbol}] Trade'den sonra yeni pivot oluşmamış, yapı kontrolü atlandı.")

            # --- 3. Adım: Kapanış İşlemleri ---
            if is_trade_terminal:
                logger.info(f"İşlem tamamlanıyor: {signal_id}, Nihai Sonuç: {final_result}")

                # --- YENİ ve DÜZELTİLMİŞ KİLİTLEME MANTIĞI ---
                # İşlem SL ile kapandıysa, stop-loss tipine bakılmaksızın yapısal kilit uygulanmalıdır.
                if final_result == 'SL':
                    self.apply_structure_lock(symbol, direction.upper())
                # -----------------------------

                signal_data["status"] = final_result
                signal_data["result_time"] = datetime.now().isoformat()
                signal_data["result_price"] = current_price

                # DÜZELTME: TP1+ seviyelerine ulaşmış işlemler için kar hesaplaması
                if final_result == "SL" and current_status in ["TP1_HIT", "TP1.5_HIT", "TP2_HIT", "TP3_HIT"]:
                    # TP seviyelerine ulaşmış işlemler için minimum %0 kar garantisi
                    calculated_profit = self._calculate_profit(
                        direction, float(signal_data.get("entry_price", 0)), current_price
                    )
                    # Negatif kar varsa %0'a çek (çünkü stop giriş seviyesindeydi)
                    signal_data["profit_percentage"] = max(0.0, calculated_profit)
                    logger.info(f"🔧 TP+ seviyesine ulaşmış işlem için kar düzeltmesi: {calculated_profit:.2f}% → {signal_data['profit_percentage']:.2f}%")
                else:
                    # Normal kar hesaplaması
                    signal_data["profit_percentage"] = self._calculate_profit(
                        direction, float(signal_data.get("entry_price", 0)), current_price
                    )
                
                if self.notification_service:
                    # YENİ: Trailing stop hit durumunda özel bildirim gönder
                    if final_result == "TRAILING_STOP_HIT":
                        self.notification_service.notify_trailing_stop_hit(signal_data)
                    else:
                        self.notification_service.notify_trade_closure(signal_data)
                
                # Results file'a yazarken header kontrolü yap
                file_is_empty = not os.path.exists(self.results_file) or os.path.getsize(self.results_file) == 0

                with open(self.results_file, 'a', newline='', encoding='utf-8') as f:
                    writer = csv.DictWriter(f, fieldnames=SIGNAL_FIELDS, extrasaction='ignore')
                    if file_is_empty:
                        writer.writeheader()
                    row_data = signal_data.copy()
                    row_data['confirmations'] = str(signal_data.get('confirmations', []))
                    writer.writerow(row_data)
                
                self.start_cooldown(symbol)
                
                signals_to_remove.append(signal_id)
                completed_trades.append(signal_data)

        # --- Döngü Sonrası Temizlik ve Güncelleme ---
        if signals_to_remove:
            for signal_id in signals_to_remove:
                if signal_id in self.active_signals:
                    del self.active_signals[signal_id]
            self._update_signals_csv()
            self._update_metrics(completed_trades=completed_trades)
        
        return completed_trades

    def _update_signals_csv(self):
        """
        SADECE AKTİF sinyallerin güncel halini CSV dosyasına yazar (üzerine yazarak).
        Tamamlanmış işlemler bu dosyadan temizlenmiş olur.
        """
        try:
            temp_file = self.signals_file + ".tmp"
            
            # Sadece o an hafızada aktif olan sinyalleri al
            active_signals_list = list(self.active_signals.values())
            
            with open(temp_file, 'w', newline='', encoding='utf-8') as f:
                writer = csv.DictWriter(f, fieldnames=SIGNAL_FIELDS, extrasaction='ignore')
                writer.writeheader()
                if active_signals_list:
                    writer.writerows(active_signals_list)
            
            # Geçici dosyayı asıl dosyanın üzerine yaz
            os.replace(temp_file, self.signals_file)
            logger.debug(f"Aktif sinyaller CSV'si güncellendi. Kalan aktif sinyal sayısı: {len(active_signals_list)}")

        except Exception as e:
            logger.error(f"Aktif sinyaller CSV'si güncellenirken hata: {e}", exc_info=True)

    def get_active_signals(self) -> List[Dict[str, Any]]:
        """Aktif sinyallerin listesini döndürür."""
        return list(self.active_signals.values())

    def get_active_signals_count(self) -> int:
        """Aktif sinyal sayısını döndürür."""
        return len(self.active_signals)

    def get_active_signals_summary(self) -> List[Dict[str, Any]]:
        """Aktif sinyallerin özetini döndürür (ana döngü için)."""
        summary = []
        for signal_id, signal_data in self.active_signals.items():
            summary_item = {
                'signal_id': signal_id,
                'symbol': signal_data.get('symbol', 'N/A'),
                'type': signal_data.get('pattern_name', signal_data.get('type', 'N/A')),
                'direction': signal_data.get('direction', 'N/A'),
                'entry_time': signal_data.get('entry_time', 'N/A'),
                'status': signal_data.get('status', 'ACTIVE'),
                'score': signal_data.get('score') if signal_data.get('score') is not None else 0.0
            }
            summary.append(summary_item)
        
        # Sinyalleri puana göre sıralayarak döndür (en yüksek skor önce)
        def safe_score(item):
            score = item.get('score')
            return score if score is not None else 0.0
        
        return sorted(summary, key=safe_score, reverse=True)

    def has_active_signal_for_symbol(self, symbol: str) -> bool:
        """Belirtilen sembol için aktif sinyal olup olmadığını kontrol eder."""
        for signal_data in self.active_signals.values():
            if signal_data.get('symbol') == symbol:
                return True
        return False

    def _check_and_notify_signal_changes(self):
        """Aktif sinyal listesindeki değişiklikleri kontrol eder ve gerekirse bildirim gönderir."""
        if not self.notification_service:
            return

        current_count = len(self.active_signals)
        current_symbols = set(signal.get('symbol') for signal in self.active_signals.values())
        
        # Son sinyal durumlarını saklamak için yeni kontrol ekleyelim
        current_signal_statuses = {}
        for signal_id, signal in self.active_signals.items():
            symbol = signal.get('symbol', '')
            status = signal.get('status', 'ACTIVE')
            current_signal_statuses[signal_id] = status
        
        # İlk kez çalıştırılıyorsa önceki durumları kaydet
        if not hasattr(self, 'last_signal_statuses'):
            self.last_signal_statuses = current_signal_statuses
        
        # Durum değişikliklerini kontrol et
        status_changes = []
        for signal_id, current_status in current_signal_statuses.items():
            previous_status = self.last_signal_statuses.get(signal_id, None)
            if previous_status and previous_status != current_status:
                # Sinyal durumu değişmiş
                symbol = self.active_signals[signal_id].get('symbol', 'N/A')
                direction = self.active_signals[signal_id].get('direction', 'N/A')
                status_changes.append({
                    'signal_id': signal_id,
                    'symbol': symbol,
                    'direction': direction,
                    'old_status': previous_status,
                    'new_status': current_status
                })
        
        # Değişiklik var mı kontrol et
        count_changed = current_count != self.last_signal_count
        symbols_changed = current_symbols != self.last_signal_symbols
        has_status_changes = len(status_changes) > 0
        
        # Durum değişikliklerini logla
        if has_status_changes:
            logger.info(f"📊 Aktif sinyal durum değişiklikleri tespit edildi:")
            for change in status_changes:
                symbol = change['symbol']
                direction = change['direction']
                old_status = change['old_status']
                new_status = change['new_status']
                
                # Durum değişikliğinin türüne göre emoji seç
                if new_status == "TP1_HIT":
                    emoji = "✅"
                    info = "TP1'e ulaştı! Stop girişe çekildi."
                elif new_status == "TP2_HIT":
                    emoji = "✅✅"
                    info = "TP2'ye ulaştı! Stop TP1'e çekildi."
                elif new_status == "TP3_HIT":
                    emoji = "✅✅✅"
                    info = "TP3'e ulaştı!"
                else:
                    emoji = "🔄"
                    info = f"Durum değişti: {old_status} → {new_status}"
                
                logger.info(f"{emoji} {symbol} {direction} işlem durumu değişti: {info}")
            
            # Durum değişikliği için bildirim gönder
            try:
                from stats_reporter import StatsReporter
                if (hasattr(self.notification_service, 'stats_reporter') and 
                    self.notification_service.stats_reporter is not None):
                    report = self.notification_service.stats_reporter.generate_active_signals_report()
                    self.notification_service.telegram.send_message(report)
                    logger.info("🔔 İşlem durumu değişikliği nedeniyle aktif sinyaller raporu güncellendi.")
            except Exception as e:
                logger.error(f"Durum değişikliği bildirimi gönderilirken hata: {e}")
        
        if count_changed or symbols_changed:
            # Yeni eklenen ve kaldırılan sinyaller
            new_symbols = current_symbols - self.last_signal_symbols
            removed_symbols = self.last_signal_symbols - current_symbols
            
            logger.info(f"📊 Aktif sinyal sayısı değişikliği tespit edildi:")
            logger.info(f"   Önceki: {self.last_signal_count} sinyal, Şimdi: {current_count} sinyal")
            
            # Değişiklik türüne göre farklı davran
            if new_symbols and not removed_symbols:
                # Sadece yeni sinyal eklendi
                logger.info(f"   ➕ Yeni sinyaller: {', '.join(new_symbols)}")
                
                # Yeni sinyal için bildirim gönder (cooldown zaten spam'ı engelliyor)
                try:
                    from stats_reporter import StatsReporter
                    if (hasattr(self.notification_service, 'stats_reporter') and 
                        self.notification_service.stats_reporter is not None):
                        report = self.notification_service.stats_reporter.generate_active_signals_report()
                        self.notification_service.telegram.send_message(report)
                        logger.info("🔔 Yeni sinyal eklenmesi nedeniyle aktif sinyaller raporu güncellendi.")
                except Exception as e:
                    logger.error(f"Yeni sinyal bildirimi gönderilirken hata: {e}")
                    
            elif removed_symbols and not new_symbols:
                # Sadece sinyal tamamlandı
                logger.info(f"   ➖ Tamamlanan sinyaller: {', '.join(removed_symbols)}")
                
                # Sinyal tamamlanması bildirimi gönder
                try:
                    from stats_reporter import StatsReporter
                    if (hasattr(self.notification_service, 'stats_reporter') and 
                        self.notification_service.stats_reporter is not None):
                        report = self.notification_service.stats_reporter.generate_active_signals_report()
                        self.notification_service.telegram.send_message(report)
                        logger.info("🔔 Sinyal tamamlanması nedeniyle aktif sinyaller raporu güncellendi.")
                except Exception as e:
                    logger.error(f"Sinyal tamamlanma bildirimi gönderilirken hata: {e}")
                    
            elif new_symbols and removed_symbols:
                # Hem yeni sinyal eklendi hem de sinyal tamamlandı
                logger.info(f"   ➕ Yeni sinyaller: {', '.join(new_symbols)}")
                logger.info(f"   ➖ Tamamlanan sinyaller: {', '.join(removed_symbols)}")
                
                # Karışık durum için tek bildirim gönder
                try:
                    from stats_reporter import StatsReporter
                    if (hasattr(self.notification_service, 'stats_reporter') and 
                        self.notification_service.stats_reporter is not None):
                        report = self.notification_service.stats_reporter.generate_active_signals_report()
                        self.notification_service.telegram.send_message(report)
                        logger.info("🔔 Sinyal değişiklikleri nedeniyle aktif sinyaller raporu güncellendi.")
                except Exception as e:
                    logger.error(f"Karışık sinyal değişiklik bildirimi gönderilirken hata: {e}")
        
        # Son durumları güncelle
        self.last_signal_count = current_count
        self.last_signal_symbols = current_symbols.copy()
        self.last_signal_statuses = current_signal_statuses.copy()

    # Diğer tüm yardımcı metodlar (_check_signal_result, _calculate_profit, _update_metrics vb.)
    # doğru ve eksiksiz bir şekilde burada yer almalıdır.
    def _check_signal_result(self, direction: str, current_price: float, entry_price: float, sl_price: float, tp1_price: Optional[float], tp1_5_price: Optional[float], tp2_price: Optional[float], tp3_price: Optional[float]) -> str:
        """Sadece SL ve TP seviyelerini kontrol eder."""
        if direction == "BULL":
            if sl_price and current_price <= sl_price: return "SL"
            if tp3_price and current_price >= tp3_price: return "TP3"
            if tp2_price and current_price >= tp2_price: return "TP2"
            if tp1_5_price and current_price >= tp1_5_price: return "TP1.5"  # YENİ KONTROL
            if tp1_price and current_price >= tp1_price: return "TP1"
        elif direction == "BEAR":
            if sl_price and current_price >= sl_price: return "SL"
            if tp3_price and current_price <= tp3_price: return "TP3"
            if tp2_price and current_price <= tp2_price: return "TP2"
            if tp1_5_price and current_price <= tp1_5_price: return "TP1.5"  # YENİ KONTROL
            if tp1_price and current_price <= tp1_price: return "TP1"
        return "ACTIVE"

    def _has_price_reached_entry(self, direction: str, current_price: float, entry_price: float) -> bool:
        tolerance = 0.001
        if direction == "BULL":
            return current_price <= entry_price * (1 + tolerance)
        elif direction == "BEAR":
            return current_price >= entry_price * (1 - tolerance)
        return True
        
    def _calculate_profit(self, direction: str, entry_price: float, exit_price: float) -> float:
        if not entry_price or entry_price == 0: return 0.0
        if direction == "BULL":
            return ((exit_price - entry_price) / entry_price) * 100
        elif direction == "BEAR":
            return ((entry_price - exit_price) / entry_price) * 100
        return 0.0

    def _calculate_risk_percentage(self, entry_price: float, sl_price: float, direction: str) -> float:
        """Stop loss mesafesini yüzde olarak hesaplar."""
        try:
            entry_price = float(entry_price)
            sl_price = float(sl_price)
            
            if entry_price == 0:
                return 0.0
                
            if direction == "BULL":
                return abs((entry_price - sl_price) / entry_price) * 100
            elif direction == "BEAR":
                return abs((sl_price - entry_price) / entry_price) * 100
            return 0.0
        except (ValueError, TypeError):
            return 0.0

    def get_performance_metrics(self) -> Dict[str, Any]:
        """Performans metriklerini döndürür."""
        try:
            # Metrics dosyasından verileri oku
            if os.path.exists(self.metrics_file):
                try:
                    with open(self.metrics_file, 'r', encoding='utf-8') as f:
                        content = f.read().strip()
                        if not content:
                            logger.warning("⚠️  Performance metrics dosyası boş, varsayılan değerler kullanılıyor.")
                            metrics = self._get_default_metrics()
                        else:
                            metrics = json.loads(content)
                            # Eksik alanları kontrol et ve ekle
                            metrics = self._validate_and_fix_metrics(metrics)
                except json.JSONDecodeError as e:
                    logger.error(f"❌ JSON dosyası bozuk: {e}")
                    logger.error("🔧 Dosya yedekleniyor ve yeniden oluşturuluyor...")
                    self._repair_metrics_file()
                    metrics = self._get_default_metrics()
                except Exception as e:
                    logger.error(f"❌ Metrics dosyası okuma hatası: {e}")
                    metrics = self._get_default_metrics()
            else:
                logger.info("📁 Performance metrics dosyası mevcut değil, varsayılan değerlerle oluşturuluyor.")
                metrics = self._get_default_metrics()

            # Aktif sinyal sayısını güncelle
            metrics["active_signals_count"] = len(self.active_signals)

            return metrics

        except Exception as e:
            logger.error(f"❌ Performans metrikleri alınırken kritik hata: {e}", exc_info=True)
            # Hata durumunda varsayılan metrikler döndür
            return self._get_default_metrics()

    def _get_default_metrics(self) -> Dict[str, Any]:
        """Varsayılan metrics yapısını döndürür."""
        return {
            "total_signals": 0,
            "completed_trades": 0,
            "successful_trades": 0,
            "failed_trades": 0,
            "success_rate": 0.0,
            "avg_profit_percentage": 0.0,
            "total_profit_percentage": 0.0,
            "tp1_hits": 0,
            "tp1_5_hits": 0,
            "tp2_hits": 0,
            "tp3_hits": 0,
            "sl_hits": 0,
            "pivot_success_hits": 0,
            "cancelled_trades": 0,
            "tp1_rate": 0.0,
            "tp1_5_rate": 0.0,
            "tp2_rate": 0.0,
            "tp3_rate": 0.0,
            "sl_rate": 0.0,
            "pattern_stats": {},
            "sl_type_stats": {},
            "strategy_stats": {},
            "best_trade": None,
            "worst_trade": None,
            "best_symbol": None,
            "best_confirmation": None,
            "best_pattern": None,
            "best_sl_type": None,
            "last_updated": datetime.now().isoformat(),
            "active_signals_count": len(self.active_signals)
        }

    def _validate_and_fix_metrics(self, metrics: Dict[str, Any]) -> Dict[str, Any]:
        """Metrics yapısını doğrular ve eksik alanları ekler."""
        default_metrics = self._get_default_metrics()

        # Eksik alanları ekle
        for key, default_value in default_metrics.items():
            if key not in metrics:
                metrics[key] = default_value
                logger.debug(f"📝 Eksik alan eklendi: {key} = {default_value}")

        return metrics

    def _repair_metrics_file(self):
        """Bozuk metrics dosyasını onarır."""
        try:
            # Mevcut dosyayı yedekle
            if os.path.exists(self.metrics_file):
                backup_path = f"{self.metrics_file}.backup_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
                os.rename(self.metrics_file, backup_path)
                logger.info(f"📁 Bozuk metrics dosyası yedeklendi: {backup_path}")

            # Yeni dosyayı oluştur
            default_metrics = self._get_default_metrics()
            with open(self.metrics_file, 'w', encoding='utf-8') as f:
                json.dump(default_metrics, f, indent=4, ensure_ascii=False)

            logger.info("✅ Metrics dosyası başarıyla onarıldı ve yeniden oluşturuldu.")

        except Exception as e:
            logger.error(f"❌ Metrics dosyası onarılırken hata: {e}", exc_info=True)

    def _update_metrics(self, completed_trades: List = None, new_signal: bool = False, signal_data: Dict[str, Any] = None):
        """Performans metriklerini günceller ve JSON dosyasına yazar."""
        if not completed_trades and not new_signal:
            return

        try:
            metrics = self.get_performance_metrics()

            if new_signal:
                metrics['total_signals'] = metrics.get('total_signals', 0) + 1

                # YENİ: Yeni sinyal için pattern istatistiklerini topla
                if signal_data:
                    pattern_name = signal_data.get('pattern_name', 'unknown')
                    if pattern_name and pattern_name != 'unknown':
                        pattern_stats = metrics.get('pattern_stats', {})
                        pattern_stats[pattern_name] = pattern_stats.get(pattern_name, 0) + 1
                        metrics['pattern_stats'] = pattern_stats

            if completed_trades:
                for trade in completed_trades:
                    metrics['completed_trades'] = metrics.get('completed_trades', 0) + 1
                    status = trade.get("status")
                    profit = trade.get("profit_percentage") or 0.0

                    if status in ["TP1", "TP1.5", "TP2", "TP3", "PIVOT_SUCCESS"]:
                        metrics['successful_trades'] = metrics.get('successful_trades', 0) + 1
                        if status == "TP1": metrics['tp1_hits'] = metrics.get('tp1_hits', 0) + 1
                        if status == "TP1.5": metrics['tp1_5_hits'] = metrics.get('tp1_5_hits', 0) + 1  # YENİ TP1.5 takibi
                        if status == "TP2": metrics['tp2_hits'] = metrics.get('tp2_hits', 0) + 1
                        if status == "TP3": metrics['tp3_hits'] = metrics.get('tp3_hits', 0) + 1
                        if status == "PIVOT_SUCCESS": metrics['pivot_success_hits'] = metrics.get('pivot_success_hits', 0) + 1
                    elif status == "SL":
                        # Kâr/Zarar durumuna göre Başarılı/Başarısız olarak ayır
                        if profit < 0:  # Sadece GERÇEKTEN zararla kapanan SL'leri başarısız say
                            metrics['failed_trades'] = metrics.get('failed_trades', 0) + 1
                            logger.debug(f"SL işlemi başarısız olarak kaydedildi: {trade.get('signal_id')} (Profit: %{profit:.2f})")
                        else:  # Giriş seviyesinde veya kârda kapanan SL'ler (TP1 sonrası) başarılı sayılır
                            metrics['successful_trades'] = metrics.get('successful_trades', 0) + 1
                            logger.debug(f"SL işlemi başarılı olarak kaydedildi: {trade.get('signal_id')} (Profit: %{profit:.2f})")

                        # SL hit sayacını her durumda artır (istatistik için)
                        metrics['sl_hits'] = metrics.get('sl_hits', 0) + 1

                    # YENİ EKLENECEK BLOK: INVALIDATED_STRUCTURE sınıflandırması
                    elif status == "INVALIDATED_STRUCTURE":
                        # Yapı kırılımı ile kârda kapanan işlemleri BAŞARILI say
                        if profit > 0:
                            metrics['successful_trades'] = metrics.get('successful_trades', 0) + 1
                            logger.debug(f"INVALIDATED_STRUCTURE işlemi başarılı olarak kaydedildi: {trade.get('signal_id')} (Profit: %{profit:.2f})")
                        # Yapı kırılımı ile zararda veya başa baş kapananları BAŞARISIZ say
                        else:  # profit <= 0
                            metrics['failed_trades'] = metrics.get('failed_trades', 0) + 1
                            logger.debug(f"INVALIDATED_STRUCTURE işlemi başarısız olarak kaydedildi: {trade.get('signal_id')} (Profit: %{profit:.2f})")

                    elif status in ["CANCELLED", "CANCELLED_PIVOT", "TIMEOUT"]:
                        metrics['cancelled_trades'] = metrics.get('cancelled_trades', 0) + 1

                    metrics['total_profit_percentage'] = metrics.get('total_profit_percentage', 0.0) + profit

                    # YENİ: SL Type istatistiklerini topla
                    sl_type = trade.get('sl_type', 'unknown')
                    if sl_type != 'unknown':
                        sl_type_stats = metrics.get('sl_type_stats', {})
                        sl_type_stats[sl_type] = sl_type_stats.get(sl_type, 0) + 1
                        metrics['sl_type_stats'] = sl_type_stats

                    # YENİ: Strategy istatistiklerini topla
                    strategy_used = trade.get('tp_strategy_used', 'unknown')
                    if strategy_used != 'unknown':
                        strategy_stats = metrics.get('strategy_stats', {})
                        strategy_stats[strategy_used] = strategy_stats.get(strategy_used, 0) + 1
                        metrics['strategy_stats'] = strategy_stats

                    current_best = metrics.get('best_trade')
                    if not current_best or profit > current_best.get('profit_percentage', -1000):
                        # DÜZELTME: 'symbol' anahtarını da ekliyoruz
                        metrics['best_trade'] = {
                            'signal_id': trade.get('signal_id'),
                            'symbol': trade.get('symbol'),
                            'profit_percentage': profit
                        }

                    current_worst = metrics.get('worst_trade')
                    if not current_worst or profit < current_worst.get('profit_percentage', 1000):
                        # DÜZELTME: 'symbol' anahtarını da ekliyoruz
                        metrics['worst_trade'] = {
                            'signal_id': trade.get('signal_id'),
                            'symbol': trade.get('symbol'),
                            'profit_percentage': profit
                        }

            # Oranları yeniden hesapla
            completed_count = metrics.get('completed_trades', 0)
            if completed_count > 0:
                metrics['success_rate'] = (metrics.get('successful_trades', 0) / completed_count) * 100
                metrics['avg_profit_percentage'] = metrics.get('total_profit_percentage', 0.0) / completed_count
            
            metrics['last_updated'] = datetime.now().isoformat()

            # Güvenli JSON yazma
            try:
                # Önce geçici dosyaya yaz
                temp_file = f"{self.metrics_file}.tmp"
                with open(temp_file, 'w', encoding='utf-8') as f:
                    json.dump(metrics, f, indent=4, ensure_ascii=False)

                # Başarılı olursa asıl dosyanın üzerine yaz
                os.replace(temp_file, self.metrics_file)
                logger.info("📈 Performans metrikleri başarıyla güncellendi.")

            except Exception as write_error:
                logger.error(f"❌ Metrics dosyası yazılırken hata: {write_error}")
                # Geçici dosyayı temizle
                if os.path.exists(f"{self.metrics_file}.tmp"):
                    os.remove(f"{self.metrics_file}.tmp")
                raise

        except Exception as e:
            logger.error(f"❌ Metrikler güncellenirken kritik hata: {e}", exc_info=True)

    def update_pivot_data(self, symbol: str, swing_points: List[Dict[str, Any]]) -> Optional[str]:
        """
        Yeni bir pivot oluşup oluşmadığını kontrol eder.
        Eğer yeni bir pivot varsa, bu pivotun TİPİNİ ('HH', 'LL', vb.) döndürür.
        YENİ ve GÜÇLENDİRİLMİŞ VERSİYON: Sadece zaman damgasına değil, son pivotun içeriğine de bakar.
        """
        try:
            previous_pivots = self.last_pivot_points.get(symbol, [])

            # Karşılaştırılacak geçmiş veri yoksa veya mevcut veri boşsa, durumu kaydet ve çık.
            if not previous_pivots or not swing_points:
                self.last_pivot_points[symbol] = swing_points
                return None

            # Her iki listedeki en son pivotu al (en yüksek zaman damgasına sahip olan)
            last_known_pivot = max(previous_pivots, key=lambda x: x.get('timestamp'))
            latest_calculated_pivot = max(swing_points, key=lambda x: x.get('timestamp'))

            # Değişiklik kontrolü için flag
            has_new_pivot_detected = False

            # 1. Koşul: Zaman damgası ilerlemişse kesinlikle yeni bir pivot vardır.
            if latest_calculated_pivot['timestamp'] > last_known_pivot['timestamp']:
                has_new_pivot_detected = True
            
            # 2. Koşul: Zaman damgası aynı olsa bile, pivotun tipi veya fiyatı değişmiş mi?
            # Bu, aynı mumda pivotun yeniden hesaplandığı nadir durumları yakalar.
            elif latest_calculated_pivot['timestamp'] == last_known_pivot['timestamp']:
                if (latest_calculated_pivot.get('type') != last_known_pivot.get('type') or
                    latest_calculated_pivot.get('price') != last_known_pivot.get('price')):
                    has_new_pivot_detected = True
            
            # Eğer yeni bir pivot tespit edildiyse
            if has_new_pivot_detected:
                new_pivot_type = latest_calculated_pivot.get('type')
                # Sadece geçerli bir tipi varsa (örn: 'HH', 'LL') raporla
                if new_pivot_type:
                    logger.info(f"🔄 {symbol} için yeni pivot tespit edildi! Tip: {new_pivot_type}, Zaman: {latest_calculated_pivot['timestamp'].strftime('%Y-%m-%d %H:%M')}")
                    self.last_pivot_points[symbol] = swing_points
                    return new_pivot_type
            
            # Değişiklik yoksa, yine de en güncel listeyi sakla ama None döndür
            self.last_pivot_points[symbol] = swing_points
            return None

        except Exception as e:
            logger.error(f"Pivot verisi güncellenirken hata ({symbol}): {e}", exc_info=True)
            return None

    def start_cooldown(self, symbol: str, minutes: int = 15):
        """Belirtilen sembol için soğuma periyodunu başlatır."""
        cooldown_end_time = datetime.now() + timedelta(minutes=minutes)
        self.signal_cooldown[symbol] = cooldown_end_time
        logger.info(f"❄️ {symbol} için {minutes} dakikalık soğuma periyodu başlatıldı. Bitiş: {cooldown_end_time.strftime('%Y-%m-%d %H:%M:%S')}")

    def is_on_cooldown(self, symbol: str) -> bool:
        """Sembolün soğuma periyodunda olup olmadığını kontrol eder."""
        if symbol in self.signal_cooldown:
            if datetime.now() < self.signal_cooldown[symbol]:
                return True
            else:
                del self.signal_cooldown[symbol]
                logger.debug(f"🔥 {symbol} için soğuma periyodu sona erdi.")
                return False
        return False

    def _load_pending_setups(self) -> Dict[str, Any]:
        """Beklemedeki kurulumları JSON dosyasından yükler."""
        try:
            if os.path.exists(self.pending_setups_file):
                with open(self.pending_setups_file, 'r', encoding='utf-8') as f:
                    setups = json.load(f)
                    # Timestamp'leri datetime nesnesine çevir
                    for symbol, setup in setups.items():
                        if 'lock_time' in setup:
                            setup['lock_time'] = datetime.fromisoformat(setup['lock_time'])
                    logger.info(f"✅ {len(setups)} adet beklemedeki kurulum yüklendi.")
                    return setups
        except (IOError, json.JSONDecodeError) as e:
            logger.error(f"❌ Beklemedeki kurulumlar dosyası okunamadı: {e}. Boş başlatılıyor.")
        return {}

    def _save_pending_setups(self):
        """Mevcut beklemedeki kurulumları JSON dosyasına kaydeder."""
        try:
            setups_to_save = {}
            for symbol, setup in self.pending_setups.items():
                savable_setup = setup.copy()
                if 'lock_time' in savable_setup and isinstance(savable_setup['lock_time'], datetime):
                    savable_setup['lock_time'] = savable_setup['lock_time'].isoformat()
                setups_to_save[symbol] = savable_setup
                
            with open(self.pending_setups_file, 'w', encoding='utf-8') as f:
                json.dump(setups_to_save, f, indent=4)
        except IOError as e:
            logger.error(f"❌ Beklemedeki kurulumlar dosyaya kaydedilemedi: {e}")

    def apply_bos_retracement_lock(self, symbol: str, bos_direction: str, target_poi_zone: Dict):
        """Bir BOS sonrası geri çekilme beklentisi için bir kilit uygular."""
        lock_data = {
            'type': 'BOS_RETRACEMENT',
            'expected_direction': bos_direction.lower(),  # DÜZELTME: Artık BOS yönünü saklıyoruz.
            'target_poi': target_poi_zone,
            'lock_time': datetime.now(),
            'bos_direction': bos_direction
        }
        self.pending_setups[symbol] = lock_data
        self._save_pending_setups()
        
        # Loglama ve Bildirim
        log_message = f"🔒 [BOS Kilidi Aktif] {symbol} için {bos_direction.upper()} yönlü trend devamı bekleniyor."
        logger.warning(log_message)
        
        if self.alert_manager:
            poi_top = target_poi_zone.get('super_poi_top', 0)
            poi_bottom = target_poi_zone.get('super_poi_bottom', 0)
            poi_factors = ", ".join(target_poi_zone.get('confluent_factors', []))
            
            alert_title = f"📈 {symbol} - Yapı Kırılımı (BOS) ve Beklenti"
            alert_message = (
                f"**{symbol}** paritesinde **{bos_direction.upper()}** yönlü bir yapı kırılımı (BOS) tespit edildi.\n\n"
                f"Sistem, fiyatin aşağıdaki POI (Point of Interest) bölgesine geri çekilmesini ve ardından trend yönünde devam etmesini bekliyor:\n\n"
                f"- **Hedef POI:** {format_price_standard(poi_bottom)} - {format_price_standard(poi_top)}\n"
                f"- **İçerik:** {poi_factors}\n\n"
                f"Bu bölgeden **{bos_direction.upper()}** yönlü bir tepki (LTF MSS) aranacak."
            )
            self.alert_manager.send_alert(f"{alert_title}\n\n{alert_message}", "info")

    def get_active_lock(self, symbol: str) -> Optional[Dict[str, Any]]:
        """Belirli bir sembol için aktif bir kilit olup olmadığını kontrol eder ve döndürür."""
        lock = self.pending_setups.get(symbol)
        if lock:
            lock_age_hours = (datetime.now() - lock['lock_time']).total_seconds() / 3600
            # Kilidi 8 saat sonra otomatik kaldır (örneğin)
            if lock_age_hours > 8:
                logger.info(f"⏳ [BOS Kilidi Kaldırıldı] {symbol} için zaman aşımı.")
                self.release_lock(symbol)
                return None
            return lock
        return None

    def release_lock(self, symbol: str):
        """Bir sembol üzerindeki kilidi kaldırır."""
        if symbol in self.pending_setups:
            del self.pending_setups[symbol]
            self._save_pending_setups()
            logger.success(f"🔓 [BOS Kilidi Kaldırıldı] {symbol} için beklenti durumu sona erdi.")