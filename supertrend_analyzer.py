import pandas as pd
import numpy as np
from loguru import logger
from typing import Dict, Any, Optional, List

from supertrend import SuperTrend
from utils import format_price_standard

class SuperTrendAnalyzer:
    """
    SuperTrend indikatörünü hesaplayan ve analiz eden sınıf.
    """
    
    def __init__(self, atr_period: int = 10, atr_multiplier: float = 3.0, use_rma: bool = True):
        """
        SuperTrendAnalyzer sınıfını başlatır.
        
        Args:
            atr_period: ATR hesaplaması için periyot
            atr_multiplier: ATR çarpanı
            use_rma: True ise RMA, False ise SMA kullanılır
        """
        self.atr_period = atr_period
        self.atr_multiplier = atr_multiplier
        self.atr_method = "rma" if use_rma else "sma"
        self.supertrend = SuperTrend(
            period=atr_period,
            multiplier=atr_multiplier,
            atr_method=self.atr_method
        )
        logger.info(
            f"SuperTrendAnalyzer başlatıldı: ATR Periyot={atr_period}, "
            f"ATR Çarpan={atr_multiplier}, ATR Metod={self.atr_method}"
        )
    
    def analyze_candles(self, symbol: str, timeframe: str, candles: pd.DataFrame) -> Dict[str, Any]:
        """
        Verilen mum verilerinde SuperTrend hesaplar ve trend durumunu analiz eder.
        
        Args:
            symbol: İşlem sembolü
            timeframe: Zaman dilimi
            candles: Mum verileri (OHLCV) içeren pandas DataFrame
        
        Returns:
            Trend durumu ve mesafesi bilgilerini içeren sözlük
        """
        if candles.empty:
            logger.warning(f"{symbol}/{timeframe} - SuperTrend için veri yok.")
            return {}
        
        logger.debug(f"{symbol}/{timeframe} - SuperTrend hesaplanıyor...")
        
        try:
            # Gerekli sütun kontrolü
            required_columns = ['open', 'high', 'low', 'close']
            if not all(col in candles.columns for col in required_columns):
                logger.warning(f"{symbol}/{timeframe} - SuperTrend hesaplaması için gerekli sütunlar eksik")
                return {}
            
            # SuperTrend hesapla
            result_df = self.supertrend.calculate(candles)
            
            # Son trend durumu bilgilerini al
            trend_status = self.supertrend.get_trend_status(result_df)
            
            return {
                "symbol": symbol,
                "timeframe": timeframe,
                "trend": trend_status["trend"],
                "signal": trend_status["signal"],
                "supertrend_value": trend_status["supertrend"],
                "current_price": trend_status["current_price"],
                "distance": trend_status["distance"],
                "distance_percent": trend_status["distance_pct"],
                "formatted_supertrend": trend_status["formatted_supertrend"],
                "formatted_price": trend_status["formatted_price"],
                "formatted_distance": trend_status["formatted_distance"]
            }
            
        except Exception as e:
            logger.error(f"{symbol}/{timeframe} - SuperTrend hesaplama hatası: {e}")
            return {} 