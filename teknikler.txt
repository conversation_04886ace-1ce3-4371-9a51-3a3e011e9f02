🥇  LIQSFP_REV (Stop Hunt/Reversal)
🥈  BRKR12_BOS1 (Breaker+BOS)
🥉  BOS, LIQSFP (genel), OB12_BOS1
🏅  TRIB/TRIT


Yeni ICT Özellikler
External Liquidity Hunt Analysis
IDM Confirmed Hunt: +2.5 puan
Standard Hunt: **** puan
12H & 24H Timeframe swing analizi
Sell-side/Buy-side likidite tespiti
FVG-Enhanced Order Blocks
Bitişiklik Kontrolü: %2 tolerans ile
Timing Kontrolü: 48 saat window
Type Matching: Bull FVG + Bull OB confluence
Score Enhancement: FVG destekli OB'ler için bonus
Smart Money Concepts
Market Structure Events: MSS/BOS detection
Liquidity Grab Validation: Professional hunt analysis
Confluence Scoring: Multi-timeframe ICT validation
Premium Signals: 10+ puanlık mükemmel setups


Automaton artık profesyonel ICT Concepts metodolojisi ile:

✅ External liquidity hunt tespiti yapıyor
✅ FVG destekli Order Block analizi gerçekleştiriyor
✅ Smart Money flow takip ediyor
✅ Premium confluence signals üretiyor

ICT Concepts Mantığı:
Valid Swing Kriterleri:
✅ Trend ile uyumlu swing'ler tercih edilir
✅ IDM confirmed swing'ler geçersiz sayılır
✅ Minimal fiyat farkı kontrolü (%0.05)
Inducement (IDM) Tespiti:
🔍 False Breakout: %0.02-0.1 arası minimal kırılım
🔍 Liquidity Grab: Çoklu test edilen seviyeler
🔍 Round Number: .00, .50 seviyelerine yakınlık
✅ Confirmation: 5-15 bar sonrası ters yön hareketi
Market Structure Analysis:
📈 MSS Bullish: LL → HL → HH (trend değişimi)
📉 MSS Bearish: HH → LH → LL (trend değişimi)
🔄 BOS Bullish/Bearish: Mevcut trend devamı
4. Korunan Önceki Geliştirmeler:
✅ LIQSFP REV mantığı korundu
✅ Scoring System puanlama mantığı korundu
✅ Smart Entry Strategy korundu
✅ Stats Tracker geliştirmeleri korundu
✅ Negative scoring sistemi korundu

ICT Concepts Temel Mantığı:
Market Structure Shift (MSS): Trend değişiklik sinyali
Break of Structure (BOS): Trend devamı sinyali
Valid Swing: Yapısal önem taşıyan swing noktaları
Inducement (IDM): False breakout'lar (Sahte kırılımlar)

Pivot Integration: Mevcut PivotAnalyzer'ın _identify_internal_swings metodunu kullanır
Dependency Injection: Constructor'da PivotAnalyzer instance'ı oluşturur
Error Handling: Kapsamlı try-catch blokları ve logging
SOLID Principles: Tek sorumluluk prensibi, açık-kapalı prensibi
PEP 8 Compliance: Türkçe dokümantasyon ve standart Python f


Strateji 1: Likidite Alımı + FVG/OB Kesişimi (Yüksek Zaman Dilimi Tuzağı)

Bu, piyasanın büyük oyuncuları tuzağa düşürdükten sonra oluşan en güçlü geri dönüş sinyallerinden biridir.

    Mantık:

        Fiyat, yüksek zaman dilimindeki (HTF) önemli bir likidite seviyesini (önceki swing high veya low) temizler (Liquidity Sweep).

        Likiditeyi aldıktan sonra, fiyat hızla ters yöne döner ve bir yapı kırılımı (MSS/BOS) oluşturur.

        Bu kırılım sonrası fiyat, geri çekilme yaparken hem bir FVG hem de o FVG'nin içinde yer alan önemli bir Order Block'un kesiştiği "Süper POI" bölgesine gelir.

        İşleme giriş, bu kesişim bölgesinden yapılır.

    Uygulama Planı:

        Likidite Alımını Tespit Et (pivot_analyzer.py veya yeni bir modül):

            pivot_analyzer.py içinde, major_swing_points listesini kullanarak bir önceki ana swing tepesinin veya dibinin fiyat tarafından "alınıp alınmadığını" tespit eden bir fonksiyon yazılabilir. Bu fonksiyon, bir liquidity_sweep: {'direction': 'bullish', 'price': ...} objesi döndürebilir.

        Sinyali Puanla (scoring_system.py):

            _score_patterns metodunda, eğer liquidity_sweep gerçekleşmiş ve ardından bir ters yönlü yapı kırılımı (BOS/MSS) oluşmuşsa, buna çok yüksek bir temel puan (örneğin 6.0 veya 7.0) atayın. Bu, sinyalin diğer tüm pattern'lerin önüne geçmesini sağlar.

        Giriş Noktasını Hesapla (smart_entry_strategy.py):

            calculate_entry_levels içinde, eğer pattern_name "LIQ_SWEEP_REVERSAL" gibi özel bir isimle gelirse, standart Fibo/OB aramak yerine yeni bir fonksiyon (_find_confluence_poi_entry) çalıştırın.

            Bu yeni fonksiyon, kırılımı yaratan hareket içindeki iç içe geçmiş FVG ve Order Block'ları aramalıdır.

            Kesişim Tespiti: Bir FVG'nin fiyat aralığı (top-bottom) ile bir OB'nin fiyat aralığının kesişip kesişmediğini kontrol edin.

            Eğer kesişim bulunursa, bu bölgenin başlangıcını (örn: Bullish için OB'nin high değeri) giriş noktası olarak belirleyin.

Strateji 2: 1 Saatlik BOS + OTE/OB Kesişimi (Trende Katılım)

Bu strateji, ana trend yönünde, en uygun geri çekilme seviyesinden işleme girmek için idealdir.

    Mantık:

        Fiyat, ana trend yönünde 1 saatlik zaman diliminde net bir yapı kırılımı (1h BOS) gerçekleştirir.

        Fiyat, bu kırılımı yapan "impulse" bacağının geri çekilmesini yapmaya başlar.

        Geri çekilme, Fibonacci'nin OTE (Optimal Trade Entry) bölgesi olan %70.5 - %78.6 aralığına kadar devam eder.

        Bu OTE bölgesinin içinde aynı zamanda geçerli bir Order Block da bulunur.

        İşleme giriş, bu OTE + OB kesişiminden yapılır.

    Uygulama Planı:

        Sinyali Puanla (scoring_system.py):

            _calculate_other_timeframe_scores metodunu güncelleyin.

            Eğer ana işlem yönü (trade_direction) ile uyumlu bir 1h BOS tespit edilirse, bu teyit için yüksek bir puan verin (örn: ****).

        Giriş Noktasını Hesapla (smart_entry_strategy.py):

            _determine_ideal_entry fonksiyonu içinde, Fibo ve OB stratejilerini birleştiren yeni bir mantık oluşturun.

            Fibonacci OTE: _calculate_fibonacci_entry fonksiyonu, artık sadece en yakın seviyeyi değil, OTE bölgesindeki seviyeleri de önceliklendirebilir.

            Kesişim Tespiti: Bir Fibo OTE seviyesi (0.705 veya 0.786) ile bir Order Block'un fiyat aralığının kesişip kesişmediğini kontrol edin.

            Eğer böyle bir "OTE+OB" kesişimi bulunursa, bu noktayı en yüksek öncelikli giriş noktası olarak belirleyin ve strateji adını ote_ob_confluence olarak kaydedin. fib_level olarak da "OTE" yazdırabilirsiniz.

Sonuç ve Özet

Bu iki stratejiyi eklemek, "Automaton" projenizi reaktif bir pattern tespit sisteminden, piyasanın likidite akışını ve yapısal niyetini anlayan proaktif bir analiz motoruna dönüştürecektir.

    Strateji 1, fiyatın "dönüş" yapma olasılığının en yüksek olduğu tuzak (stop-hunt) anlarını yakalar.

    Strateji 2, established bir trende en güvenli ve en karlı yerden "trene atlama" fırsatlarını kollar.

Bir işlem stratejisinde giriş noktaları, riskin en aza indirilmesi ve potansiyel kârın en üst düzeye çıkarılması açısından kritik öneme sahiptir. ICT (Inner Circle Trader) metodolojisi, piyasa analizi ve akıllı para konseptlerini birleştirerek yüksek olasılıklı giriş noktaları belirlemeyi hedefler. İşte işleme giriş stratejileri ve nasıl olmaları gerektiği, örneklerle birlikte:

### İşleme Giriş Stratejilerinin Temel Prensipleri

ICT ticaretinde girişler, piyasa yapısı, likidite havuzları ve piyasadaki verimsizlikler gibi temel konseptlere dayanır. Amaç, fiyatın akıllı paranın pozisyonlarını oluşturduğu veya dengelediği kritik seviyelere geri çekilmesini beklemektir.

### 1. Yüksek Zaman Dilimi (HTF) Analizi ve Önyargı (Bias) Belirleme

HTF analizi, piyasanın genel eğilimini ve potansiyel dönüş veya devam alanlarını belirlemek için kullanılır. Günlük, Haftalık, Aylık ve 4 saatlik grafikler, yüksek zaman dilimi olarak kabul edilir. Kurumlar genellikle piyasaları günlük, haftalık ve aylık bazda analiz eder.

*   **Önyargı (Bias) Belirleme:** Fiyatın bir sonraki hareketinin yukarı mı yoksa aşağı mı daha olası olduğunu belirlemek çok önemlidir. Doğru bir önyargıya sahip olmak, başarılı işlem olasılığını önemli ölçüde artırır. Örneğin, yükseliş trendindeyseniz, düşüş yönlü kurulumları göz ardı edersiniz. Trader Mayne'e göre, bir trader'ın en büyük sorunlarından biri önyargısını belirleyememesidir.
*   **İlgi Noktaları (Points of Interest - POIs):** HTF analizinde, **Order Block'lar (OB'ler)**, **Breaker'lar**, **Fair Value Gap'ler (FVG'ler)** ve **Likidite Seviyeleri** gibi önemli ilgi noktaları (POIs) belirlenir. Bu POI'ler, akıllı paranın piyasada bıraktığı ayak izleri olarak kabul edilir ve fiyatın bu bölgelere geri dönme eğiliminde olduğu düşünülür.
    *   **Order Block (OB):** Piyasa Yapısı Kırılımından (MSB) önceki zıt yöndeki son mum çubuğudur. Boğa OB, yükseliş yönlü bir MSB'den önceki son düşüş (ayı) mum çubuğu iken, Ayı OB, düşüş yönlü bir MSB'den önceki son yükseliş (boğa) mum çubuğudur. Fiyat bir OB'ye geri döndüğünde, akıllı paranın pozisyonlarını dengelemek veya yeni pozisyonlar eklemek için bir fırsat olarak görülür.
    *   **Breaker Block:** Piyasa Yapısı Kırılımına (MSB) yol açan, fiyat tarafından ihlal edilmiş en son Order Block'tur. Breaker'lar, piyasanın yön değiştirdiğini teyit eden ve potansiyel giriş noktaları sunan önemli yapılardır.
    *   **Fair Value Gap (FVG):** Genellikle üç mum çubuğu formasyonuyla tanımlanır; birinci mumun yüksekliği ile üçüncü mumun düşüklüğü arasında bir boşluk olması durumunda oluşur. FVG'ler, piyasanın bu boşlukları doldurma eğiliminde olduğu "çekim alanları" olarak işlev görür.
    *   **Likidite Seviyeleri / İçsel HTF Pivotları:** Yüksek zaman dilimindeki önemli destek ve direnç seviyeleridir ve akıllı paranın emirlerinin bulunduğu likidite havuzları olarak görülür.
*   **Premium ve İskonto Alanları:** Bir işlem aralığı belirlendikten sonra, aralığın %50 seviyesinin üzerindeki kısım "premium" (pahalı) bölge, altındaki kısım ise "iskonto" (ucuz) bölge olarak adlandırılır. Yükseliş trendinde iskontodan alım yapmak, düşüş trendinde ise premiumdan satış yapmak optimaldir.

### 2. Düşük Zaman Dilimi (LTF) Giriş Tetikleyicileri

HTF analizi genel yönü belirlerken, LTF analizi hassas giriş noktalarını bulmak için kullanılır.

*   **Swing Failure Pattern (SFP):** Fiyatın önemli bir swing yüksekliğini veya düşüklüğünü (likidite havuzu) kısa süreliğine ihlal edip hemen ardından tersine dönmesiyle oluşur. Bu, akıllı paranın stop-loss emirlerini tetikleyerek veya likiditeyi emerek pozisyonlarını oluşturduğu bir manipülasyon olarak görülür. LTF'de bir SFP'nin ardından gelen bir piyasa yapısı kırılımı (MSB) veya kayması (MSS), güçlü bir dönüş sinyali olabilir. SFP'ler genellikle aralık sahtekarlıklarını (fakeout) tespit etmek için de kullanılır.
*   **Piyasa Yapısı Kırılımı (MSB) / Piyasa Yapısı Kayması (MSS):** MSB, fiyatın önceki bir yüksek veya düşüğü kırarak piyasa yapısında net bir değişiklik olduğunu gösterirken, MSS daha hassas bir piyasa yapısı değişikliğini ifade eder ve genellikle bir SFP sonrası oluşur. MSS'ler daha erken sinyaller verebilir ancak daha fazla yanlış sinyal potansiyeli taşır.
*   **Judas Swing (Piyasa Protraction):** Piyasa yapıcıların perakende tüccarları yanlış yöne çekmek için tasarladığı sahte bir fiyat hareketidir. Genellikle yakındaki likiditeyi toplamak için kısa süreli bir genişleme olarak gerçekleşir ve ardından gerçek yönde bir dönüş yapar.

### 3. İşleme Giriş Stratejileri (Türleri)

ICT metodolojisinde iki ana giriş türü bulunur: Risk Girişi ve Onay Girişi.

*   **1. Risk Girişi (Risk Entry):**
    *   Bu, herhangi bir ek onay gerektirmeyen agresif bir giriş türüdür.
    *   Belirlenen POI'ye (Order Block, Breaker, FVG) fiyatın geri çekilmesi beklenir ve limit emriyle doğrudan giriş yapılır.
    *   Zaman kısıtlaması olan trader'lar için idealdir, çünkü emir verildikten sonra aktif izleme gerektirmez ("set and forget").
    *   Daha düşük bir kazanma oranına sahip olabilir, ancak potansiyel olarak daha iyi risk-ödül oranları sunar çünkü daha erken bir fiyattan giriş yapar.
    *   Önyargının net olduğu ve yüksek zaman dilimi bağlamının güçlü olduğu durumlarda tercih edilebilir.

*   **2. Onay Girişi (Confirmation Entry):**
    *   Bu, daha konservatif bir giriş türüdür ve özellikle HTF'den onay gerektirir.
    *   Fiyatın HTF POI'ye ulaşmasından sonra, düşük zaman diliminde (LTF) bir dönüş sinyali (örneğin, bir SFP veya piyasa yapısı kırılımı) beklenir.
    *   HTF POI ile LTF POI arasındaki aralık büyük olduğunda veya trend yönlü işlem yaparken tercih edilir.
    *   Daha yüksek bir kazanma oranına sahip olabilir, ancak bazen daha büyük stop-loss mesafeleri veya fırsatın kaçırılması riski taşıyabilir.

### Giriş Modları ve Uygulama (IMG Göstergesi Destekli)

IMG Göstergesi gibi araçlar, bu giriş modlarını otomatik olarak tespit ederek trader'lara yardımcı olur.

*   **Yapı Kırılımı Türü (MSB vs MSS):** Girişin temelini oluşturan piyasa yapısı kırılımının türünü belirler.
*   **LTF Giriş Modu:** HTF analizinden sonra hassas giriş noktalarını belirlemek için kullanılır.
    *   **MSB Seviyesi:** LTF'de bir MSB seviyesinde giriş.
    *   **Breaker:** LTF'de bir Breaker Block'a geri çekilme sonrası giriş.
    *   **Raid (Süpürme):** Likidite havuzlarına yapılan bir "raid" sonrası giriş.
    *   **OTE (Optimal Trade Entry) 70.5%:** Fibonacci geri çekilme seviyeleri kullanılarak belirlenen optimal ticaret giriş bölgesi. Özellikle %62 ve %79 arasındaki bölge, yüksek olasılıklı geri çekilme girişleri için tercih edilir.
*   **HTF Premium / İskonto:** Fiyatın bir ticaret aralığının premium (pahalı) bölgesinde satış, indirim (ucuz) bölgesinde ise alım yapılması tercih edilir. Bu, risk-ödül oranını optimize etmeye yardımcı olur.
*   **LTF SFP in Valid Setup (İkincil Onay):** Geçerli bir kurulumda LTF SFP ikincil bir onay olarak kullanılır ve HTF'deki bir ticaret kurulumunu daha da güçlendirebilir.

### Örneklerle İşlem Stratejisi Oluşturma

**Örnek: Pazartesi Aralığı Stratejisi (Monday's Range Strategy)**

Bu strateji, yüksek zaman dilimi analizi ve sapma kavramlarını birleştirerek basit ama etkili bir giriş stratejisi sunar:

1.  **Pazartesi Aralığını Belirle:** Pazartesi günü oluşan en yüksek ve en düşük seviyeler belirlenir. Trader Mayne'e göre, "key levels by spaceman BTC" göstergesi gibi araçlar Pazartesi aralığı, haftalık açılış, günlük yüksek/düşük gibi seviyeleri otomatik olarak gösterebilir.
2.  **Sapma (Deviation) Bekle:** Genellikle Salı veya Çarşamba günü, fiyatın Pazartesi aralığının bir tarafını (yüksek veya düşük) ihlal etmesini bekleyin. Bu bir "sapma" veya "stop run" olarak da adlandırılır.
3.  **Aralığa Geri Dönüş ve Giriş:** Fiyat Pazartesi aralığının dışına çıktıktan sonra, tekrar aralığın içine kapanırsa, bu bir giriş sinyali olarak kabul edilir.
    *   **Yükseliş Önyargısı İçin:** Fiyat Pazartesi düşük seviyesinin altına iner ve sonra aralığa geri kapanırsa, uzun (long) pozisyona girilir. Stop-loss, sapmanın altına yerleştirilir. Hedef, Pazartesi yüksek seviyesidir.
    *   **Düşüş Önyargısı İçin:** Fiyat Pazartesi yüksek seviyesinin üzerine çıkar ve sonra aralığa geri kapanırsa, kısa (short) pozisyona girilir. Stop-loss, sapmanın üzerine yerleştirilir. Hedef, Pazartesi düşük seviyesidir.
4.  **Zaman Dilimi Uyumu:** Bu stratejinin en basit hali, saatlik kapanışın aralık içine dönmesini beklemektir. Daha hassas girişler için 5 dakikalık grafik gibi düşük zaman dilimlerine inilebilir ve burada Order Block, Breaker veya SFP gibi LTF onayları aranır. Ancak bu LTF onaylarının, HTF önyargısıyla (Pazartesi aralığından gelen) uyumlu olması önemlidir.
5.  **Örnek Senaryo:** Fiyat Pazartesi düşük seviyesinin altına iner, sonra tekrar aralığa girer ve saatlik mum aralık içinde kapanır. Bu durumda, aralığa geri kapanan mumun kapanışından hemen sonra uzun pozisyona girilir. Stop-loss, sapmanın en düşük noktasının altına konulur ve kar hedefi Pazartesi yüksek seviyesidir. Bu, genellikle 1.5R civarında bir risk-ödül oranı sunar. Bu strateji, haftalık, aylık veya herhangi bir aralıkta da uygulanabilir.

### Risk Yönetimi ve Pozisyon Boyutlandırma (Girişlerle Bütünleştirme)

*   **Stop Loss Yerleşimi:** Genellikle dar stop-loss yerleşimine olanak tanır. Stop-loss, sapmanın (deviation) altına veya üzerine, ya da belirlenen Order Block/Breaker'ın altına/üstüne yerleştirilir.
*   **Hedef Belirleme:** Hedefler genellikle aralığın karşı tarafı (Pazartesi aralığı örneğinde olduğu gibi) veya bir sonraki dış aralık likidite seviyesi (external range liquidity) ya da HTF POI'si olabilir.
*   **Kısmi Kar Alma:** İlk kar hedeflerine ulaşıldığında pozisyonun bir kısmını kapatarak riski azaltmak ve kalan pozisyonu daha büyük hedefler için tutmak yaygın bir stratejidir.
*   **Risk Yönetimi Ayarları:** Ticaret başına maksimum risk yüzdesi belirlemek (örneğin %1 veya %2) ve bu riski aşmamak çok önemlidir. IMG göstergesinde sermaye, lot boyutu, işlem ücretleri gibi birçok risk yönetim parametresi ayarlanabilir.

### Sonuç

Yüksek zaman dilimi analizi ve sapma kavramlarını bir işlem stratejisine dahil etmek, piyasanın genel eğilimini anlamanızı ve akıllı paranın manipülasyonlarını kullanarak hassas giriş noktaları bulmanızı sağlar. Bu, piyasadaki "gürültüyü" azaltır ve işlemlerinizin olasılığını önemli ölçüde artırır. **Disiplinli risk yönetimi** ve **sürekli pratik** ile bu konseptler uzun vadede başarılı bir işlem kariyeri oluşturmanın temelini atabilir. ICT metodolojisi, HTF'deki piyasa yapısını ve POI'leri belirleyerek genel önyargıyı sağlar ve ardından LTF'deki sapma veya yapı kırılımı gibi tetikleyicilerle hassas girişler bulmayı hedefler.


İşlem stratejilerinde giriş noktaları, risk yönetimi ve kar potansiyelini optimize etmek açısından büyük önem taşır. ICT (Inner Circle Trader) metodolojisi, çeşitli piyasa analizi konseptlerini bir araya getirerek yüksek olasılıklı giriş noktaları belirlemeyi hedefler. Sorgunuzda bahsedilen "CVD/Open Interest", "VWAP" ve "Divergence" gibi kavramların ICT giriş stratejileriyle nasıl birleştirilebileceğine dair kaynaklarda yer alan bilgiler aşağıda örneklerle açıklanmıştır:

### ICT Giriş Stratejilerinin Temelleri ve Destekleyici Konseptler

ICT ticaretinde girişler, piyasa yapısı, likidite havuzları ve piyasadaki verimsizlikler gibi temel konseptlere dayanır. Amaç, fiyatın akıllı paranın pozisyonlarını oluşturduğu veya dengelediği kritik seviyelere geri çekilmesini beklemektir. Bu, genellikle **Yüksek Zaman Dilimi (HTF) analizi ile genel piyasa eğiliminin (önyargı/bias) belirlenmesi** ve ardından **Düşük Zaman Dilimi (LTF) onay tetikleyicileriyle hassas giriş noktalarının bulunması** süreçlerini içerir.

IMG göstergesi gibi araçlar, bu süreçlerde önemli rol oynar ve **Order Block'lar (OB'ler)**, **Breaker'lar**, **Fair Value Gap'ler (FVG'ler)** ve **Likidite Seviyeleri (Internal HTF Pivots)** gibi temel "İlgi Noktalarını" (POIs) otomatik olarak tespit eder. Bu POI'ler, akıllı paranın piyasada bıraktığı ayak izleri olarak kabul edilir ve fiyatın bu bölgelere geri dönme eğiliminde olduğu düşünülür.

### 1. Açık Faiz (Open Interest - OI) / Ticaret Temsilcileri Raporu (COT) ile Birlikte Kullanım

Kaynaklarda "CVD" (Cumulative Volume Delta) terimi doğrudan yer almasa da, "Açık Faiz" (Open Interest) ve "Ticaret Temsilcileri Raporu" (Commitment of Traders - COT) bilgileri, hacim ve kurumsal pozisyonlanma analiziyle yakından ilişkilidir ve smart money'nin niyetini anlamak için kullanılır.

*   **Tanım ve Önemi:** Açık Faiz (OI), vadeli işlem enstrümanına giren toplam long ve short pozisyonların sayısıdır [119a]. COT raporu, ticari trader'ların ("Smart Money") pozisyonlarını gösterir ve piyasadaki büyük bir trend değişikliğinin habercisi olabilir [115a, 122a, 277]. Ticari trader'lar, piyasadaki açık faizin çoğunluğuna karşı pozisyon alan en bilgili gruptur [122a].
*   **Giriş Stratejileriyle Entegrasyon Örnekleri:**
    *   **Trend Onayı ve Güçlü Hareketler:** Açık faizin artması, spekülatörlerin mevcut piyasa yönüne daha agresif bahis oynadığını gösterir ve mevcut trendin devamını destekler, ancak yeni bilgi akışıyla duyarlılık değişirse bu durum değişebilir [123a]. Tersine, açık faizin azalması, spekülatörlerin piyasadan para çektiğini ve duyarlılıkta bir değişiklik olduğunu gösterir [124b].
    *   **Belirleyici Sinyaller:** Açık faiz hızlı bir şekilde yükselir veya düşerse, fiyatın muhtemelen ters yönde hareket edeceği belirtilir [125e].
    *   **Konsolidasyon Dönemlerinde Kullanım:** Eğer fiyat bir konsolidasyon aralığına girer ve sonra açık faiz %20 veya daha fazla düşerse, bu ticari oyuncuların short pozisyonlarını kapattığını ve fiyatın yükseleceğini beklediklerini gösterir. Tam tersi, eğer açık faiz %20 veya daha fazla artarsa, bu ticari oyuncuların short pozisyonlarını artırdığını gösterir.
    *   **Örnek Senaryo:** "Vadeli işlem sözleşmelerinde prim varsa ve açık faiz düşüyorsa, bu neredeyse mükemmel bir senaryodur... alın!". Bu, belirli bir emtia veya para birimi için yüksek bir talebi işaret eder.
    *   **Yüksek Zaman Dilimi Onayı:** Boğa koşullarında ve destek seviyesindeyken açık faizde hızlı bir düşüş (%20 veya daha fazla), bir AL sinyali olarak değerlendirilir. Ayı koşullarında ve direnç seviyesindeyken açık faizde hızlı bir artış (%20 veya daha fazla), bir SAT sinyali olarak değerlendirilir.
    *   **Pazartesi Aralığı Stratejisi ile Birlikte:** Pazartesi aralığı örneğinde (bkz. genel ICT açıklamasında), eğer Pazartesi aralığının altında bir sapma (deviation) olur ve fiyat aralığa geri kapanırsa (yükseliş önyargısı), buna ek olarak açık faizde de bir düşüş gözlemlenirse, bu ticari oyuncuların pozisyonlarını azalttığını ve daha yüksek fiyatlar beklediğini teyit eder, böylece **giriş sinyalinin olasılığını artırır**. Bu, biasın (önyargının) belirlenmesine yardımcı olur.
    *   **Tek Atış Tek Öldürüş (One Shot One Kill - OSOK) Kurulumları:** COT ve Açık Faiz verileri, Smart Money'nin bir trade fikrinin arkasında olup olmadığını doğrulamak için OSOK trade prosedüründe kullanılır.

### 2. VWAP (Volume Weighted Average Price) ile Birlikte Kullanım

VWAP, hacim ağırlıklı ortalama fiyattır ve adil bir fiyatı temsil eder. Kurumsal faaliyetleri ortaya çıkarmaya yardımcı olması VWAP'nin güçlü yönlerinden biridir. Ancak kaynaklarda VWAP'nin doğrudan Order Block, FVG gibi spesifik ICT giriş yapılarıyla birleştirildiğine dair açık bir örnek bulunmamaktadır. Bunun yerine, VWAP daha çok bir **filtre** ve **ticaret yönetimi aracı** olarak ele alınmıştır:

*   **Ticaret Bölgesi Belirleme:** Fiyat VWAP'a yakınsa işlem yapmaktan kaçınılmalıdır çünkü bu adil bir fiyat bölgesidir ve fiyatın dar bir aralıkta kalma riski vardır.
*   **Giriş ve Çıkış Yönü Filtresi:**
    *   **Sadece VWAP'ın Üzerinde Uzun:** Fiyat VWAP'ın üzerindeyse sadece uzun pozisyonlar aranmalı ve kısa sinyaller göz ardı edilmelidir.
    *   **Sadece VWAP'ın Altında Kısa:** Fiyat VWAP'ın altındaysa sadece kısa pozisyonlar aranmalı ve uzun sinyaller göz ardı edilmelidir.
    *   **Aşırı Noktalarda Kontra Trend:** Fiyat VWAP'tan "gerçekten uzak" (en az bir veya tercihen iki standart sapma kadar) ve ters yönde yeni bir trend gelişiyorsa, küresel trende karşı kısa pozisyonlar düşünülebilir.
*   **Kar Alma Hedefi:** Kısa pozisyon açıldığında, kar alma hedefi fiyatın VWAP'a düşmesiyle VWAP'ın hemen üzerinde olmalıdır.
*   **Kaçınılması Gerekenler:** VWAP ile fiyatın kesiştiği anlarda işlem yapılmamalı, bunun yerine VWAP'a doğru geri çekilmeler veya toparlanmalar aranmalıdır. VWAP yakınında ayı ve boğa tuzaklarına dikkat edilmelidir.

### 3. Supertrend ile Birlikte Kullanım

Verilen kaynaklarda "Supertrend" göstergesine dair herhangi bir bilgi bulunmamaktadır. Dolayısıyla bu göstergenin ICT konseptleriyle nasıl kullanıldığına dair bir örnek verilememektedir.

### 4. Diverjans (Divergence) ile Birlikte Kullanım

ICT metodolojisinde diverjans, özellikle "SMT (Smart Money Tool) Divergence" olarak adlandırılan bir konseptle önemli bir teyit sinyali olarak kullanılır. Göstergelerle fiyat arasındaki uyumsuzluklar, trend dönüşlerini veya devamlarını öngörmek için kilit öneme sahiptir.

*   **Tanım ve Önemi:** SMT Divergence, korelasyonlu veya ters korelasyonlu enstrümanlar arasındaki fiyat hareketlerinde görülen sapmalardır. Bu sapmalar, smart money'nin pozisyon topladığını (akumülasyon) veya dağıttığını (distribüsyon) gösterebilir.
*   **Giriş Stratejileriyle Entegrasyon Örnekleri:**
    *   **Günlük Yüksek/Düşük Tahmini:** Scalping yöntemlerinde, günün en düşük veya en yüksek seviyesini tahmin etmek için SMT Divergence aranması önerilir [170b.v, 171b.v].
    *   **Faiz Oranı Diverjansı:** 2, 5, 10 ve 30 yıllık ABD hazine bonosu getirileri arasındaki diverjanslar, USDX'in gelecekteki yönü hakkında ipuçları sağlayabilir [117f, 168, 386]. Örneğin, ABD getirisinin yükselirken diğer ülkelerin getirilerinin düşmesi (diverjans), piyasada zayıflık olacağına dair bir teyit olarak yorumlanabilir.
    *   **Korelasyonlu Çiftlerde Onay:** EUR/USD ve GBP/USD gibi korelasyonlu çiftler arasında SMT Divergence oluştuğunda, bu her iki çift için de bir dönüş sinyali olabilir. Örneğin, GBP'nin daha yüksek bir tepe yaparken EUR'un yapmaması, her iki çift için de bir düşüş sinyali olarak görülebilir. Ters korelasyonlu USDX ile de SMT Divergence izlenir.
    *   **Order Block ve POI'lerle Birlikte:** Fiyat bir HTF POI'ye (Order Block, Breaker, FVG) geri çekildiğinde ve LTF'de bir SMT Divergence (veya diğer gösterge diverjansları) oluşursa, bu o POI'nin geçerliliğini ve dönüş olasılığını teyit eden güçlü bir sinyaldir [168c].
    *   **Piyasa Yapısı Kırılımı ile Kombinasyon:** Trader Mayne, bir yükseliş trendinde Pazartesi düşük seviyesinin ihlal edilip aralığa geri dönülmesini takiben, düşüş yönlü bir SMT Divergence'ı göz ardı etmenin, çünkü bunun yüksek zaman dilimi bias'ıyla uyumlu olmadığını belirtir. Ancak, yükseliş yönlü bir SFP veya Order Block gibi bir teyit kurulumunun SMT Divergence ile birlikte oluşması, **güçlü bir giriş fırsatı** sunar.

### Sonuç

İşleme giriş stratejileri, yüksek zaman dilimi biasının belirlenmesi, Smart Money'nin ayak izlerini (Order Block, Breaker, FVG, Likidite) takip eden ilgi noktalarının tespiti ve düşük zaman diliminde onay tetikleyicilerinin (SFP, MSS) aranması üzerine kuruludur. Açık Faiz ve SMT Divergence gibi ek konseptler, bu temel giriş kurulumlarının **olasılığını ve güvenilirliğini artırmak için birer teyit ve filtre** görevi görür. VWAP ise daha çok işlem yapma veya yapmama kararlarını etkileyen bir piyasa durumu göstergesi ve genel trend filtresi olarak kullanılır. Bu konseptleri bir araya getirmek, piyasadaki "gürültüyü" azaltır ve daha disiplinli ve yüksek olasılıklı işlemler yapılmasına olanak tanır.


Kaynaklarda, işleme giriş stratejileri ICT (Inner Circle Trader) metodolojisinin temel taşlarından biri olarak detaylı bir şekilde açıklanmaktadır. Genel olarak, bir işlemin giriş noktası, piyasa yapısı, likidite havuzları ve piyasadaki verimsizliklerin dikkatli bir analizi sonucunda belirlenir. Bu analiz, genellikle Yüksek Zaman Dilimi (HTF) ve Düşük Zaman Dilimi (LTF) kavramlarının birleştirilmesiyle yapılır.

İşleme girişler, piyasadaki "Smart Money"nin (kurumsal yatırımcılar) bıraktığı ayak izlerini takip etme felsefesine dayanır. Amaç, fiyatın, Smart Money'nin pozisyonlarını oluşturduğu veya dengelediği kritik seviyelere geri çekilmesini beklemektir.

İşleme giriş stratejileri ve ilgili konseptler aşağıdaki gibi özetlenebilir:

### 1. Temel ICT Giriş Prensipleri

*   **Çoklu Zaman Dilimi Analizi (Top-Down Analysis)**:
    *   ICT ticaretinde girişler, yüksek zaman diliminde (HTF) genel piyasa yönünün veya "bias"ın belirlenmesiyle başlar (örn. aylık, haftalık, günlük, 4 saatlik grafikler).
    *   Ardından, düşük zaman dilimlerinde (LTF) (örn. 1 saatlik, 15 dakikalık, 5 dakikalık) bu HTF bias ile uyumlu hassas giriş noktaları aranır. Bu yaklaşım, stop-loss mesafelerini daraltarak riski azaltmaya ve risk-ödül oranlarını iyileştirmeye olanak tanır.
    *   "Monday's Range" stratejisi gibi basit yöntemler, intraday (gün içi) bias belirlemek için kullanılabilir. Pazartesi aralığının altına veya üstüne bir sapma (deviation) olup fiyatın tekrar aralığa kapanmasıyla bias doğrulanır.
*   **Piyasa Yapısı (Market Structure - MS) ve Likidite**:
    *   Piyasa yapısının doğru bir şekilde belirlenmesi (yüksek zirveler, yüksek dipler veya alçak zirveler, alçak dipler) bir işlem için bias oluşturur.
    *   **Likidite Havuzları**: Piyasa, eski yüksekler ve düşükler (Old Highs/Lows), temiz yüksekler/düşükler (Clean Highs/Lows) gibi likidite havuzlarına doğru hareket etme eğilimindedir. Bu seviyeler, Smart Money'nin stop-loss'ları tetiklediği veya emirlerini topladığı yerlerdir.
    *   **Dışsal ve İçsel Aralık Likiditesi**: Fiyatın dışsal aralık likiditesini (daha geniş aralıklardaki likidite) temizledikten sonra, içsel aralık likiditesine (bir Fibonacci aralığının içindeki veya Order Block, FVG gibi POI'lardaki likidite) geri dönmesi beklenir.

### 2. Temel İlgi Noktaları (Points of Interest - POIs) ve Kurulumlar

IMG göstergesi gibi araçlar, bu ilgi noktalarını otomatik olarak tespit eder.

*   **Order Block (OB)**:
    *   Piyasa Yapısı Kırılımı (Market Structure Break - MSB) öncesindeki zıt yöndeki son mum çubuğudur. Örneğin, boğa yönlü bir MSB'den önceki son ayı (düşüş) mum çubuğu boğa OB'sidir.
    *   Fiyatın bu bloklara geri dönmesi, Smart Money'nin daha önce açtığı pozisyonları dengelemek veya yeni pozisyonlar eklemek için bir fırsat sunar.
    *   **OB Rafinasyonu**: Yüksek zaman dilimindeki (HTF) Order Block'lar, daha düşük zaman dilimlerinde (LTF) (örn. 5 dakikalık, 15 dakikalık) daha küçük ve hassas Order Block'lar bulunarak rafine edilebilir. Bu, daha dar stop-loss'lar ve daha iyi risk-ödül oranları sağlar.
    *   **İmbalans (FVG) ile Onay**: Bir Order Block'un yakınında oluşan Fair Value Gap'ler (FVG'ler) veya imbalanslar, kurumların aktif olduğunu ve OB'nin daha güçlü bir ilgi noktası olduğunu gösterir. Fiyatın bu imbalansı düzeltmek için geri dönme eğilimi vardır.
*   **Breaker Block**:
    *   Piyasa Yapısı Kırılımına (MSB) yol açan, fiyat tarafından ihlal edilmiş en son Order Block'tur.
    *   Fiyatın bu Breaker bölgesine geri dönmesi, genellikle piyasanın yeni yönde hareketine devam etmeden önce bir onay veya yeniden giriş fırsatı sunar.
*   **Fair Value Gap (FVG)**:
    *   Fiyatın hızlı ve dengesiz hareket ettiği, geride verimsiz fiyatlandırma bıraktığı bölgelerdir. Genellikle üç mum çubuğu formasyonuyla tanımlanır.
    *   ICT'de FVG'ler, piyasanın bu boşlukları doldurma eğiliminde olduğu "çekim alanları" olarak işlev görür.
*   **Optimal Trade Entry (OTE)**:
    *   Fibonacci geri çekilme seviyeleri (%62 ile %79 arası, özellikle %70.5) kullanılarak belirlenen ideal ticaret giriş bölgesidir. Fiyatın bir OTE bölgesine geri çekilmesi, yüksek olasılıklı bir geri çekilme girişini işaret eder.
    *   Fibonacci, mum çubuklarının gövdeleri kullanılarak çizilir, çünkü fitiller farklı brokerlar arasında farklılık gösterebilir.
*   **Swing Failure Pattern (SFP) / Judas Swing**:
    *   Bir aralık ekstreminde (yüksek veya düşük) fiyatın kısa süreliğine bir seviyeyi ihlal edip hemen geri dönmesiyle oluşan SFP'ler, likidite toplama operasyonlarını işaret eder. Bu, Smart Money'nin stop-loss'ları tetikleyerek veya likiditeyi emerek pozisyonlarını oluşturduğu bir manipülasyon olarak görülür.
    *   **Judas Swing**, genellikle günlük açılış fiyatının altına veya üstüne doğru yapılan bir manipülatif fiyattır ve gerçek hareketin tersi yönde gerçekleşir. Özellikle Londra Açılışında (London Open - LO) görülür ve ardından bir OTE'ye doğru geri çekilme beklenebilir.

### 3. Giriş Onayları ve Filtreler

*   **Piyasa Yapısı Kırılımı (MSB) ve Piyasa Yapısı Kayması (MSS)**:
    *   **MSB**, fiyatın önceki bir yüksek veya düşüğü kırarak piyasa yapısında net bir değişiklik olduğunu gösterir.
    *   **MSS**, daha hassas bir piyasa yapısı değişikliğini ifade eder ve genellikle bir SFP sonrası, fiyatın önceki bir pivotu kırarak yön değiştirdiğini gösterir. MSS, daha erken sinyaller verebilir ancak daha fazla yanlış sinyal potansiyeli taşır.
    *   Bir işlemin doğrulanması için genellikle bir itici hareketin (impulse) bir yapısal noktayı kırması ve ardından bu kırılımı onaylayan başarılı bir geri çekilme (successful retrace) gereklidir.
*   **Premium ve Discount Bölgeleri**:
    *   Piyasada bir hareket sonrası fiyatın 50% veya denge noktasının altına düşmesi "discount" (indirimli) bölgeyi, üstüne çıkması ise "premium" (pahalı) bölgeyi gösterir.
    *   Yükseliş trendlerinde indirimli bölgelerden alım yapmak, düşüş trendlerinde ise primli bölgelerden satım yapmak risk-ödül oranını optimize eder.
*   **Smart Money Tool (SMT) Divergence**:
    *   Korelasyonlu enstrümanlar (örn. EUR/USD ve GBP/USD veya ABD tahvil getirileri ile USDX) arasındaki fiyat hareketlerinde görülen sapmalardır.
    *   Bu sapmalar, Smart Money'nin pozisyon topladığını (akumülasyon) veya dağıttığını (distribüsyon) gösterebilir ve güçlü bir trend dönüşü veya devamı sinyali olarak kullanılır.
*   **COT (Commitment of Traders) Raporu ve Açık Faiz (Open Interest - OI)**:
    *   COT raporu, ticari trader'ların ("Smart Money") pozisyonlarını gösterir ve piyasadaki büyük trend değişikliklerinin habercisi olabilir.
    *   Açık faizin (OI) hızlı bir şekilde düşmesi (%20 veya daha fazla), ticari oyuncuların short pozisyonlarını kapattığını ve fiyatın yükseleceğini gösterir; hızlı bir artış ise short pozisyonlarını artırdığını gösterir. Bu veriler, HTF bias ve işlem teyidi için kullanılır.
*   **VWAP (Volume Weighted Average Price)**:
    *   VWAP, hacim ağırlıklı ortalama fiyattır ve "adil bir fiyatı" temsil eder.
    *   Fiyat VWAP'tan "gerçekten uzak" olduğunda (en az bir veya tercihen iki standart sapma kadar) işlem yapmak daha güvenlidir. Fiyat VWAP'a yakınsa veya kesişiyorsa işlem yapmaktan kaçınılması önerilir, çünkü bu adil fiyat bölgesinde fiyatın dar bir aralıkta kalma riski vardır.
    *   VWAP'ın üzerinde yalnızca uzun, altında ise yalnızca kısa pozisyonlar aranmalı ve ters sinyaller göz ardı edilmelidir. VWAP, kurumsal faaliyetleri ortaya çıkarmaya yardımcı olan bir filtre ve ticaret yönetimi aracı olarak kullanılır.
*   **Piyasa Seansları (Killzones)**:
    *   Belirli seanslar (örn. Asya, Londra, New York Killzones ve Londra Kapanışı) likidite ve volatilite açısından önemlidir. Bu zaman dilimlerinde oluşan Order Block'lar, FVG'ler veya likidite süpürmeleri yüksek olasılıklı ticaret fırsatları sunabilir.

### 4. Emir ve Risk Yönetimi

*   **Emir Türleri**:
    *   Bir kurulum onaylandığında, fiyat giriş modunun dışında kapanırsa **Limit Emir** verilmesi önerilir.
    *   Fiyat giriş modunun içinde kapanırsa **Piyasa Emri** verilmesi önerilir. Hassas girişler için Limit Emirleri tercih edilir.
*   **Stop Loss (SL) ve Kar Alma (TP)**:
    *   Stop-loss yerleşimi genellikle giriş noktasının veya Order Block'un altında/üstünde, veya SFP ekstremlerinin altında/üstünde belirlenir.
    *   İlk kar alma hedefi genellikle 1:1 Risk/Ödül oranı (örn. 20-30 pip) veya Fibonacci hedefleri (örn. 0.0, -0.27, -0.62) olarak belirlenir. Kısmi kar alımları (örn. %50 veya %70) pozisyonun geri kalanını riske sıfır veya kırılma noktasına getirmek için yapılır.
*   **Pozisyon Boyutlandırma ve Risk**: İşlem başına %1.5-2 veya daha az risk alınması tavsiye edilir. Düşük çekilmeyi (drawdown) korumak, uzun vadeli başarı için anahtardır.

### 5. Giriş Stratejisi Türleri

*   **Risk Girişleri (Risk Entries)**:
    *   Daha düşük bir kazanma oranına sahip olabilirler ancak "ayarla ve unut" (set and forget) mentalitesiyle daha az ekran başında zaman geçirmek isteyenler için uygundur.
    *   Genellikle bir ilk piyasa yapısı kırılımı (MSB) veya likidite çekişinden sonra oluşan bir Order Block'ta doğrudan giriş yapılır.
*   **Onay Girişleri (Confirmation Entries)**:
    *   Daha yüksek bir kazanma oranına sahiptirler ve fiyatın Order Block bölgesine girdikten sonra düşük zaman diliminde ek bir onay beklenmesini gerektirir.
    *   Bu onay, genellikle ikinci bir yapısal kırılım (successful retrace) veya bir Swing Failure Pattern (SFP) şeklinde olabilir, bu da işlemi daha yüksek olasılıklı hale getirir.

Yukarıda belirtilen konseptler, ICT metodolojisinin temelini oluşturur ve ticarete disiplinli, kurumsal bir yaklaşımla yaklaşmayı hedefler.
