import os
import requests
from typing import Optional, List, Dict, Any
from loguru import logger
from datetime import datetime
from utils import format_price_standard # Fiyat formatlama için
import time

class TelegramNotifier:
    """Telegram üzerinden bildirimleri yöneten sınıf"""
    
    def __init__(self, bot_token: str, chat_id: str):
        """
        Telegram bildirim sınıfını başlatır
        
        Args:
            bot_token: Telegram Bot API token'ı
            chat_id: Hedef sohbet ID'si
        """
        self.bot_token = bot_token
        self.chat_id = chat_id
        self.base_url = f"https://api.telegram.org/bot{self.bot_token}/"
        logger.info("Telegram bildirici başlatıldı")
        
    def send_message(self, message: str, parse_mode: Optional[str] = "Markdown") -> bool:
        """
        Telegram'a metin mesajı gönderir
        
        Args:
            message: Gönderilecek mesaj
            parse_mode: Telegram'a gönderilecek parse modu (Markdown, HTML veya None)
            
        Returns:
            bool: <PERSON><PERSON> ba<PERSON>arıyla gönderildiyse True
        """
        try:
            url = self.base_url + "sendMessage"
            payload = {
                "chat_id": self.chat_id,
                "text": message,
            }
            # Sadece None değilse parse_mode'u ekle
            if parse_mode is not None:
                payload["parse_mode"] = parse_mode
            
            response = requests.post(url, json=payload, timeout=10)
            response.raise_for_status()
            
            logger.debug(f"Telegram mesajı gönderildi: {message[:50]}...")
            return True
            
        except requests.exceptions.RequestException as e:
            logger.error(f"Telegram API isteği hatası: {e}")
            # Yanıt içeriğini logla (hata ayıklama için)
            if e.response is not None:
                 logger.error(f"Telegram API Yanıtı: {e.response.text}")
            return False
        except Exception as e:
            logger.error(f"Telegram mesajı gönderme sırasında beklenmeyen hata: {str(e)}")
            return False

    def send_photo(self, photo_path: str, caption: str, parse_mode: Optional[str] = None) -> bool:
        """
        Telegram'a başlık ile birlikte bir fotoğraf gönderir.

        Args:
            photo_path (str): Gönderilecek fotoğrafın dosya yolu.
            caption (str): Fotoğrafın başlığı (mesaj metni).
            parse_mode (Optional[str]): Başlık için parse modu.

        Returns:
            bool: Fotoğraf başarıyla gönderildiyse True.
        """
        if not os.path.exists(photo_path):
            logger.error(f"Gönderilecek fotoğraf bulunamadı: {photo_path}")
            return False

        # Telegram caption limiti (1024 karakter)
        if len(caption) > 1024:
            caption = caption[:1021] + "..."
            logger.warning("Telegram caption 1024 karakter limitini aştı ve kısaltıldı.")

        try:
            url = self.base_url + "sendPhoto"
            payload = {
                "chat_id": self.chat_id,
                "caption": caption,
            }
            if parse_mode:
                payload["parse_mode"] = parse_mode

            with open(photo_path, 'rb') as photo_file:
                files = {'photo': photo_file}
                response = requests.post(url, params=payload, files=files, timeout=20)
                response.raise_for_status()
            logger.debug(f"Telegram'a grafikli sinyal gönderildi: {caption[:50]}...")
            return True
        except Exception as e:
            logger.error(f"Telegram'a fotoğraf gönderilirken hata: {e}")
            return False
               
    def send_pattern_alert(self, symbol: str, timeframe: str, 
                             pattern_data: Dict[str, Any],
                             price: Optional[float],
                             score: Optional[float] = None) -> bool:
        """
        Pattern tespiti için formatlanmış bir uyarı gönderir
        
        Args:
            symbol: Kripto sembolü
            timeframe: Zaman dilimi
            pattern_data: Pattern bilgileri
            price: Güncel fiyat
            score: Varsa, pattern skoru
            
        Returns:
            bool: Mesaj başarıyla gönderildiyse True
        """
        pattern_name = pattern_data.get("name", "Bilinmeyen")
        pattern_type = pattern_data.get("type", "Bilinmeyen").upper()
        price_str = format_price_standard(price) if price is not None else "N/A"
        
        message = f"🚨 *Pattern Tespit Edildi!* 🚨\n\n"
        message += f"*Sembol:* `{symbol}`\n"
        message += f"*Zaman Dilimi:* `{timeframe}`\n"
        message += f"*Pattern:* `{pattern_name}` ({pattern_type})\n"
        message += f"*Fiyat:* `{price_str}`\n"
        
        if score is not None:
            message += f"*Skor:* `{score:.2f}`\n"
               
        message += f"\n🕒 _{self._get_current_time()}_"
        
        return self.send_message(message)
       
    def _get_current_time(self) -> str:
        """Güncel zamanı formatlı string olarak döndürür"""
        return datetime.now().strftime("%Y-%m-%d %H:%M:%S")
    
    def send_score_report(self, report_text: str) -> bool:
        """
        Puanlama raporunu gönderir (isteğe bağlı olarak uzun raporları böler)
        
        Args:
            report_text: Gönderilecek rapor metni
            
        Returns:
            bool: Tüm parçalar başarıyla gönderildiyse True
        """
        try:
            # Telegram mesaj boyutu sınırı
            MAX_LENGTH = 4000
            
            # Rapor çok uzunsa böl
            if len(report_text) > MAX_LENGTH:
                parts = [report_text[i:i+MAX_LENGTH] for i in range(0, len(report_text), MAX_LENGTH)]
                logger.info(f"Uzun rapor {len(parts)} parçaya bölündü")
                
                success = True
                for i, part in enumerate(parts):
                    part_header = f"📊 Rapor ({i+1}/{len(parts)}):\n\n"
                    success = success and self.send_message(part_header + part)
                    # Telegram hızlı mesaj limiti aşılmasın diye kısa bekleme
                    time.sleep(0.5)
                
                return success
            else:
                # Standart uzunlukta
                return self.send_message(report_text)
                
        except Exception as e:
            logger.error(f"Rapor gönderme hatası: {e}")
            return False

# Örnek kullanım (test için)
if __name__ == "__main__":
    # Ortam değişkenlerinden al (güvenli yol)
    BOT_TOKEN = os.getenv("TELEGRAM_BOT_TOKEN")
    CHAT_ID = os.getenv("TELEGRAM_CHAT_ID")

    if not BOT_TOKEN or not CHAT_ID:
        print("Lütfen TELEGRAM_BOT_TOKEN ve TELEGRAM_CHAT_ID ortam değişkenlerini ayarlayın.")
    else:
        notifier = TelegramNotifier(BOT_TOKEN, CHAT_ID)
        
        # Test Mesajı
        print("Basit mesaj gönderiliyor...")
        notifier.send_message("Merhaba! Bu bir test mesajıdır.")
        
        # Test Pattern Uyarısı
        print("Pattern uyarısı gönderiliyor...")
        test_pattern = {"name": "BOSHL", "type": "Bullish"}
        notifier.send_pattern_alert("BTCUSDT", "4s", test_pattern, 81500.50, 9.5)
        
        # Test Raporu (Kısa)
        print("Kısa rapor gönderiliyor...")
        short_report = "--- BOĞA ---\n1. BTCUSDT | Skor: 9.5\n--- AYI ---\n>> Sinyal yok."
        notifier.send_score_report(short_report)
        
        # Test Raporu (Uzun)
        print("Uzun rapor gönderiliyor (bölünmüş)...")
        long_report = "A" * 4500 # 4096 karakterden uzun
        notifier.send_score_report(long_report)
        
        print("Test tamamlandı.")
