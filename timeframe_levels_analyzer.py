import os
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
from bybit_client import BybitClient
from typing import Optional, Dict
from utils import format_price_standard
from loguru import logger

# Logs klasörünü oluştur (yoksa)
os.makedirs("logs", exist_ok=True)

# Loguru logger kullanıyoruz, ek yapılandırma gerekli değil

class TimeframeLevelsAnalyzer:
    def __init__(self):
        """Zaman dilimi bazlı açılış ve denge seviyelerini analiz eden sınıf"""
        self.client = BybitClient()
        # Son analiz zamanı
        self.last_analysis_time = {}
        # Seviye bilgilerini saklayacak sözlük
        self.levels = {}
        self.all_levels_data = {} # Tüm seviyeleri saklamak için
    
    def _get_week_start(self, dt):
        """Haftanın başlangıç tarihini bul (pazartesi)"""
        # Pazartesi = 0, Pazar = 6
        return dt - timedelta(days=dt.weekday())
    
    def _get_month_start(self, dt):
        """Ayın başlangıç tarihini bul"""
        return dt.replace(day=1)
    
    def analyze_timeframe_levels(self, symbol, timeframe):
        """
        Belirli bir sembol ve zaman dilimi için zaman dilimi bazlı seviyeleri analiz et
        
        Args:
            symbol (str): Sembol (örn. "BTCUSDT")
            timeframe (str): Zaman dilimi (örn. "60", "240")
            
        Returns:
            dict: Zaman dilimi bazlı seviyeler
        """
        key = f"{symbol}_{timeframe}"
        
        # Son analizden bu yana 4 saat geçip geçmediğini kontrol et
        now = datetime.now()
        if key in self.last_analysis_time:
            hours_since_last_analysis = (now - self.last_analysis_time[key]).total_seconds() / 3600
            if hours_since_last_analysis < 4:
                logger.debug(f"{key} için son analizden bu yana {hours_since_last_analysis:.1f} saat geçti, atlıyorum")
                return self.levels.get(key, {})
        
        try:
            # Günlük, haftalık ve aylık veriler için yeterli veriyi çekelim
            # Analiz için en az 40 günlük veri
            daily_candles = self.client.fetch_klines(symbol, "D", 40)
            
            if daily_candles is None or len(daily_candles) < 7:
                logger.warning(f"{symbol} için yeterli günlük veri bulunamadı!")
                return {}
            
            # Şimdiki zaman bilgisi
            now = datetime.now()
            
            # 1. Günlük Açılış (DO)
            today = now.replace(hour=0, minute=0, second=0, microsecond=0)
            today_candle = daily_candles[daily_candles['timestamp'].dt.date == today.date()]
            
            if len(today_candle) > 0:
                daily_open = today_candle.iloc[0]['open']
            else:
                # Bugün için mum yoksa, son mumun kapanış değerini kullan
                daily_open = daily_candles.iloc[-1]['close']
            
            # 2. Haftalık Açılış (WO)
            week_start = self._get_week_start(now)
            week_candle = daily_candles[daily_candles['timestamp'].dt.date == week_start.date()]
            
            if len(week_candle) > 0:
                weekly_open = week_candle.iloc[0]['open']
            else:
                # Bu hafta için mum yoksa, en yakın önceki haftanın ilk mumunu bul
                prev_days = 1
                while len(week_candle) == 0 and prev_days < 7:
                    check_date = week_start - timedelta(days=prev_days)
                    week_candle = daily_candles[daily_candles['timestamp'].dt.date == check_date.date()]
                    prev_days += 1
                
                if len(week_candle) > 0:
                    weekly_open = week_candle.iloc[0]['close']
                else:
                    weekly_open = daily_candles.iloc[-1]['close']
            
            # 3. Aylık Açılış (MO)
            month_start = self._get_month_start(now)
            month_candle = daily_candles[daily_candles['timestamp'].dt.date == month_start.date()]
            
            if len(month_candle) > 0:
                monthly_open = month_candle.iloc[0]['open']
            else:
                # Bu ay için mum yoksa, en yakın önceki ayın ilk mumunu bul
                prev_days = 1
                while len(month_candle) == 0 and prev_days < 31:
                    check_date = month_start - timedelta(days=prev_days)
                    month_candle = daily_candles[daily_candles['timestamp'].dt.date == check_date.date()]
                    prev_days += 1
                
                if len(month_candle) > 0:
                    monthly_open = month_candle.iloc[0]['close']
                else:
                    monthly_open = daily_candles.iloc[-1]['close']
            
            # 4. Equilibrium Seviyeleri (Range Ortası)
            # Günlük EQ
            today_high = daily_candles[daily_candles['timestamp'].dt.date == today.date()]['high'].max() if len(today_candle) > 0 else None
            today_low = daily_candles[daily_candles['timestamp'].dt.date == today.date()]['low'].min() if len(today_candle) > 0 else None
            
            if today_high is not None and today_low is not None:
                eq_daily = (today_high + today_low) / 2
            else:
                # Bugün için veri yoksa, son mumun yüksek ve düşük değerlerini kullan
                eq_daily = (daily_candles.iloc[-1]['high'] + daily_candles.iloc[-1]['low']) / 2
            
            # Haftalık EQ
            # Bu haftanın tüm günlerinin mumlarını al
            this_week_candles = daily_candles[daily_candles['timestamp'] >= week_start]
            if len(this_week_candles) > 0:
                week_high = this_week_candles['high'].max()
                week_low = this_week_candles['low'].min()
                eq_weekly = (week_high + week_low) / 2
            else:
                eq_weekly = (daily_candles.iloc[-1]['high'] + daily_candles.iloc[-1]['low']) / 2
            
            # Aylık EQ
            # Bu ayın tüm günlerinin mumlarını al
            this_month_candles = daily_candles[daily_candles['timestamp'] >= month_start]
            if len(this_month_candles) > 0:
                month_high = this_month_candles['high'].max()
                month_low = this_month_candles['low'].min()
                eq_monthly = (month_high + month_low) / 2
            else:
                eq_monthly = (daily_candles.iloc[-1]['high'] + daily_candles.iloc[-1]['low']) / 2
            
            # 5. Monday Range (MR) ve Monday Range EQ (MREq)
            # Pazartesi gününü bul (haftanın başlangıcı)
            monday_date = self._get_week_start(now)
            monday_candle = daily_candles[daily_candles['timestamp'].dt.date == monday_date.date()]
            
            if len(monday_candle) > 0:
                monday_high = monday_candle['high'].max()
                monday_low = monday_candle['low'].min()
                monday_range = [monday_low, monday_high]
                monday_range_eq = (monday_high + monday_low) / 2
            else:
                # Pazartesi için veri yoksa, önceki pazartesiyi bul
                prev_monday = monday_date - timedelta(days=7)
                prev_monday_candle = daily_candles[daily_candles['timestamp'].dt.date == prev_monday.date()]
                
                if len(prev_monday_candle) > 0:
                    monday_high = prev_monday_candle['high'].max()
                    monday_low = prev_monday_candle['low'].min()
                    monday_range = [monday_low, monday_high]
                    monday_range_eq = (monday_high + monday_low) / 2
                else:
                    monday_range = [None, None]
                    monday_range_eq = None
            
            # Sonuçları sakla
            levels = {
                'do': daily_open,
                'wo': weekly_open,
                'mo': monthly_open,
                'eqd': eq_daily,
                'eqw': eq_weekly,
                'eqm': eq_monthly,
                'mr': monday_range,
                'mreq': monday_range_eq
            }
            
            self.levels[key] = levels
            
            # Güncel fiyatı al
            current_candles = self.client.fetch_klines(symbol, timeframe, 1)
            if current_candles is not None and len(current_candles) > 0:
                current_price = current_candles['close'].iloc[-1]
            else:
                current_price = None
            
            # Son analiz zamanını güncelle
            self.last_analysis_time[key] = now
            
            # Sonuçları logla
            self._log_levels(symbol, timeframe, levels, current_price)
            
            # Analiz sonucunu sakla
            if symbol not in self.all_levels_data:
                self.all_levels_data[symbol] = {}
            self.all_levels_data[symbol][timeframe] = levels # levels sözlüğünü sakla
            
            return levels
        
        except Exception as e:
            logger.error(f"Zaman dilimi analizi sırasında hata: {symbol} {timeframe} - {str(e)}")
            import traceback
            logger.error(f"Hata detayları: {traceback.format_exc()}")
            return {}
    
    def _log_levels(self, symbol, timeframe, levels, current_price):
        """Hesaplanan seviyeleri ve güncel fiyatı logla"""
        tf_label = "1s" if timeframe == "60" else "4s" if timeframe == "240" else timeframe
        
        logger.info(f"[LEVEL-ANALİZ] {symbol}/{tf_label} Zaman Dilimi Seviyeleri:")
        logger.info(f"[LEVEL-FİYAT] {symbol}: {format_price_standard(current_price)} (Güncel Fiyat)")
        logger.info(f"[LEVEL-DO] {symbol}: {format_price_standard(levels['do'])} (Günlük Açılış)")
        logger.info(f"[LEVEL-WO] {symbol}: {format_price_standard(levels['wo'])} (Haftalık Açılış)")
        logger.info(f"[LEVEL-MO] {symbol}: {format_price_standard(levels['mo'])} (Aylık Açılış)")
        logger.info(f"[LEVEL-EqD] {symbol}: {format_price_standard(levels['eqd'])} (Günlük Denge)")
        logger.info(f"[LEVEL-EqW] {symbol}: {format_price_standard(levels['eqw'])} (Haftalık Denge)")
        logger.info(f"[LEVEL-EqM] {symbol}: {format_price_standard(levels['eqm'])} (Aylık Denge)")
        
        if levels['mr'][0] is not None and levels['mr'][1] is not None:
            logger.info(f"[LEVEL-MR] {symbol}: {format_price_standard(levels['mr'][0])}-{format_price_standard(levels['mr'][1])} (Pazartesi Aralığı)")
        else:
            logger.info(f"[LEVEL-MR] {symbol}: Veri yok (Pazartesi Aralığı)")
        
        if levels['mreq'] is not None:
            logger.info(f"[LEVEL-MREq] {symbol}: {format_price_standard(levels['mreq'])} (Pazartesi Aralığı Dengesi)")
            # Sözlük içinde mreq anahtarının varlığını kontrol et ve yazdır
            logger.info(f" {symbol} için 'mreq' anahtarı levels sözlüğünde mevcut: {format_price_standard(levels['mreq'])}")
            logger.info(f" {symbol} için levels sözlüğündeki tüm anahtarlar: {list(levels.keys())}")
        else:
            logger.info(f"[LEVEL-MREq] {symbol}: Veri yok (Pazartesi Aralığı Dengesi)")
            logger.info(f" {symbol} için 'mreq' anahtarı levels sözlüğünde bulunamadı")
            logger.info(f" {symbol} için levels sözlüğündeki tüm anahtarlar: {list(levels.keys())}")
        
        # Fiyatın seviyelere göre durumunu hesapla
        if current_price is not None:
            logger.info(f"[LEVEL-DURUM] {symbol} Fiyat Durumu:")
            
            def get_percent_diff(level):
                return ((current_price - level) / level) * 100.0
            
            do_diff = get_percent_diff(levels['do'])
            wo_diff = get_percent_diff(levels['wo'])
            mo_diff = get_percent_diff(levels['mo'])
            
            logger.info(f"[LEVEL-DURUM-DO] DO'ya göre: %{do_diff:.2f} ({'Üstünde' if do_diff > 0 else 'Altında'})")
            logger.info(f"[LEVEL-DURUM-WO] WO'ya göre: %{wo_diff:.2f} ({'Üstünde' if wo_diff > 0 else 'Altında'})")
            logger.info(f"[LEVEL-DURUM-MO] MO'ya göre: %{mo_diff:.2f} ({'Üstünde' if mo_diff > 0 else 'Altında'})")
        
        logger.info("-" * 50)
    
    def analyze_all_symbols(self):
        """Tüm semboller ve zaman dilimleri için zaman dilimi bazlı seviyeleri analiz et"""
        # Çevre değişkenlerinden sembolleri al
        symbols = os.environ.get("SYMBOLS", "BTCUSDT,ETHUSDT,SOLUSDT").split(",")
        # timeframes = ["60", "240"]  # Eski: Hem 1s hem de 4s
        timeframes = ["240"]  # Yeni: Sadece 4s
        
        logger.info("[LEVEL-SİSTEM-BAŞLA] Zaman dilimi seviye analizi başlatılıyor...")
        logger.info("=" * 50)
        
        all_levels = {}
        
        for symbol in symbols:
            for timeframe in timeframes:
                levels = self.analyze_timeframe_levels(symbol, timeframe)
                all_levels[f"{symbol}_{timeframe}"] = levels
        
        logger.info("[LEVEL-SİSTEM-BİTİŞ] Zaman dilimi seviye analizi tamamlandı")
        logger.info("=" * 50)
        
        # Analiz bittikten sonra self.all_levels_data dolu olacak
        self.all_levels_data = all_levels
        
        return all_levels
    
    def get_levels(self, symbol, timeframe):
        """
        Belirli bir sembol ve zaman dilimi için hesaplanmış seviyeleri döndür
        
        Args:
            symbol (str): Sembol (örn. "BTCUSDT")
            timeframe (str): Zaman dilimi (örn. "60", "240")
            
        Returns:
            dict: Zaman dilimi bazlı seviyeler
        """
        key = f"{symbol}_{timeframe}"
        return self.levels.get(key, {})

    def get_all_levels(self) -> Dict:
        """
        Analiz edilen tüm semboller ve zaman dilimleri için seviye verilerini döndürür.
        """
        return self.all_levels_data

# Doğrudan çalıştırma testi
if __name__ == "__main__":
    analyzer = TimeframeLevelsAnalyzer()
    analyzer.analyze_all_symbols()