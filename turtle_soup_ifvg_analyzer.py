# turtle_soup_ifvg_analyzer.py

from typing import Dict, Any, List, Optional
import pandas as pd
from loguru import logger
from datetime import datetime, timedelta

class TurtleSoupIFVGAnalyzer:
    """
    Turtle Soup + IFVG (Inverse Fair Value Gap) kombinasyon stratejisi.
    
    Turtle Soup Konsepti:
    - Fiyat öne<PERSON>li bir seviyeyi (support/resistance) kırar
    - Kırılım false breakout olur (turtle soup)
    - <PERSON>yat hızla geri döner ve ters yönde hareket eder
    
    IFVG Konsepti:
    - Fair Value Gap doldurulduktan sonra tersine hareket
    - Güçlü reversal sinyali verir
    
    Kombinasyon:
    - Turtle Soup + IFVG = Çok güçlü reversal sinyali
    - Yüksek R:R oranı
    - Düşük false positive oranı
    """
    
    def __init__(self, config: Optional[Dict[str, Any]] = None):
        """
        Turtle Soup + IFVG Analyzer'ı başlatır.
        
        Args:
            config: Konfigürasyon parametreleri
        """
        self.config = config or {}
        
        # Turtle Soup parametreleri
        self.min_breakout_distance_pct = self.config.get('min_breakout_distance_pct', 0.2)  # %0.2 minimum kırılım
        self.max_breakout_distance_pct = self.config.get('max_breakout_distance_pct', 1.0)  # %1.0 maksimum kırılım
        self.soup_confirmation_candles = self.config.get('soup_confirmation_candles', 3)  # 3 mum teyit
        self.min_level_age_hours = self.config.get('min_level_age_hours', 4)  # Minimum 4 saat eski seviye
        
        # IFVG parametreleri
        self.min_ifvg_strength = self.config.get('min_ifvg_strength', 6.0)  # Minimum IFVG gücü
        self.ifvg_proximity_tolerance_pct = self.config.get('ifvg_proximity_tolerance_pct', 0.5)  # %0.5 yakınlık
        
        # Confluence parametreleri
        self.min_confluence_score = self.config.get('min_confluence_score', 70.0)  # Minimum confluence skoru
        self.max_time_between_events_hours = self.config.get('max_time_between_events_hours', 12)  # Max 12 saat arayla
        
        logger.info("Turtle Soup + IFVG Analyzer başlatıldı")
    
    def analyze(self, symbol: str, candles: pd.DataFrame, 
               structure_analysis: Dict[str, Any],
               ifvg_analysis: Dict[str, Any],
               liquidity_analysis: Dict[str, Any]) -> Dict[str, Any]:
        """
        Turtle Soup + IFVG kombinasyon analizini yapar.
        
        Args:
            symbol: Sembol adı
            candles: Mum verileri
            structure_analysis: Market structure analizi
            ifvg_analysis: IFVG analizi
            liquidity_analysis: Likidite analizi
            
        Returns:
            Dict: Turtle Soup + IFVG analiz sonuçları
        """
        try:
            logger.info(f"[{symbol}] Turtle Soup + IFVG analizi başlatılıyor...")
            
            if candles is None or candles.empty:
                return {'signals': [], 'error': 'no_candle_data'}
            
            current_price = float(candles.iloc[-1]['close'])
            
            # 1. Turtle Soup pattern'lerini tespit et
            turtle_soup_patterns = self._detect_turtle_soup_patterns(
                candles, structure_analysis, liquidity_analysis
            )
            
            # 2. IFVG'leri al
            ifvgs = ifvg_analysis.get('inverse_fvgs', [])
            active_ifvgs = [ifvg for ifvg in ifvgs if not ifvg.get('filled', False)]
            
            # 3. Turtle Soup + IFVG confluence'larını analiz et
            confluence_signals = self._analyze_turtle_soup_ifvg_confluence(
                turtle_soup_patterns, active_ifvgs, current_price, candles
            )
            
            # 4. Sinyalleri skorla ve filtrele
            scored_signals = self._score_and_filter_signals(confluence_signals, current_price)
            
            logger.success(f"[{symbol}] Turtle Soup + IFVG analizi tamamlandı: {len(scored_signals)} sinyal")
            
            return {
                'signals': scored_signals,
                'turtle_soup_patterns': turtle_soup_patterns,
                'active_ifvgs': active_ifvgs,
                'confluence_count': len(confluence_signals),
                'analysis_timestamp': datetime.now(),
                'total_score': max([s.get('confluence_score', 0) for s in scored_signals], default=0)
            }
            
        except Exception as e:
            logger.error(f"[{symbol}] Turtle Soup + IFVG analizi hatası: {e}", exc_info=True)
            return {'signals': [], 'error': str(e)}
    
    def _detect_turtle_soup_patterns(self, candles: pd.DataFrame, 
                                   structure_analysis: Dict[str, Any],
                                   liquidity_analysis: Dict[str, Any]) -> List[Dict[str, Any]]:
        """
        Turtle Soup pattern'lerini tespit eder.
        """
        turtle_soup_patterns = []
        
        try:
            # Pivot noktalarını al
            major_pivots = structure_analysis.get('major_pivots', [])
            if len(major_pivots) < 2:
                return turtle_soup_patterns
            
            # Likidite seviyelerini al
            bsl_levels = liquidity_analysis.get('buy_side_liquidity', [])
            ssl_levels = liquidity_analysis.get('sell_side_liquidity', [])
            
            # Son 20 mumda turtle soup ara
            recent_candles = candles.tail(20)
            
            for i in range(5, len(recent_candles)):
                current_candle = recent_candles.iloc[i]
                current_price = float(current_candle['close'])
                current_time = current_candle['timestamp']
                
                # Bullish Turtle Soup (SSL kırılımı sonrası geri dönüş)
                bullish_soup = self._check_bullish_turtle_soup(
                    recent_candles, i, ssl_levels, current_price, current_time
                )
                if bullish_soup:
                    turtle_soup_patterns.append(bullish_soup)
                
                # Bearish Turtle Soup (BSL kırılımı sonrası geri dönüş)
                bearish_soup = self._check_bearish_turtle_soup(
                    recent_candles, i, bsl_levels, current_price, current_time
                )
                if bearish_soup:
                    turtle_soup_patterns.append(bearish_soup)
            
            # En yakın ve en güçlü pattern'leri filtrele
            turtle_soup_patterns.sort(key=lambda x: (x['strength'], -x['candles_ago']), reverse=True)
            
            return turtle_soup_patterns[:3]  # En iyi 3 pattern
            
        except Exception as e:
            logger.error(f"Turtle Soup pattern tespiti hatası: {e}", exc_info=True)
            return []
    
    def _check_bullish_turtle_soup(self, candles: pd.DataFrame, index: int,
                                  ssl_levels: List[Dict[str, Any]], 
                                  current_price: float, current_time) -> Optional[Dict[str, Any]]:
        """
        Bullish Turtle Soup pattern'ini kontrol eder.
        """
        try:
            if index < 3:
                return None
            
            # Son 3 mumda SSL kırılımı var mı?
            for j in range(max(0, index-3), index):
                candle = candles.iloc[j]
                candle_low = float(candle['low'])
                
                # En yakın SSL seviyesini bul
                closest_ssl = None
                min_distance = float('inf')
                
                for ssl in ssl_levels:
                    ssl_price = ssl.get('price', 0)
                    distance = abs(candle_low - ssl_price)
                    if distance < min_distance:
                        min_distance = distance
                        closest_ssl = ssl
                
                if not closest_ssl:
                    continue
                
                ssl_price = closest_ssl.get('price', 0)
                
                # SSL kırılımı kontrolü
                breakout_distance = abs(candle_low - ssl_price) / ssl_price * 100
                
                if (candle_low < ssl_price and 
                    self.min_breakout_distance_pct <= breakout_distance <= self.max_breakout_distance_pct):
                    
                    # Geri dönüş kontrolü (sonraki mumlar SSL üstüne çıktı mı?)
                    reversal_confirmed = False
                    for k in range(j+1, min(j+self.soup_confirmation_candles+1, len(candles))):
                        if k >= len(candles):
                            break
                        next_candle = candles.iloc[k]
                        if float(next_candle['close']) > ssl_price:
                            reversal_confirmed = True
                            break
                    
                    if reversal_confirmed:
                        return {
                            'type': 'BULLISH_TURTLE_SOUP',
                            'direction': 'bullish',
                            'breakout_candle_index': j,
                            'breakout_price': candle_low,
                            'ssl_level': ssl_price,
                            'breakout_distance_pct': breakout_distance,
                            'reversal_confirmed': True,
                            'strength': self._calculate_turtle_soup_strength(candles, j, ssl_price, 'bullish'),
                            'candles_ago': len(candles) - j - 1,
                            'timestamp': current_time,
                            'liquidity_data': closest_ssl
                        }
            
            return None
            
        except Exception as e:
            logger.error(f"Bullish turtle soup kontrolü hatası: {e}", exc_info=True)
            return None
    
    def _check_bearish_turtle_soup(self, candles: pd.DataFrame, index: int,
                                  bsl_levels: List[Dict[str, Any]], 
                                  current_price: float, current_time) -> Optional[Dict[str, Any]]:
        """
        Bearish Turtle Soup pattern'ini kontrol eder.
        """
        try:
            if index < 3:
                return None
            
            # Son 3 mumda BSL kırılımı var mı?
            for j in range(max(0, index-3), index):
                candle = candles.iloc[j]
                candle_high = float(candle['high'])
                
                # En yakın BSL seviyesini bul
                closest_bsl = None
                min_distance = float('inf')
                
                for bsl in bsl_levels:
                    bsl_price = bsl.get('price', 0)
                    distance = abs(candle_high - bsl_price)
                    if distance < min_distance:
                        min_distance = distance
                        closest_bsl = bsl
                
                if not closest_bsl:
                    continue
                
                bsl_price = closest_bsl.get('price', 0)
                
                # BSL kırılımı kontrolü
                breakout_distance = abs(candle_high - bsl_price) / bsl_price * 100
                
                if (candle_high > bsl_price and 
                    self.min_breakout_distance_pct <= breakout_distance <= self.max_breakout_distance_pct):
                    
                    # Geri dönüş kontrolü (sonraki mumlar BSL altına indi mi?)
                    reversal_confirmed = False
                    for k in range(j+1, min(j+self.soup_confirmation_candles+1, len(candles))):
                        if k >= len(candles):
                            break
                        next_candle = candles.iloc[k]
                        if float(next_candle['close']) < bsl_price:
                            reversal_confirmed = True
                            break
                    
                    if reversal_confirmed:
                        return {
                            'type': 'BEARISH_TURTLE_SOUP',
                            'direction': 'bearish',
                            'breakout_candle_index': j,
                            'breakout_price': candle_high,
                            'bsl_level': bsl_price,
                            'breakout_distance_pct': breakout_distance,
                            'reversal_confirmed': True,
                            'strength': self._calculate_turtle_soup_strength(candles, j, bsl_price, 'bearish'),
                            'candles_ago': len(candles) - j - 1,
                            'timestamp': current_time,
                            'liquidity_data': closest_bsl
                        }
            
            return None
            
        except Exception as e:
            logger.error(f"Bearish turtle soup kontrolü hatası: {e}", exc_info=True)
            return None
    
    def _calculate_turtle_soup_strength(self, candles: pd.DataFrame, breakout_index: int,
                                      level_price: float, direction: str) -> float:
        """
        Turtle Soup pattern'inin gücünü hesaplar (1-10 arası).
        """
        try:
            strength = 5.0  # Base strength
            
            # Volume factor
            if breakout_index < len(candles):
                breakout_candle = candles.iloc[breakout_index]
                avg_volume = candles['volume'].rolling(10).mean().iloc[breakout_index]
                breakout_volume = float(breakout_candle['volume'])
                
                if avg_volume > 0:
                    volume_ratio = breakout_volume / avg_volume
                    if volume_ratio > 2.0:
                        strength += 2.0
                    elif volume_ratio > 1.5:
                        strength += 1.0
            
            # Reversal speed factor
            reversal_candles = 0
            for i in range(breakout_index + 1, min(breakout_index + 5, len(candles))):
                candle = candles.iloc[i]
                if direction == 'bullish':
                    if float(candle['close']) > level_price:
                        reversal_candles = i - breakout_index
                        break
                else:
                    if float(candle['close']) < level_price:
                        reversal_candles = i - breakout_index
                        break
            
            if reversal_candles == 1:
                strength += 2.0  # Çok hızlı geri dönüş
            elif reversal_candles == 2:
                strength += 1.5
            elif reversal_candles == 3:
                strength += 1.0
            
            return min(10.0, max(1.0, strength))
            
        except Exception as e:
            logger.error(f"Turtle soup strength hesaplama hatası: {e}", exc_info=True)
            return 5.0
    
    def _analyze_turtle_soup_ifvg_confluence(self, turtle_soup_patterns: List[Dict[str, Any]],
                                           active_ifvgs: List[Dict[str, Any]],
                                           current_price: float,
                                           candles: pd.DataFrame) -> List[Dict[str, Any]]:
        """
        Turtle Soup + IFVG confluence'larını analiz eder.
        """
        confluence_signals = []
        
        try:
            for soup in turtle_soup_patterns:
                for ifvg in active_ifvgs:
                    # Direction uyumluluğu kontrol et
                    if soup['direction'] == ifvg.get('direction', ''):
                        confluence = self._check_soup_ifvg_confluence(soup, ifvg, current_price, candles)
                        if confluence:
                            confluence_signals.append(confluence)
            
            return confluence_signals
            
        except Exception as e:
            logger.error(f"Turtle Soup + IFVG confluence analizi hatası: {e}", exc_info=True)
            return []
    
    def _check_soup_ifvg_confluence(self, soup: Dict[str, Any], ifvg: Dict[str, Any],
                                   current_price: float, candles: pd.DataFrame) -> Optional[Dict[str, Any]]:
        """
        Tek bir Turtle Soup + IFVG confluence'ını kontrol eder.
        """
        try:
            # IFVG strength kontrolü
            ifvg_strength = ifvg.get('inversion_strength', 0)
            if ifvg_strength < self.min_ifvg_strength:
                return None
            
            # Proximity kontrolü
            soup_level = soup.get('ssl_level') or soup.get('bsl_level', 0)
            ifvg_level = ifvg.get('inversion_point', 0)
            
            if soup_level == 0 or ifvg_level == 0:
                return None
            
            proximity_pct = abs(soup_level - ifvg_level) / current_price * 100
            if proximity_pct > self.ifvg_proximity_tolerance_pct:
                return None
            
            # Time proximity kontrolü
            soup_time = soup.get('timestamp')
            ifvg_time = ifvg.get('timestamp')
            
            if soup_time and ifvg_time:
                time_diff_hours = abs((soup_time - ifvg_time).total_seconds()) / 3600
                if time_diff_hours > self.max_time_between_events_hours:
                    return None
            
            # Confluence score hesapla
            confluence_score = self._calculate_confluence_score(soup, ifvg, proximity_pct)
            
            if confluence_score >= self.min_confluence_score:
                return {
                    'type': 'TURTLE_SOUP_IFVG',
                    'pattern': 'TURTLE_SOUP_IFVG',
                    'direction': soup['direction'].upper(),
                    'turtle_soup_data': soup,
                    'ifvg_data': ifvg,
                    'confluence_score': confluence_score,
                    'proximity_pct': proximity_pct,
                    'entry_method': 'turtle_soup_ifvg',
                    'current_price': current_price,
                    'soup_level': soup_level,
                    'ifvg_level': ifvg_level
                }
            
            return None
            
        except Exception as e:
            logger.error(f"Soup-IFVG confluence kontrolü hatası: {e}", exc_info=True)
            return None
    
    def _calculate_confluence_score(self, soup: Dict[str, Any], ifvg: Dict[str, Any],
                                   proximity_pct: float) -> float:
        """
        Turtle Soup + IFVG confluence skorunu hesaplar.
        """
        try:
            score = 0.0
            
            # Turtle Soup strength (40%)
            soup_strength = soup.get('strength', 5)
            score += (soup_strength / 10) * 40
            
            # IFVG strength (35%)
            ifvg_strength = ifvg.get('inversion_strength', 5)
            score += (ifvg_strength / 10) * 35
            
            # Proximity score (15%)
            proximity_score = max(0, 100 - (proximity_pct * 200))  # %0.5 = 100 puan
            score += (proximity_score / 100) * 15
            
            # Timing score (10%)
            soup_recency = soup.get('candles_ago', 10)
            timing_score = max(0, 100 - (soup_recency * 10))
            score += (timing_score / 100) * 10
            
            return min(100.0, max(0.0, score))
            
        except Exception as e:
            logger.error(f"Confluence score hesaplama hatası: {e}", exc_info=True)
            return 0.0
    
    def _score_and_filter_signals(self, confluence_signals: List[Dict[str, Any]],
                                 current_price: float) -> List[Dict[str, Any]]:
        """
        Confluence sinyallerini skorla ve filtrele.
        """
        try:
            # Minimum confluence score filtresi
            valid_signals = [
                signal for signal in confluence_signals 
                if signal.get('confluence_score', 0) >= self.min_confluence_score
            ]
            
            # Confluence score'a göre sırala
            valid_signals.sort(key=lambda x: x.get('confluence_score', 0), reverse=True)
            
            # En iyi 2 sinyali döndür
            return valid_signals[:2]
            
        except Exception as e:
            logger.error(f"Signal scoring ve filtreleme hatası: {e}", exc_info=True)
            return []