#!/usr/bin/env python3
"""
Performance Metrics Güncelleme Scripti
Bu script performance_metrics.json dosyasını yeni alan<PERSON> günceller.
"""

import json
import os
from datetime import datetime

def update_performance_metrics():
    """Performance metrics JSON dosyasını yeni alanlarla günceller."""
    
    stats_dir = "stats"
    metrics_file = os.path.join(stats_dir, "performance_metrics.json")
    
    # Güncellenmiş metrics yapısı
    updated_metrics = {
        "total_signals": 0,
        "completed_trades": 0,
        "successful_trades": 0,
        "failed_trades": 0,
        "success_rate": 0.0,
        "avg_profit_percentage": 0.0,
        "total_profit_percentage": 0.0,
        "tp1_hits": 0,
        "tp1_5_hits": 0,
        "tp2_hits": 0,
        "tp3_hits": 0,
        "sl_hits": 0,
        "pivot_success_hits": 0,
        "cancelled_trades": 0,
        "tp1_rate": 0.0,
        "tp1_5_rate": 0.0,
        "tp2_rate": 0.0,
        "tp3_rate": 0.0,
        "sl_rate": 0.0,
        "pattern_stats": {},
        "sl_type_stats": {},
        "strategy_stats": {},
        "best_trade": None,
        "worst_trade": None,
        "best_symbol": None,
        "best_confirmation": None,
        "best_pattern": None,
        "best_sl_type": None,
        "last_updated": datetime.now().isoformat(),
        "active_signals_count": 0
    }
    
    # Mevcut dosya varsa eski değerleri koru
    if os.path.exists(metrics_file):
        try:
            with open(metrics_file, 'r', encoding='utf-8') as f:
                existing_metrics = json.load(f)
            
            # Mevcut değerleri koru, sadece eksik alanları ekle
            for key, value in existing_metrics.items():
                if key in updated_metrics:
                    updated_metrics[key] = value
                    
            print(f"✅ Mevcut metrikler korundu ve yeni alanlar eklendi.")
        except Exception as e:
            print(f"⚠️ Mevcut dosya okuma hatası: {e}, yeni dosya oluşturuluyor.")
    
    # Stats klasörünü oluştur
    os.makedirs(stats_dir, exist_ok=True)
    
    # Güncellenmiş metrikleri kaydet
    try:
        with open(metrics_file, 'w', encoding='utf-8') as f:
            json.dump(updated_metrics, f, indent=2, ensure_ascii=False)
        print(f"🎯 Performance metrics başarıyla güncellendi: {metrics_file}")
        print(f"📊 Toplam alan sayısı: {len(updated_metrics)}")
        
        # Yeni eklenen alanları listele
        new_fields = [
            "tp1_5_hits", "tp1_5_rate", "pivot_success_hits", "cancelled_trades", 
            "best_confirmation", "best_pattern", "best_sl_type"
        ]
        print(f"🆕 Yeni eklenen alanlar: {', '.join(new_fields)}")
        
    except Exception as e:
        print(f"💥 Dosya yazma hatası: {e}")

if __name__ == "__main__":
    update_performance_metrics()
