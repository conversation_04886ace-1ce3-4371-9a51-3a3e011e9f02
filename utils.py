from typing import Optional, List
from loguru import logger

def format_price_standard(price: Optional[float]) -> str:
    """
    GELİŞTİRİLDİ: Farklı büyüklükteki kripto para fiyatlarını, önce yuvarlayarak
    sonra standart formatta biçimlendirir. Bu, kayan nokta aritmetiğinden
    kaynaklanan istenmeyen ondalık basamakları temizler.
    
    Args:
        price: Biçimlendirilecek fiyat değeri
        
    Returns:
        str: Biçimlendirilmiş fiyat değeri
    """
    if price is None:
        return "N/A"
    
    try:
        # Fiyatın büyüklüğüne göre hassasiyeti belirle ve YUVARLA
        if price >= 1000:
            precision = 2
        elif price >= 1:
            precision = 4
        elif price >= 0.01:
            precision = 5
        elif price >= 0.0001:
            precision = 6
        else:
            precision = 8
            
        price = round(price, precision)

        # Yuvarlanmış fiyata göre formatı belirle
        if price >= 1000:
            return f"{price:.2f}"  # Örn: 45000.00
        elif price >= 100:
            return f"{price:.2f}"  # Örn: 500.00
        elif price >= 10:
            return f"{price:.2f}"  # Örn: 50.00
        elif price >= 1:
            return f"{price:.3f}"  # Örn: 5.000
        elif price >= 0.1:
            return f"{price:.4f}"  # Örn: 0.5000
        elif price >= 0.01:
            return f"{price:.5f}"  # Örn: 0.05000
        elif price >= 0.001:
            return f"{price:.6f}"  # Örn: 0.005000
        else:
            return f"{price:.8f}"  # Örn: 0.00050000
            
    except (TypeError, ValueError) as e:
        logger.warning(f"Fiyat formatlanırken hata oluştu: {price} - Hata: {e}")
        return str(price) # Hata durumunda orijinal değeri string olarak döndür 

def format_volume(volume: float) -> str:
    """
    Büyük sayısal hacim veya ciro değerlerini okunaklı bir formata ('B' Milyar, 'M' Milyon) çevirir.

    Args:
        volume (float): Formatlanacak sayısal değer.

    Returns:
        str: Formatlanmış string (örn. "1.23B", "45.67M", "890.12").
    """
    if volume >= 1_000_000_000:
        return f"{volume/1_000_000_000:.2f}B"
    elif volume >= 1_000_000:
        return f"{volume/1_000_000:.2f}M"
    else:
        return f"{volume:.2f}" # Milyon altına düşenler için direkt format

def shorten_indicator_names(indicators: List[str]) -> str:
    """
    Verilen gösterge adları listesini kısaltarak tek bir string haline getirir.
    Önceden tanımlanmış kısaltmalar kullanılır, yoksa baş harf alınır.
    (örn. ["MACD", "RSI"] -> "MR")
    
    Args:
        indicators (List[str]): Gösterge adlarının listesi.
        
    Returns:
        str: Kısaltılmış ve birleştirilmiş gösterge adları string'i.
    """
    indicator_map = {
        "MACD": "M",
        "MACD Histogram": "H", # Histogram için ayrı
        "RSI": "R",
        "Stochastic": "S",
        "CCI": "C",
        "Momentum": "M", # MACD ile aynı harf, dikkat
        "OBV": "O",
        "vwMACD": "V",
        "CMF": "C", # CCI ile aynı harf, dikkat
        "MFI": "F"
    }
    short_names = []
    for ind in indicators:
        # Eşleşme varsa haritasını, yoksa ilk harfini al (büyük harf)
        short_names.append(indicator_map.get(ind, ind[0].upper() if ind else ''))
    return "".join(short_names) 

from typing import Dict, Any
from datetime import datetime
import pandas as pd
import numpy as np

def calculate_spatial_overlap_score(zone1: Dict[str, Any], zone2: Dict[str, Any]) -> float:
    """İki fiyat bölgesinin mekansal çakışma skorunu hesaplar (0-100)."""
    try:
        zone1_top = float(zone1.get('top', 0))
        zone1_bottom = float(zone1.get('bottom', 0))
        zone2_top = float(zone2.get('top', 0))
        zone2_bottom = float(zone2.get('bottom', 0))

        if zone1_top <= zone1_bottom or zone2_top <= zone2_bottom:
            return 0.0

        overlap_top = min(zone1_top, zone2_top)
        overlap_bottom = max(zone1_bottom, zone2_bottom)

        overlap_height = max(0, overlap_top - overlap_bottom)
        if overlap_height == 0:
            return 0.0

        zone1_height = zone1_top - zone1_bottom
        zone2_height = zone2_top - zone2_bottom

        # Çakışmanın her iki bölgeye oranının ortalaması
        overlap_ratio_1 = (overlap_height / zone1_height) * 100
        overlap_ratio_2 = (overlap_height / zone2_height) * 100

        return (overlap_ratio_1 + overlap_ratio_2) / 2

    except (ValueError, TypeError, ZeroDivisionError):
        return 0.0

def calculate_price_overlap_vectorized(fvg_df: pd.DataFrame, ob_df: pd.DataFrame) -> pd.Series:
    """
    İki DataFrame (FVG'ler ve OB'ler) arasındaki fiyat çakışmasını vektörel olarak hesaplar.

    Args:
        fvg_df: FVG verilerini içeren DataFrame. 'top' ve 'bottom' sütunları olmalı.
        ob_df: Order Block verilerini içeren DataFrame. 'top' ve 'bottom' sütunları olmalı.

    Returns:
        Her bir FVG-OB çifti için çakışma yüzdesini içeren bir Pandas Serisi.
    """
    # DataFrame'ler arasında kartezyen çarpım yapmak için bir 'key' sütunu ekle
    fvg_df['key'] = 1
    ob_df['key'] = 1

    # Birleştirme işlemi
    combined_df = pd.merge(fvg_df, ob_df, on='key', suffixes=('_fvg', '_ob')).drop('key', axis=1)

    # Vektörel hesaplama
    overlap_height = np.maximum(0, 
        np.minimum(combined_df['top_fvg'], combined_df['top_ob']) - 
        np.maximum(combined_df['bottom_fvg'], combined_df['bottom_ob'])
    )

    fvg_height = combined_df['top_fvg'] - combined_df['bottom_fvg']
    
    # Sıfıra bölme hatasını önle
    overlap_percentage = np.where(
        fvg_height > 0, 
        (overlap_height / fvg_height) * 100, 
        0
    )

    return pd.Series(overlap_percentage)

def calculate_temporal_proximity(fvg_time: datetime, ob_time: datetime, max_hours: int = 48) -> float:
    """FVG ve OB arasındaki zamansal yakınlığı 0-1 arasında bir skor olarak hesaplar."""
    if not isinstance(fvg_time, datetime) or not isinstance(ob_time, datetime):
        logger.warning("calculate_temporal_proximity: Geçersiz zaman damgası türü.")
        return 0.0
    
    time_diff = abs((fvg_time - ob_time).total_seconds() / 3600)
    if time_diff > max_hours:
        return 0.0
        
    # Zaman farkı azaldıkça skor 1'e yaklaşır
    score = 1 - (time_diff / max_hours)
    return max(0.0, score)

def calculate_temporal_proximity_score(item1: Dict[str, Any], item2: Dict[str, Any], max_hours: int = 72) -> float:
    """İki öğenin zamansal yakınlık skorunu hesaplar (0-100)."""
    try:
        time1 = item1.get('timestamp') or item1.get('start_time')
        time2 = item2.get('timestamp') or item2.get('start_time')

        if not isinstance(time1, datetime) or not isinstance(time2, datetime):
            return 0.0

        time_diff_hours = abs((time1 - time2).total_seconds() / 3600)

        if time_diff_hours > max_hours:
            return 0.0

        # Yakınlık arttıkça skor artar (lineer azalan)
        score = 100 * (1 - (time_diff_hours / max_hours))
        return max(0, score)

    except (TypeError, ValueError):
        return 0.0

def is_ote_enhanced(fvg_ob_confluence_zone: Dict[str, float], ote_zone: Dict[str, float]) -> bool:
    """Bir FVG-OB çakışma bölgesinin bir OTE bölgesi ile çakışıp çakışmadığını kontrol eder."""
    if not ote_zone or not fvg_ob_confluence_zone:
        return False

    # İki bölgenin çakışıp çakışmadığını kontrol et
    overlap = max(fvg_ob_confluence_zone['low'], ote_zone['low']) <= min(fvg_ob_confluence_zone['high'], ote_zone['high'])
    
    if overlap:
        logger.debug(f"OTE Zenginleştirmesi Tespit Edildi. FVG-OB Bölgesi: {fvg_ob_confluence_zone}, OTE Bölgesi: {ote_zone}")

    return overlap