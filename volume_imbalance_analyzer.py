# volume_imbalance_analyzer.py
# LuxAlgo ICT - Volume Imbalance Analyzer
# FVG'ye benzer ancak hacim odaklı verimsizlik analizi

import pandas as pd
import numpy as np
from loguru import logger
from typing import List, Dict, Any, Optional, Tuple
from datetime import datetime


class VolumeImbalanceAnalyzer:
    """
    Hacim Dengesizliği (Volume Imbalance) analizi yapar.
    
    Volume Imbalance (VI), FVG'ye benzer şekilde fiyat verimsizliği olarak çalışır
    ancak hacim perspektifinden değerlendirilir. İki bitişik mum arasında:
    - <PERSON><PERSON><PERSON> kapanış fiyatı ile diğerinin açılış fiyatı arasında boşluk olduğunda
    - Bu boşluk genellikle düşük hacimli veya agresif hareket sonucu oluşur
    - Fiyat bu bölgeyi "doldurmaya" eğilimindedir (rebalance)
    
    ICT Konseptleri:
    - Bullish VI: Yukarı yönlü gap (open[i] > close[i-1])
    - Bearish VI: Aşa<PERSON>ı yönlü gap (open[i] < close[i-1])
    - VI Mitigation: Boşluğun doldurulması
    - VI Confluence: FVG veya OB ile çakışma
    """
    
    def __init__(self, 
                 min_gap_size_pct: float = 0.05,
                 max_gap_size_pct: float = 2.0,
                 volume_threshold_multiplier: float = 1.5,
                 mitigation_buffer_pct: float = 0.01):
        """
        Volume Imbalance Analyzer'ı başlatır.
        
        Args:
            min_gap_size_pct: Minimum gap boyutu (% olarak)
            max_gap_size_pct: Maximum gap boyutu (% olarak, noise filtreleme)
            volume_threshold_multiplier: Volume threshold çarpanı
            mitigation_buffer_pct: Mitigation teyidi için buffer (% olarak)
        """
        self.min_gap_size_pct = min_gap_size_pct
        self.max_gap_size_pct = max_gap_size_pct
        self.volume_threshold_multiplier = volume_threshold_multiplier
        self.mitigation_buffer_pct = mitigation_buffer_pct
        
        logger.info(f"⚖️ Volume Imbalance Analyzer başlatıldı - "
                   f"Min Gap: {min_gap_size_pct}%, "
                   f"Max Gap: {max_gap_size_pct}%, "
                   f"Volume Threshold: {volume_threshold_multiplier}x")

    def analyze(self, candles: pd.DataFrame, 
                fvg_analysis: List[Dict[str, Any]] = None, 
                cvd_analysis: Dict[str, Any] = None, 
                symbol: str = None, 
                timeframe: str = None) -> Dict[str, Any]:
        """
        Ana volume imbalance analizi.
        
        Args:
            candles: Mum verileri
            fvg_analysis: FVG analiz sonuçları (confluence için)
            
        Returns:
            Volume imbalance analiz sonuçları
        """
        if candles is None or len(candles) < 3:
            logger.warning("Volume Imbalance analizi için yetersiz mum verisi")
            return self._empty_result()
        
        logger.info(f"⚖️ Volume Imbalance analizi başlıyor - {len(candles)} mum")
        
        # 1. Volume imbalance detection (artık OB ve CVD verilerini kullanıyor)
        detected_imbalances = self._detect_volume_imbalances(candles, None, cvd_analysis)
        
        # 2. Imbalance mitigation analysis
        mitigated_imbalances = self._analyze_mitigation(candles, detected_imbalances)
        
        # 3. Active imbalances
        active_imbalances = self._filter_active_imbalances(mitigated_imbalances)
        
        # 4. FVG confluence analysis
        confluence_analysis = []
        if fvg_analysis:
            confluence_analysis = self._analyze_fvg_confluence(active_imbalances, fvg_analysis)
        
        # 5. Trading signals
        trading_signals = self._generate_vi_signals(active_imbalances, confluence_analysis)
        
        # 6. Quality scoring
        scored_imbalances = self._score_imbalances(active_imbalances, confluence_analysis)
        
        result = {
            'detected_imbalances': detected_imbalances,
            'active_imbalances': scored_imbalances,
            'mitigated_imbalances': [vi for vi in mitigated_imbalances if vi.get('mitigated', False)],
            'fvg_confluence': confluence_analysis,
            'trading_signals': trading_signals,
            'summary': {
                'total_detected': len(detected_imbalances),
                'active_count': len(active_imbalances),
                'bullish_vi': len([vi for vi in active_imbalances if vi.get('direction') == 'BULLISH']),
                'bearish_vi': len([vi for vi in active_imbalances if vi.get('direction') == 'BEARISH']),
                'high_quality_vi': len([vi for vi in scored_imbalances if vi.get('quality_score', 0) >= 7]),
                'confluence_count': len(confluence_analysis)
            },
            'analysis_timestamp': datetime.now().isoformat()
        }
        
        logger.success(f"✅ Volume Imbalance analizi tamamlandı: "
                      f"Detected={len(detected_imbalances)}, "
                      f"Active={len(active_imbalances)}, "
                      f"Confluence={len(confluence_analysis)}")
        
        return result

    def _detect_volume_imbalances(self, candles: pd.DataFrame, 
                                  order_blocks: Dict[str, Any], 
                                  cvd_data: Dict[str, Any]) -> List[Dict[str, Any]]:
        """
        Volume imbalance'ları tespit eder.
        """
        imbalances = []
        
        # Volume ortalaması hesapla (son 20 mum)
        avg_volume = candles['volume'].rolling(window=20).mean()
        
        for i in range(1, len(candles)):
            current_candle = candles.iloc[i]
            previous_candle = candles.iloc[i-1]
            
            current_open = float(current_candle['open'])
            current_close = float(current_candle['close'])
            current_high = float(current_candle['high'])
            current_low = float(current_candle['low'])
            current_volume = float(current_candle['volume'])
            
            prev_close = float(previous_candle['close'])
            prev_volume = float(previous_candle['volume'])
            
            # Gap detection
            gap_size = abs(current_open - prev_close)
            gap_size_pct = (gap_size / prev_close) * 100
            
            # Gap size filter
            if gap_size_pct < self.min_gap_size_pct or gap_size_pct > self.max_gap_size_pct:
                continue
            
            # Volume analysis
            volume_avg = avg_volume.iloc[i] if not pd.isna(avg_volume.iloc[i]) else current_volume
            is_high_volume = current_volume > (volume_avg * self.volume_threshold_multiplier)
            
            # Imbalance direction
            if current_open > prev_close:
                # Bullish Imbalance (Gap up)
                direction = 'BULLISH'
                gap_high = current_open
                gap_low = prev_close
                
                # Additional validation: Candle should not fill the gap immediately
                gap_filled_immediately = current_low <= prev_close
                
            else:
                # Bearish Imbalance (Gap down)
                direction = 'BEARISH'
                gap_high = prev_close
                gap_low = current_open
                
                # Additional validation: Candle should not fill the gap immediately
                gap_filled_immediately = current_high >= prev_close
            
            # Skip if gap filled immediately (not a true imbalance)
            if gap_filled_immediately:
                continue
            
            # Order Block ve CVD ile teyit kontrolü
            ob_confluence = self._check_ob_confluence(gap_high, gap_low, direction, order_blocks)
            cvd_divergence = self._check_cvd_divergence(i, direction, cvd_data)

            # Volume Imbalance object
            vi = {
                'type': 'VOLUME_IMBALANCE',
                'direction': direction,
                'candle_index': i,
                'previous_candle_index': i - 1,
                'gap_high': gap_high,
                'gap_low': gap_low,
                'gap_size': gap_size,
                'gap_size_pct': gap_size_pct,
                'midpoint': (gap_high + gap_low) / 2,
                'volume': current_volume,
                'prev_volume': prev_volume,
                'avg_volume': volume_avg,
                'is_high_volume': is_high_volume,
                'volume_ratio': current_volume / volume_avg if volume_avg > 0 else 1.0,
                'timestamp': current_candle.get('timestamp', datetime.now()),
                'mitigated': False,
                'mitigation_candle_index': None,
                'ob_confluence': ob_confluence,
                'cvd_divergence': cvd_divergence
            }
            
            imbalances.append(vi)
            
            logger.debug(f"📊 Volume Imbalance detected: {direction} @ {vi['midpoint']:.6f} "
                        f"(Gap: {gap_size_pct:.2f}%, Volume: {current_volume:.0f})")
        
        return imbalances

    def _check_ob_confluence(self, vi_high: float, vi_low: float, direction: str, order_blocks: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """
        Verilen VI bölgesi ile bir Order Block arasında kesişim olup olmadığını kontrol eder.
        """
        ob_list = order_blocks.get('order_blocks', [])
        for ob in ob_list:
            # Sadece VI yönüyle uyumlu OB'leri dikkate al
            if (direction == 'BULLISH' and ob['direction'] == 'bullish') or \
               (direction == 'BEARISH' and ob['direction'] == 'bearish'):
                
                # Kesişim kontrolü (overlap)
                if max(vi_low, ob['bottom']) < min(vi_high, ob['top']):
                    logger.debug(f"VI-OB Confluence: {direction} VI at [{vi_low:.4f}-{vi_high:.4f}] overlaps with OB at [{ob['bottom']:.4f}-{ob['top']:.4f}]")
                    return ob # Kesişen OB'yi döndür
        return None

    def _check_cvd_divergence(self, index: int, direction: str, cvd_data: Dict[str, Any]) -> bool:
        """
        VI oluşumu sırasında CVD uyumsuzluğu olup olmadığını kontrol eder.
        Örn: Bullish VI oluşurken CVD düşüyorsa, bu bir zayıflık işaretidir.
        """
        divergences = cvd_data.get('divergences', [])
        for div in divergences:
            # VI'nin oluştuğu mumda bir uyumsuzluk var mı?
            if div['candle_index'] == index:
                # Bullish VI ve Bearish CVD uyumsuzluğu (negatif)
                if direction == 'BULLISH' and div['type'] == 'bearish':
                    return True
                # Bearish VI ve Bullish CVD uyumsuzluğu (negatif)
                if direction == 'BEARISH' and div['type'] == 'bullish':
                    return True
        return False

    def _analyze_mitigation(self, candles: pd.DataFrame, 
                           imbalances: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """
        Volume imbalance mitigation'ını analiz eder.
        """
        mitigated_imbalances = []
        
        for vi in imbalances:
            vi_copy = vi.copy()
            candle_index = vi['candle_index']
            gap_high = vi['gap_high']
            gap_low = vi['gap_low']
            direction = vi['direction']
            
            # Bu VI'dan sonraki mumları kontrol et
            subsequent_candles = candles.iloc[candle_index + 1:]
            
            mitigation_found = False
            mitigation_candle_index = None
            mitigation_type = None
            
            for j, (idx, candle) in enumerate(subsequent_candles.iterrows()):
                candle_high = float(candle['high'])
                candle_low = float(candle['low'])
                
                # Mitigation check
                if direction == 'BULLISH':
                    # Bullish VI: Fiyat gap'in altına inmeli (gap_low'u test etmeli)
                    if candle_low <= gap_low * (1 + self.mitigation_buffer_pct):
                        mitigation_found = True
                        mitigation_candle_index = candle_index + 1 + j
                        mitigation_type = 'FULL_MITIGATION'
                        break
                    # Partial mitigation: Gap'in ortasına kadar gelme
                    elif candle_low <= vi['midpoint']:
                        mitigation_type = 'PARTIAL_MITIGATION'
                
                else:  # BEARISH
                    # Bearish VI: Fiyat gap'in üstüne çıkmalı (gap_high'ı test etmeli)
                    if candle_high >= gap_high * (1 - self.mitigation_buffer_pct):
                        mitigation_found = True
                        mitigation_candle_index = candle_index + 1 + j
                        mitigation_type = 'FULL_MITIGATION'
                        break
                    # Partial mitigation: Gap'in ortasına kadar gelme
                    elif candle_high >= vi['midpoint']:
                        mitigation_type = 'PARTIAL_MITIGATION'
            
            # Update VI with mitigation info
            vi_copy['mitigated'] = mitigation_found
            vi_copy['mitigation_candle_index'] = mitigation_candle_index
            vi_copy['mitigation_type'] = mitigation_type
            
            if mitigation_found:
                logger.debug(f"✅ VI Mitigated: {direction} @ {vi['midpoint']:.6f} "
                           f"at candle {mitigation_candle_index}")
            
            mitigated_imbalances.append(vi_copy)
        
        return mitigated_imbalances

    def _filter_active_imbalances(self, imbalances: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """
        Aktif (henüz mitigate edilmemiş) imbalance'ları filtreler.
        """
        active_imbalances = [vi for vi in imbalances if not vi.get('mitigated', False)]
        
        # En son 20 aktif VI'yi al (performans için)
        if len(active_imbalances) > 20:
            active_imbalances = active_imbalances[-20:]
        
        logger.info(f"📊 Aktif Volume Imbalances: {len(active_imbalances)}")
        
        return active_imbalances

    def _analyze_fvg_confluence(self, imbalances: List[Dict[str, Any]], 
                               fvg_analysis: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """
        Volume Imbalance ile FVG confluence analizi.
        """
        confluence_zones = []
        
        for vi in imbalances:
            vi_high = vi['gap_high']
            vi_low = vi['gap_low']
            vi_midpoint = vi['midpoint']
            vi_direction = vi['direction']
            
            for fvg in fvg_analysis:
                fvg_high = float(fvg.get('high', 0))
                fvg_low = float(fvg.get('low', 0))
                fvg_direction = fvg.get('direction', '').upper()
                
                # Overlap kontrolü
                overlap_high = min(vi_high, fvg_high)
                overlap_low = max(vi_low, fvg_low)
                
                if overlap_high > overlap_low:  # Overlap var
                    overlap_size = overlap_high - overlap_low
                    vi_size = vi_high - vi_low
                    fvg_size = fvg_high - fvg_low
                    
                    # Overlap percentage
                    vi_overlap_pct = (overlap_size / vi_size) * 100 if vi_size > 0 else 0
                    fvg_overlap_pct = (overlap_size / fvg_size) * 100 if fvg_size > 0 else 0
                    
                    # Confluence quality
                    if vi_overlap_pct >= 50 or fvg_overlap_pct >= 50:
                        # Direction confluence
                        direction_match = vi_direction == fvg_direction
                        
                        confluence = {
                            'type': 'VI_FVG_CONFLUENCE',
                            'vi_data': vi,
                            'fvg_data': fvg,
                            'overlap_zone': {
                                'high': overlap_high,
                                'low': overlap_low,
                                'midpoint': (overlap_high + overlap_low) / 2
                            },
                            'overlap_size': overlap_size,
                            'vi_overlap_pct': vi_overlap_pct,
                            'fvg_overlap_pct': fvg_overlap_pct,
                            'direction_match': direction_match,
                            'confluence_strength': 'HIGH' if direction_match and min(vi_overlap_pct, fvg_overlap_pct) >= 70 else 'MEDIUM',
                            'timestamp': datetime.now().isoformat()
                        }
                        
                        confluence_zones.append(confluence)
                        
                        logger.info(f"🎯 VI-FVG Confluence: {vi_direction} VI + {fvg_direction} FVG "
                                   f"@ {confluence['overlap_zone']['midpoint']:.6f}")
        
        return confluence_zones

    def _generate_vi_signals(self, imbalances: List[Dict[str, Any]], 
                            confluence_zones: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """
        Volume Imbalance'lara dayalı trading sinyalleri üretir.
        """
        signals = []
        
        # 1. Pure VI signals
        for vi in imbalances:
            if vi.get('volume_ratio', 1.0) >= 1.5:  # Yüksek volume
                signal = {
                    'type': 'VOLUME_IMBALANCE_REBALANCE',
                    'direction': 'BEARISH' if vi['direction'] == 'BULLISH' else 'BULLISH',  # Rebalance yönü ters
                    'entry_zone': vi['midpoint'],
                    'target_level': vi['gap_low'] if vi['direction'] == 'BULLISH' else vi['gap_high'],
                    'signal_strength': 'HIGH' if vi.get('is_high_volume', False) else 'MEDIUM',
                    'reasoning': f'{vi["direction"]} VI rebalance expected',
                    'vi_data': vi
                }
                signals.append(signal)
        
        # 2. Confluence signals (daha güçlü)
        for confluence in confluence_zones:
            if confluence.get('direction_match', False):
                vi_direction = confluence['vi_data']['direction']
                signal = {
                    'type': 'VI_FVG_CONFLUENCE_SIGNAL',
                    'direction': vi_direction,
                    'entry_zone': confluence['overlap_zone']['midpoint'],
                    'signal_strength': 'EXCELLENT',
                    'reasoning': f'VI-FVG confluence in {vi_direction} direction',
                    'confluence_data': confluence
                }
                signals.append(signal)
        
        return signals

    def _score_imbalances(self, imbalances: List[Dict[str, Any]], 
                         confluence_analysis: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """
        Volume Imbalance'ları kaliteye göre puanlar.
        Artık OB ve CVD teyitlerini de içerir.
        """
        scored_imbalances = []
        
        for vi in imbalances:
            vi_copy = vi.copy()
            score = 5.0  # Base score
            details = []

            # Volume factor
            if vi.get('is_high_volume'):
                score += 1.5
                details.append("High Volume")

            # Gap size factor (penalty for non-optimal)
            gap_size_pct = vi.get('gap_size_pct', 0)
            if gap_size_pct < 0.1:
                score -= 1.0
                details.append("Small Gap (<0.1%)")
            elif gap_size_pct > 1.0:
                score -= 1.0
                details.append("Large Gap (>1.0%)")

            # OB Confluence Score
            if vi.get('ob_confluence'):
                score += 2.5
                details.append("Order Block Confluence")

            # CVD Divergence (Negative Score)
            if vi.get('cvd_divergence'):
                score -= 3.0
                details.append("CVD Divergence (Negative)")

            # FVG Confluence
            has_confluence = any(c.get('vi_data', {}).get('candle_index') == vi.get('candle_index') for c in confluence_analysis)
            if has_confluence:
                score += 2.0
                details.append("FVG Confluence")

            vi_copy['quality_score'] = max(0, min(10, score))  # Normalize between 0-10
            vi_copy['score_details'] = details
            scored_imbalances.append(vi_copy)
        
        # Score'a göre sırala
        scored_imbalances.sort(key=lambda x: x['quality_score'], reverse=True)
        
        return scored_imbalances

    def _empty_result(self) -> Dict[str, Any]:
        """
        Boş analiz sonucu döner.
        """
        return {
            'detected_imbalances': [],
            'active_imbalances': [],
            'mitigated_imbalances': [],
            'fvg_confluence': [],
            'trading_signals': [],
            'summary': {
                'total_detected': 0,
                'active_count': 0,
                'bullish_vi': 0,
                'bearish_vi': 0,
                'high_quality_vi': 0,
                'confluence_count': 0
            },
            'analysis_timestamp': datetime.now().isoformat()
        }
