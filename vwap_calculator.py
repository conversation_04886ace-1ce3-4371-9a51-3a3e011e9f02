"""
VWAP (Volume Weighted Average Price) Hesaplama Modülü.
"""
import pandas as pd
from loguru import logger
from typing import Optional

def calculate_vwap(
    df: pd.DataFrame,
    anchor: str = "D",
    offset: int = 0,
    timestamp_col: str = 'timestamp',
    high_col: str = 'high',
    low_col: str = 'low',
    close_col: str = 'close',
    volume_col: str = 'volume'
) -> Optional[pd.Series]:
    """
    VWAP (Volume Weighted Average Price) hesaplar.

    Farklı periyotlara (gün, hafta, ay vb.) göre VWAP hesaplaması yapabilir.

    Args:
        df (pd.DataFrame): İşlenecek OHLCV verilerini içeren DataFrame.
            'timestamp_col', 'high_col', 'low_col', 'close_col', 'volume_col'
            ile belirtilen sütunları içermelidir.
        anchor (str): VWAP periyodu. Pandas offset alias string'leri kabul eder.
            Örnekler: 'D' (Gün), 'W' (Hafta), 'M' (Ay), 'H' (Saat).
            Varsayılan: 'D' (Gün).
        offset (int): VWAP hesaplaması için periyot kaydırma değeri.
            Genellikle kullanılmaz. Varsayılan: 0.
        timestamp_col (str): Zaman damgası sütununun adı. Varsayılan: 'timestamp'.
        high_col (str): Yüksek fiyat sütununun adı. Varsayılan: 'high'.
        low_col (str): Düşük fiyat sütununun adı. Varsayılan: 'low'.
        close_col (str): Kapanış fiyat sütununun adı. Varsayılan: 'close'.
        volume_col (str): Hacim sütununun adı. Varsayılan: 'volume'.

    Returns:
        Optional[pd.Series]: Hesaplanan VWAP değerlerini içeren Pandas Serisi.
                             Gerekli sütunlar yoksa veya hata oluşursa None dönebilir.
                             Serinin indeksi orijinal DataFrame'in indeksi ile aynıdır.
    """
    required_cols = [timestamp_col, high_col, low_col, close_col, volume_col]
    if not all(col in df.columns for col in required_cols):
        logger.error(f"VWAP hesaplaması için gerekli sütunlar eksik! Beklenenler: {required_cols}, Mevcut: {df.columns.tolist()}")
        return None

    df_copy = df.copy()

    try:
        # Zaman damgasını datetime formatına çevir
        df_copy['date'] = pd.to_datetime(df_copy[timestamp_col])

        # Anchor periyoduna göre başlangıç zamanını belirle
        # Düzenleme: anchor='D' (günlük) için .dt.date kullan
        if anchor.upper() == 'D':
             df_copy['anchor_time'] = df_copy['date'].dt.date
        else:
             # Diğer periyotlar için .dt.floor kullanmayı dene (örn. 'H', 'W', 'M')
             try:
                 df_copy['anchor_time'] = df_copy['date'].dt.floor(anchor)
             except ValueError as floor_err:
                  logger.error(f"VWAP anchor '{anchor}' için .dt.floor hatası: {floor_err}. Günlük gruplama kullanılacak.")
                  # Hata durumunda varsayılan olarak günlük kullan
                  df_copy['anchor_time'] = df_copy['date'].dt.date

        # Offset uygula (eğer gerekiyorsa, ancak genellikle anchor ile halledilir)
        if offset != 0:
            # Offset'i uygulamak karmaşık olabilir, şimdilik basit bir shift varsayımı
            # logger.warning("VWAP için offset kullanımı tam desteklenmiyor.")
            # Pratikte anchor periyodunu ayarlamak daha yaygındır.
            # Geçici olarak bu kısmı yoruma alıyorum veya basit bir shift ekleyebiliriz
            # df_copy['anchor_time'] = df_copy['anchor_time'].shift(offset) # Bu genellikle istenen sonucu vermez
            pass # Offset şimdilik ihmal ediliyor, anchor kullanılması önerilir.

        # VWAP hesapla
        df_copy['typical_price'] = (df_copy[high_col] + df_copy[low_col] + df_copy[close_col]) / 3
        df_copy['price_volume'] = df_copy['typical_price'] * df_copy[volume_col]

        # Periyot başlangıcına göre kümülatif değerleri hesapla
        df_copy['cumulative_volume'] = df_copy.groupby('anchor_time')[volume_col].cumsum()
        df_copy['cumulative_price_volume'] = df_copy.groupby('anchor_time')['price_volume'].cumsum()

        # VWAP hesapla (sıfıra bölme hatasını önle)
        df_copy['vwap'] = df_copy['cumulative_price_volume'] / df_copy['cumulative_volume'].replace(0, pd.NA) # 0 yerine NA kullanmak daha iyi olabilir

        # Eğer ilk değerler NaN ise, ileriye doğru doldur (opsiyonel, periyodun başında hacim 0 olabilir)
        # df_copy['vwap'].ffill(inplace=True)

        logger.debug(f"VWAP hesaplaması '{anchor}' periyodu ile tamamlandı.")
        return df_copy['vwap']

    except Exception as e:
        logger.error(f"VWAP hesaplaması sırasında hata oluştu: {e}", exc_info=True)
        return None 