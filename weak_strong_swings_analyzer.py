# weak_strong_swings_analyzer.py
# LuxAlgo SMC - Weak/Strong Highs & Lows Analyzer
# Piyasa yapısı kırılımlarına dayanarak <PERSON>ayıf ve Güçlü Swing Noktalarını analiz eder

import pandas as pd
import numpy as np
from loguru import logger
from typing import List, Dict, Any, Optional, Tuple
from datetime import datetime

class WeakStrongSwingsAnalyzer:
    """
    LuxAlgo SMC konseptine göre Zayıf ve Güçlü Swing Noktalarını analiz eder.
    
    ICT/SMC Konseptleri:
    - Strong High (Güçlü Tepe): Yeni bir dü<PERSON> (LL) yapmayı başaran swing tepesi
    - Weak High (Zayıf Tepe): Yeni bir dü<PERSON> (LL) yapmayı başaramayan swing tepesi
    - Strong Low (Güçlü Dip): Yeni bir tepe (HH) yapmayı başaran swing dibi
    - Weak Low (Zayıf Dip): Yeni bir tepe (HH) yapmayı başaramayan swing dibi
    
    Bu analiz, likidite hedeflerini ve trend sağlığını değerlendirmede kritik öneme sahiptir.
    """
    
    def __init__(self, lookback_period: int = 20, 
                 min_swing_distance: int = 5,
                 strength_confirmation_candles: int = 3):
        """
        Weak/Strong Swings Analyzer'ı başlatır.
        
        Args:
            lookback_period: Analiz için geriye bakış periyodu
            min_swing_distance: Swing'ler arası minimum mesafe (mum sayısı)
            strength_confirmation_candles: Güçlü/zayıf onayı için gereken mum sayısı
        """
        self.lookback_period = lookback_period
        self.min_swing_distance = min_swing_distance
        self.strength_confirmation_candles = strength_confirmation_candles
        
        logger.info(f"💪 Weak/Strong Swings Analyzer başlatıldı - "
                   f"Lookback: {lookback_period}, "
                   f"Min Distance: {min_swing_distance}, "
                   f"Confirmation: {strength_confirmation_candles}")

    def analyze(self, structure_analysis: Dict[str, Any], 
                candles: pd.DataFrame = None) -> Dict[str, Any]:
        """
        Ana weak/strong swing analizi.
        
        Args:
            structure_analysis: MarketStructureAnalyzer'dan gelen analiz sonucu
            candles: Mum verileri (opsiyonel, additional context için)
            
        Returns:
            Weak/Strong swing analiz sonuçları
        """
        if structure_analysis is None or (hasattr(structure_analysis, 'empty') and structure_analysis.empty) or (isinstance(structure_analysis, dict) and not structure_analysis):
            logger.warning("Weak/Strong swing analizi için structure_analysis verisi yok")
            return self._empty_result()
        
        # DataFrame ise dict'e çevir veya gerekli alanları al
        if hasattr(structure_analysis, 'to_dict'):
            # DataFrame case
            logger.debug("structure_analysis DataFrame formatında, dict formatına çeviriliyor")
            structure_dict = structure_analysis.to_dict() if hasattr(structure_analysis, 'to_dict') else {}
            major_pivots = structure_dict.get('major_pivots', [])
            breaks = structure_dict.get('breaks', [])
        else:
            # Dict case (normal)
            major_pivots = structure_analysis.get('major_pivots', [])
            breaks = structure_analysis.get('breaks', [])
        
        if not major_pivots or len(major_pivots) < 3:
            logger.warning(f"Weak/Strong swing analizi için yetersiz pivot ({len(major_pivots)}/3)")
            return self._empty_result()
        
        logger.info(f"💪 Weak/Strong Swings analizi başlıyor - "
                   f"{len(major_pivots)} pivot, {len(breaks)} break")
        
        # 1. Swing strength classification
        classified_swings = self._classify_swing_strengths(major_pivots, breaks)
        
        # 2. Trend continuation/reversal analysis
        trend_analysis = self._analyze_trend_implications(classified_swings)
        
        # 3. Liquidity target identification
        liquidity_targets = self._identify_liquidity_targets(classified_swings)
        
        # 4. Active weak/strong levels
        active_levels = self._identify_active_levels(classified_swings, candles)
        
        # 5. Trading signals based on weak/strong analysis
        trading_signals = self._generate_weak_strong_signals(classified_swings, trend_analysis)
        
        result = {
            'classified_swings': classified_swings,
            'trend_analysis': trend_analysis,
            'liquidity_targets': liquidity_targets,
            'active_levels': active_levels,
            'trading_signals': trading_signals,
            'summary': {
                'total_swings': len(classified_swings),
                'strong_highs': len([s for s in classified_swings if s.get('strength_type') == 'Strong High']),
                'weak_highs': len([s for s in classified_swings if s.get('strength_type') == 'Weak High']),
                'strong_lows': len([s for s in classified_swings if s.get('strength_type') == 'Strong Low']),
                'weak_lows': len([s for s in classified_swings if s.get('strength_type') == 'Weak Low']),
                'high_probability_targets': len([t for t in liquidity_targets if t.get('probability') == 'HIGH'])
            },
            'analysis_timestamp': datetime.now().isoformat()
        }
        
        logger.success(f"✅ Weak/Strong Swings analizi tamamlandı: "
                      f"Strong Highs={result['summary']['strong_highs']}, "
                      f"Weak Highs={result['summary']['weak_highs']}, "
                      f"Strong Lows={result['summary']['strong_lows']}, "
                      f"Weak Lows={result['summary']['weak_lows']}")
        
        return result

    def _classify_swing_strengths(self, major_pivots: List[Dict[str, Any]], 
                                 breaks: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """
        Swing noktalarını güçlü/zayıf olarak sınıflandırır.
        """
        classified_swings = []
        
        # Son 10 swing'i analiz et (performans için)
        recent_pivots = major_pivots[-10:] if len(major_pivots) > 10 else major_pivots
        
        for i, swing in enumerate(recent_pivots):
            swing_copy = swing.copy()
            swing_type = swing.get('type', '').lower()
            swing_price = float(swing.get('price', 0))
            swing_index = swing.get('candle_index', 0)
            
            # Default classification
            strength_type = 'Neutral'
            strength_reason = 'Insufficient data'
            
            if swing_type == 'high':
                # High için: Sonraki swing'lerde lower low yapılıp yapılmadığını kontrol et
                strength_result = self._analyze_high_strength(swing, recent_pivots[i:], breaks)
                strength_type = strength_result['type']
                strength_reason = strength_result['reason']
                
            elif swing_type == 'low':
                # Low için: Sonraki swing'lerde higher high yapılıp yapılmadığını kontrol et
                strength_result = self._analyze_low_strength(swing, recent_pivots[i:], breaks)
                strength_type = strength_result['type']
                strength_reason = strength_result['reason']
            
            swing_copy.update({
                'strength_type': strength_type,
                'strength_reason': strength_reason,
                'strength_confidence': strength_result.get('confidence', 0.5),
                'classification_timestamp': datetime.now().isoformat()
            })
            
            classified_swings.append(swing_copy)
            
            logger.debug(f"📊 Swing classified: {swing_type.upper()} @ {swing_price:.6f} -> {strength_type} ({strength_reason})")
        
        return classified_swings

    def _analyze_high_strength(self, high_swing: Dict[str, Any], 
                              subsequent_swings: List[Dict[str, Any]], 
                              breaks: List[Dict[str, Any]]) -> Dict[str, Any]:
        """
        High swing'in güçlü/zayıf olduğunu analiz eder.
        """
        high_price = float(high_swing.get('price', 0))
        high_index = high_swing.get('candle_index', 0)
        
        # Bu high'dan sonraki low'ları kontrol et
        subsequent_lows = [s for s in subsequent_swings if s.get('type') == 'low' and s.get('candle_index', 0) > high_index]
        
        if not subsequent_lows:
            return {
                'type': 'Weak High',
                'reason': 'No subsequent lows to analyze',
                'confidence': 0.3
            }
        
        # En düşük subsequent low'u bul
        lowest_subsequent = min(subsequent_lows, key=lambda x: float(x.get('price', float('inf'))))
        lowest_price = float(lowest_subsequent.get('price', float('inf')))
        
        # Bu high'dan önceki low'ları da kontrol et
        previous_lows = [s for s in subsequent_swings if s.get('type') == 'low' and s.get('candle_index', 0) < high_index]
        
        if previous_lows:
            # En yakın önceki low
            recent_previous_low = max(previous_lows, key=lambda x: x.get('candle_index', 0))
            previous_low_price = float(recent_previous_low.get('price', 0))
            
            # Lower Low yapıldı mı?
            if lowest_price < previous_low_price:
                # Bu high, lower low yapılmasını sağladıysa Strong High
                
                # Break onayı var mı?
                relevant_breaks = [b for b in breaks if b.get('price', 0) <= high_price and b.get('candle_index', 0) > high_index]
                if relevant_breaks:
                    confidence = 0.9
                    reason = 'Lower Low created with BOS confirmation'
                else:
                    confidence = 0.7
                    reason = 'Lower Low created'
                
                return {
                    'type': 'Strong High',
                    'reason': reason,
                    'confidence': confidence,
                    'subsequent_low_price': lowest_price,
                    'previous_low_price': previous_low_price
                }
            else:
                # Lower Low yapılamadıysa Weak High
                return {
                    'type': 'Weak High',
                    'reason': 'Failed to create Lower Low',
                    'confidence': 0.8,
                    'subsequent_low_price': lowest_price,
                    'previous_low_price': previous_low_price
                }
        
        # Önceki low yok, sadece subsequent'a bakabiliriz
        return {
            'type': 'Weak High',
            'reason': 'Insufficient previous context',
            'confidence': 0.4,
            'subsequent_low_price': lowest_price
        }

    def _analyze_low_strength(self, low_swing: Dict[str, Any], 
                             subsequent_swings: List[Dict[str, Any]], 
                             breaks: List[Dict[str, Any]]) -> Dict[str, Any]:
        """
        Low swing'in güçlü/zayıf olduğunu analiz eder.
        """
        low_price = float(low_swing.get('price', 0))
        low_index = low_swing.get('candle_index', 0)
        
        # Bu low'dan sonraki high'ları kontrol et
        subsequent_highs = [s for s in subsequent_swings if s.get('type') == 'high' and s.get('candle_index', 0) > low_index]
        
        if not subsequent_highs:
            return {
                'type': 'Weak Low',
                'reason': 'No subsequent highs to analyze',
                'confidence': 0.3
            }
        
        # En yüksek subsequent high'ı bul
        highest_subsequent = max(subsequent_highs, key=lambda x: float(x.get('price', 0)))
        highest_price = float(highest_subsequent.get('price', 0))
        
        # Bu low'dan önceki high'ları da kontrol et
        previous_highs = [s for s in subsequent_swings if s.get('type') == 'high' and s.get('candle_index', 0) < low_index]
        
        if previous_highs:
            # En yakın önceki high
            recent_previous_high = max(previous_highs, key=lambda x: x.get('candle_index', 0))
            previous_high_price = float(recent_previous_high.get('price', 0))
            
            # Higher High yapıldı mı?
            if highest_price > previous_high_price:
                # Bu low, higher high yapılmasını sağladıysa Strong Low
                
                # Break onayı var mı?
                relevant_breaks = [b for b in breaks if b.get('price', 0) >= low_price and b.get('candle_index', 0) > low_index]
                if relevant_breaks:
                    confidence = 0.9
                    reason = 'Higher High created with BOS confirmation'
                else:
                    confidence = 0.7
                    reason = 'Higher High created'
                
                return {
                    'type': 'Strong Low',
                    'reason': reason,
                    'confidence': confidence,
                    'subsequent_high_price': highest_price,
                    'previous_high_price': previous_high_price
                }
            else:
                # Higher High yapılamadıysa Weak Low
                return {
                    'type': 'Weak Low',
                    'reason': 'Failed to create Higher High',
                    'confidence': 0.8,
                    'subsequent_high_price': highest_price,
                    'previous_high_price': previous_high_price
                }
        
        # Önceki high yok, sadece subsequent'a bakabiliriz
        return {
            'type': 'Weak Low',
            'reason': 'Insufficient previous context',
            'confidence': 0.4,
            'subsequent_high_price': highest_price
        }

    def _analyze_trend_implications(self, classified_swings: List[Dict[str, Any]]) -> Dict[str, Any]:
        """
        Weak/Strong swing'lerin trend implications'ını analiz eder.
        """
        if len(classified_swings) < 3:
            return {
                'trend_strength': 'INSUFFICIENT_DATA',
                'trend_direction': 'UNKNOWN',
                'reversal_probability': 0.0,
                'continuation_probability': 0.0
            }
        
        recent_swings = classified_swings[-5:]  # Son 5 swing
        
        # Trend pattern analizi
        strong_highs = [s for s in recent_swings if s.get('strength_type') == 'Strong High']
        weak_highs = [s for s in recent_swings if s.get('strength_type') == 'Weak High']
        strong_lows = [s for s in recent_swings if s.get('strength_type') == 'Strong Low']
        weak_lows = [s for s in recent_swings if s.get('strength_type') == 'Weak Low']
        
        # Trend strength calculation
        strength_score = 0
        
        # Strong swings artırır, weak swings azaltır
        strength_score += len(strong_highs) * 2
        strength_score += len(strong_lows) * 2
        strength_score -= len(weak_highs) * 1
        strength_score -= len(weak_lows) * 1
        
        # Trend direction analysis
        if len(recent_swings) >= 2:
            last_swing = recent_swings[-1]
            prev_swing = recent_swings[-2]
            
            last_type = last_swing.get('type', '')
            last_strength = last_swing.get('strength_type', '')
            
            if last_type == 'high' and last_strength == 'Weak High':
                trend_direction = 'BEARISH_BIAS'
                reversal_probability = 0.75
                continuation_probability = 0.25
            elif last_type == 'low' and last_strength == 'Weak Low':
                trend_direction = 'BULLISH_BIAS'
                reversal_probability = 0.75
                continuation_probability = 0.25
            elif last_type == 'high' and last_strength == 'Strong High':
                trend_direction = 'BEARISH_CONFIRMED'
                reversal_probability = 0.25
                continuation_probability = 0.75
            elif last_type == 'low' and last_strength == 'Strong Low':
                trend_direction = 'BULLISH_CONFIRMED'
                reversal_probability = 0.25
                continuation_probability = 0.75
            else:
                trend_direction = 'NEUTRAL'
                reversal_probability = 0.5
                continuation_probability = 0.5
        else:
            trend_direction = 'UNKNOWN'
            reversal_probability = 0.5
            continuation_probability = 0.5
        
        # Trend strength classification
        if strength_score >= 5:
            trend_strength = 'VERY_STRONG'
        elif strength_score >= 3:
            trend_strength = 'STRONG'
        elif strength_score >= 1:
            trend_strength = 'MODERATE'
        elif strength_score >= -1:
            trend_strength = 'WEAK'
        else:
            trend_strength = 'VERY_WEAK'
        
        return {
            'trend_strength': trend_strength,
            'trend_direction': trend_direction,
            'strength_score': strength_score,
            'reversal_probability': reversal_probability,
            'continuation_probability': continuation_probability,
            'recent_pattern': {
                'strong_highs': len(strong_highs),
                'weak_highs': len(weak_highs),
                'strong_lows': len(strong_lows),
                'weak_lows': len(weak_lows)
            }
        }

    def _identify_liquidity_targets(self, classified_swings: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """
        Weak/Strong swing'lere dayalı likidite hedeflerini belirler.
        """
        targets = []
        
        for swing in classified_swings:
            strength_type = swing.get('strength_type', '')
            swing_price = float(swing.get('price', 0))
            swing_type = swing.get('type', '')
            confidence = swing.get('strength_confidence', 0.5)
            
            # Weak swings = Yüksek likidite hedefi
            if 'Weak' in strength_type:
                if strength_type == 'Weak High':
                    target = {
                        'type': 'LIQUIDITY_TARGET',
                        'level': swing_price,
                        'direction': 'UPSIDE',  # Weak high'ların üzerindeki likidite
                        'swing_type': swing_type,
                        'strength_type': strength_type,
                        'probability': 'HIGH' if confidence >= 0.7 else 'MEDIUM',
                        'target_reason': 'Weak High liquidity above',
                        'distance_buffer_pct': 0.2  # %0.2 buffer
                    }
                else:  # Weak Low
                    target = {
                        'type': 'LIQUIDITY_TARGET',
                        'level': swing_price,
                        'direction': 'DOWNSIDE',  # Weak low'ların altındaki likidite
                        'swing_type': swing_type,
                        'strength_type': strength_type,
                        'probability': 'HIGH' if confidence >= 0.7 else 'MEDIUM',
                        'target_reason': 'Weak Low liquidity below',
                        'distance_buffer_pct': 0.2  # %0.2 buffer
                    }
                
                targets.append(target)
                
            # Strong swings = Düşük likidite hedefi (daha çok destek/direnç)
            elif 'Strong' in strength_type:
                target = {
                    'type': 'SUPPORT_RESISTANCE',
                    'level': swing_price,
                    'direction': 'BOTH',
                    'swing_type': swing_type,
                    'strength_type': strength_type,
                    'probability': 'MEDIUM',  # Strong swings daha güvenilir S/R
                    'target_reason': f'{strength_type} support/resistance',
                    'distance_buffer_pct': 0.1  # %0.1 buffer
                }
                targets.append(target)
        
        # Probability'e göre sırala
        targets.sort(key=lambda x: {'HIGH': 3, 'MEDIUM': 2, 'LOW': 1}.get(x['probability'], 0), reverse=True)
        
        return targets

    def _identify_active_levels(self, classified_swings: List[Dict[str, Any]], 
                               candles: pd.DataFrame = None) -> List[Dict[str, Any]]:
        """
        Aktif weak/strong seviyelerini belirler.
        """
        if candles is None or candles.empty:
            logger.warning("Active levels analizi için candles verisi yok")
            return []
        
        current_price = float(candles.iloc[-1]['close'])
        active_levels = []
        
        # Güncel fiyata yakın swing'leri bul (%5 içinde)
        proximity_threshold_pct = 5.0
        
        for swing in classified_swings:
            swing_price = float(swing.get('price', 0))
            distance_pct = abs((swing_price - current_price) / current_price) * 100
            
            if distance_pct <= proximity_threshold_pct:
                active_level = swing.copy()
                active_level.update({
                    'distance_from_current': distance_pct,
                    'proximity_score': max(0, 10 - distance_pct * 2),  # 0-10 skor
                    'is_above_price': swing_price > current_price,
                    'activation_status': 'ACTIVE'
                })
                active_levels.append(active_level)
        
        # Proximity score'a göre sırala
        active_levels.sort(key=lambda x: x['proximity_score'], reverse=True)
        
        return active_levels

    def _generate_weak_strong_signals(self, classified_swings: List[Dict[str, Any]], 
                                     trend_analysis: Dict[str, Any]) -> List[Dict[str, Any]]:
        """
        Weak/Strong swing analizine dayalı trading sinyalleri üretir.
        """
        signals = []
        
        if len(classified_swings) < 2:
            return signals
        
        last_swing = classified_swings[-1]
        trend_direction = trend_analysis.get('trend_direction', 'UNKNOWN')
        reversal_probability = trend_analysis.get('reversal_probability', 0.0)
        
        # Son swing'e göre sinyal üret
        last_strength = last_swing.get('strength_type', '')
        last_type = last_swing.get('type', '')
        last_price = float(last_swing.get('price', 0))
        
        # Weak High sonrası Bearish signals
        if last_strength == 'Weak High' and reversal_probability >= 0.6:
            signal = {
                'type': 'WEAK_HIGH_REVERSAL',
                'direction': 'BEARISH',
                'entry_zone': last_price,
                'signal_strength': 'HIGH' if reversal_probability >= 0.75 else 'MEDIUM',
                'reasoning': 'Weak High suggests bearish reversal',
                'confidence': reversal_probability,
                'swing_data': last_swing
            }
            signals.append(signal)
        
        # Weak Low sonrası Bullish signals
        elif last_strength == 'Weak Low' and reversal_probability >= 0.6:
            signal = {
                'type': 'WEAK_LOW_REVERSAL',
                'direction': 'BULLISH',
                'entry_zone': last_price,
                'signal_strength': 'HIGH' if reversal_probability >= 0.75 else 'MEDIUM',
                'reasoning': 'Weak Low suggests bullish reversal',
                'confidence': reversal_probability,
                'swing_data': last_swing
            }
            signals.append(signal)
        
        # Strong swing continuation signals
        elif 'Strong' in last_strength and trend_analysis.get('continuation_probability', 0) >= 0.6:
            if last_strength == 'Strong High':
                signal = {
                    'type': 'STRONG_HIGH_CONTINUATION',
                    'direction': 'BEARISH',
                    'entry_zone': last_price,
                    'signal_strength': 'MEDIUM',
                    'reasoning': 'Strong High confirms bearish continuation',
                    'confidence': trend_analysis.get('continuation_probability', 0),
                    'swing_data': last_swing
                }
            else:  # Strong Low
                signal = {
                    'type': 'STRONG_LOW_CONTINUATION', 
                    'direction': 'BULLISH',
                    'entry_zone': last_price,
                    'signal_strength': 'MEDIUM',
                    'reasoning': 'Strong Low confirms bullish continuation',
                    'confidence': trend_analysis.get('continuation_probability', 0),
                    'swing_data': last_swing
                }
            signals.append(signal)
        
        return signals

    def _empty_result(self) -> Dict[str, Any]:
        """
        Boş analiz sonucu döner.
        """
        return {
            'classified_swings': [],
            'trend_analysis': {
                'trend_strength': 'INSUFFICIENT_DATA',
                'trend_direction': 'UNKNOWN',
                'reversal_probability': 0.0,
                'continuation_probability': 0.0
            },
            'liquidity_targets': [],
            'active_levels': [],
            'trading_signals': [],
            'summary': {
                'total_swings': 0,
                'strong_highs': 0,
                'weak_highs': 0,
                'strong_lows': 0,
                'weak_lows': 0,
                'high_probability_targets': 0
            },
            'analysis_timestamp': datetime.now().isoformat()
        }
